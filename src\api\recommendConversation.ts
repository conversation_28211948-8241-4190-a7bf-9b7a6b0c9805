import { http } from '@/network';
import {
  IApiRecommendConversationBatchConfirm,
  IApiRecommendConversationBatchConfirmParams,
  IApiRecommendConversationConfirmParams,
  IApiRecommendConversationFeedbackParams,
  IApiRecommendConversationLatest,
  IApiRecommendConversationLatestContent,
  IApiRecommendConversationLatestContentParams,
  IApiRecommendConversationLatestParams,
  IApiRecommendConversationQueryPage,
  IApiRecommendConversationQueryPageParams,
} from '@/interface/type';

// ------------------Ai------------------

/** 分页查询推荐 */
export function getRecommendConversation(
  params: IApiRecommendConversationQueryPageParams
) {
  return http.post<IApiRecommendConversationQueryPage>({
    url: '/api/recommend/conversation/query/page',
    data: params,
  });
}

/** 确认使用/忽略 推荐 */
export function confirmRespond(params: IApiRecommendConversationConfirmParams) {
  return http.post({
    url: '/api/recommend/conversation/confirm',
    data: params,
  });
}

/** 反馈推荐 */
export function updateFeedback(
  params: IApiRecommendConversationFeedbackParams
) {
  return http.post({
    url: '/api/recommend/conversation/feedback',
    data: params,
  });
}

/** 最近一次推荐 */
export function getLatestRecommend(
  params: IApiRecommendConversationLatestParams
) {
  return http.post<IApiRecommendConversationLatest>({
    url: '/api/recommend/conversation/latest',
    data: params,
  });
}

/** 最近一次推荐对话 */
export function getLatestConversation(
  params: IApiRecommendConversationLatestContentParams
) {
  return http.post<IApiRecommendConversationLatestContent>({
    url: '/api/recommend/conversation/latest/content',
    data: params,
  });
}

/** 批量更新患者推荐 */
export function updateConversation(
  params: IApiRecommendConversationBatchConfirmParams
) {
  return http.post<IApiRecommendConversationBatchConfirm>({
    url: '/api/recommend/conversation/batch/confirm',
    data: params,
  });
}
