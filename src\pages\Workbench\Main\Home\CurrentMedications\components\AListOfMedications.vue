<template>
  <div :class="{ 'stop-status': props.showStopStatus }">
    <div
      v-if="props.showStopStatus"
      class="status-stop flex justify-center items-center"
    >
      <div class="text w-120">停</div>
    </div>
    <div class="drug-list">
      <el-table
        :data="tableDatas"
        style="width: 100%"
        header-row-class-name="head-class"
      >
        <el-table-column
          prop="drugName"
          label="药品名称"
          align="left"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div>
              <span>{{ scope.$index + 1 }}.</span>
              <span>{{ scope.row.drugName }} </span>
            </div>
            <!--            <div v-if="scope.row.commonName">-->
            <!--              <span>{{ scope.$index + 1 }}.</span>-->
            <!--              <span>{{ scope.row.commonName }}</span>-->
            <!--              <span>({{ scope.row.drugName }}) </span>-->
            <!--            </div>-->
            <!--            <div v-else>-->
            <!--              <span>{{ scope.$index + 1 }}.</span>-->
            <!--              <span>{{ scope.row.drugName }} </span>-->
            <!--            </div>-->
          </template>
        </el-table-column>
        <el-table-column prop="drugUsage" align="center" label="频次" />
        <el-table-column prop="drugAmount" align="center" label="单量">
          <template #default="scope">
            {{ drugAmountText(scope.row.drugAmount) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div
      v-if="props.tableData.length > 5"
      class="more-btn flex justify-center py-16"
    >
      <el-button link class="link" @click="showMoreList = !showMoreList">
        {{ showMoreList ? '收起' : '展开全部' }}
        <el-icon class="icon-arrow ml-7">
          <i-ep-d-arrow-left v-if="showMoreList" />
          <i-ep-d-arrow-right v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface IProps {
  showStopStatus?: boolean;
  tableData?: any;
}
const props = defineProps<IProps>();
const showMoreList = ref(false);
const tableDatas = computed(() => {
  let arr = showMoreList.value ? props.tableData : props.tableData?.slice(0, 5);
  return arr;
});
const drugAmountText = obj => {
  let text = '';
  if (obj.custom) {
    text = obj.custom;
  } else {
    text = obj.ingredients + ' ' + obj.contentUnit;
  }
  return text;
};
</script>
<style scoped lang="less">
.more-btn {
  .link {
    font-size: 14px;
    color: #2e6be6;
    display: flex;
    align-items: center;
    &:hover {
      color: #2e6be6;
      opacity: 0.75;
      cursor: pointer;
    }
    &:active {
      color: #2e6be6 !important;
      opacity: 1;
    }
    .icon-arrow {
      transform: rotate(90deg);
    }
  }
}
:deep(.drug-list) {
  margin-top: 8px;
  .el-table__inner-wrapper::before {
    background: none;
  }
  .el-table__header-wrapper {
    margin-bottom: 6px;
  }
  .head-class {
    background: #f7f8fa;

    .el-table__cell {
      background-color: #f7f8fa !important;
      padding: 6px 0;
      .cell {
        font-size: 14px;
        font-weight: normal !important;
        color: #708293;
        min-width: 60px;
        height: 20px;
      }
    }
  }
  .el-table__body-wrapper {
    .el-table__body {
      .el-table__cell {
        font-size: 14px;
        border: none;
        padding: 6px 0;
        .cell {
          color: #203549;
          line-height: 20px;
        }
      }
    }
  }
  .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
    background-color: transparent;
  }
  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    border-bottom: none;
  }
}
.stop-status {
  position: relative;
  :deep(.el-table__cell) .cell {
    color: #b8becc !important;
  }
  .link {
    color: #b8becc !important;
  }
  .status-stop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    .text {
      border-radius: 4px;
      border: 3px solid #15233f;
      font-weight: bold;
      font-size: 32px;
      color: #3a4762;
      line-height: 38px;
      height: 46px;
      text-align: center;
      //margin: 80px 0 0 235px;
      transform: rotate(-30deg);
    }
    z-index: 999;
  }
}
</style>
