import { IExaminationItem } from '@/hooks/useExaminationList';
import {
  IApiIndexQueryResponseDTOList,
  IApiIndexSubordinateQueryItem,
} from '@/interface/type';

// 公共指标数据
interface IComIndic {
  id: string | number;
  name: string;
}
// 指标分析当前选择指标
export type IAnaActItem = IApiIndexQueryResponseDTOList & IComIndic;
// 指标分析指标下级
export type IAnaSubIndicItem = IApiIndexSubordinateQueryItem & IComIndic;
// 报告原文当前选择指标
export type IRawActItem = IExaminationItem & IComIndic;
