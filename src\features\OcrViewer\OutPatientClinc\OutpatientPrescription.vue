<template>
  <div>
    <PatientRecordHead
      v-if="!locatedPatientHistoryId"
      @get-record-params="getRecordParams"
    />
    <SingleDynamicForm
      ref="outpatientPrescriptionRef"
      v-model="formData"
      class="mt-12"
      mode="edit"
      :category="FormCategory.OUTPATIENT_PRESCRIPTION"
      hidden-file-upload
      block-label
      :exclude-validate-keys="['admission_date']"
      :extra-data="extraParams"
    />
  </div>
</template>

<script setup lang="ts">
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';
import PatientRecordHead from './PatientRecordHead.vue';
import { useOcrScan } from '@/store/module/useOcrScan';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';
import globalBus from '@/lib/bus';
import { FormCategory, VALIDATION_PASSED } from '@/constant';

const outpatientPrescriptionRef = shallowRef();
const locatedPatientHistoryId = inject('locatedPatientHistoryId');
const saveOcrStore = useSaveOcrInfo();
const ocrStore = useOcrScan();
const formData = ref({});
const recordParams = ref<any>({});
const getRecordParams = (value: {
  patientHistoryId: null | number;
  date: string;
  saveType: 0 | 1;
}) => {
  recordParams.value = value;
  saveOcrStore.updateCurrentDataTimeAndId(value);
};

const extraParams = computed(() => {
  return {
    source_type: 0,
    source_id: locatedPatientHistoryId
      ? locatedPatientHistoryId
      : recordParams.value.patientHistoryId,
    medication_start_time: recordParams.value.date ?? null,
    medication_accessory: [ocrStore.globalImgInfo.currentImgUrl.url],
    ...saveOcrStore.getExtraParams(),
  };
});
const saveData = async businessCallEnabled => {
  if (!recordParams.value.date && !locatedPatientHistoryId) {
    ElMessage.warning('请选择门诊时间！');
    return;
  }
  return outpatientPrescriptionRef.value
    ?.submit(businessCallEnabled)
    ?.then(res => {
      if (res !== undefined && res !== VALIDATION_PASSED) {
        globalBus.emit('close-ocr-dialog');
        globalBus.emit('saved-ocr-info');
        saveOcrStore.showConfirmDialog = false;
        if (locatedPatientHistoryId || recordParams.value.patientHistoryId) {
          let id = locatedPatientHistoryId
            ? locatedPatientHistoryId
            : recordParams.value.patientHistoryId;

          globalBus.emit('refresh-attachment', id);
          globalBus.emit('refresh-record-data', id);
        } else {
          globalBus.emit('updata-review-list');
        }
        ElMessage.success('保存成功！');
        return true;
      }
      return res;
    });
};
const setFormData = (outData = null) => {
  const data = ocrStore.aiResultData[FormCategory.OUTPATIENT_PRESCRIPTION];
  const finalData = outData ? outData : data;
  formData.value = finalData;
};
const getSaveData = () => {
  return {
    data: { ...formData.value },
    source_type: extraParams.value.source_type,
    key: FormCategory.OUTPATIENT_PRESCRIPTION,
    excludeKeys: ['medication_start_time'],
  };
};
watch(
  () => ocrStore.aiResultData,
  () => {
    setFormData();
  },
  { deep: true }
);
defineExpose({
  saveData,
  setFormData,
  getSaveData,
});
</script>

<style scoped lang="less"></style>
