import useTabs from './module/useTabs';
import useGlobal from './module/useGlobal';
import useUserList from './module/useUserList';
import useUserStore from './module/useUserStore';
import useIM from './module/useIM';
import useMessage from './module/useMessage';
import useComponentsTabAction from './module/useComponentsTabAction';
import useAIConversation from './module/useAIConversation';
import useInternDrawer from './module/useInternDrawer';
import useIndicatorInfo from './module/useIndicatorInfo';
export default {
  useTabs,
  useGlobal,
  useUserList,
  useUserStore,
  useIM,
  useMessage,
  useComponentsTabAction,
  useAIConversation,
  useInternDrawer,
  useIndicatorInfo,
};
