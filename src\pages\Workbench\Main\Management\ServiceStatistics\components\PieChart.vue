<template>
  <div class="pie flex items-center">
    <div ref="main" class="echarts"></div>
    <div class="index">
      <div
        v-for="(item, index) in props.data"
        :key="index"
        class="item-index flex items-center mb-6"
      >
        <div
          class="flag w-8 h-8 mr-8"
          :style="{ background: color[index] }"
        ></div>
        <div class="title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts';

let main = ref(null);
let series = ref<any>([]);
let color = ref([
  '#2E6BE6',
  '#11A0AD',
  '#2FC25B',
  '#FACC14',
  '#F04864',
  '#8543E0',
]);
let init = () => {
  let myChart = echarts.init(main.value);
  let option;

  option = {
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: series.value,
        labelLine: {
          show: false,
        },
        label: {
          normal: {
            show: true,
          },
        },
      },
    ],
  };

  option && myChart.setOption(option);
};

interface ItemType {
  name: string;
}
const props = defineProps({
  data: {
    type: Array as PropType<ItemType[]>,
    default: () => [],
  },
});

watch(
  () => props.data,
  () => {
    handle();
  },
  { deep: true }
);

onMounted(() => {
  handle();
});

let handle = () => {
  if (main.value && props.data.length) {
    series.value = props.data.map((item: any, index) => {
      return {
        value: item.value,
        itemStyle: { color: color.value[index] },
      };
    });
    init();
  }
};
</script>
<style scoped lang="less">
.pie {
  display: flex;
  justify-content: space-around;
  .echarts {
    height: 200px;
    width: 200px;
  }
  .index {
    .item-index {
      .flag {
        border-radius: 50%;
      }
      .title {
        font-size: 12px;
        color: #3a4762;
      }
    }
  }
}
</style>
