<template>
  <div>
    <div class="title mb-16">通话详情</div>
    <div class="module flex items-center flex-wrap">
      <div
        v-for="(item, index) in detailsList"
        :key="index"
        class="item-box flex items-center mb-16"
      >
        <div class="title mr-16">
          {{ item.title }}
        </div>
        <div class="content">
          {{ item.value }}
        </div>
      </div>
    </div>
    <div class="title mb-16">呼叫坐席</div>
    <div class="module flex items-center flex-wrap">
      <div
        v-for="(item, index) in banquetList"
        :key="index"
        class="item-box flex items-center mb-16"
      >
        <div class="title mr-16">
          {{ item.title }}
        </div>
        <div
          class="content flex"
          :class="{ 'items-center': item.value === '处理成功' }"
        >
          <div v-if="item.value === '处理成功'" class="flag-box mr-4"></div>
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  detailsInfo: {
    default: () => ({}),
    type: Object,
  },
  banquetInfo: {
    default: () => ({}),
    type: Object,
  },
});

let banquetList = ref([
  {
    title: '坐席名称',
    type: 'clientName',
    value: '',
  },
  {
    title: '坐席工号',
    type: 'cno',
    value: '',
  },
  {
    title: '呼叫类型',
    type: 'callType',
    value: '',
  },
  {
    title: '呼叫结果',
    type: 'sipCause',
    value: '',
  },
  {
    title: '呼叫情况',
    type: 'status',
    value: '',
  },
  {
    title: '主叫记忆',
    type: 'remember',
    value: '',
  },
  {
    title: '开始时间',
    type: 'startTime',
    value: '',
  },
  {
    title: '接通时间',
    type: 'answerTime',
    value: '',
  },
  {
    title: '通话时长',
    type: 'totalDuration',
    value: '',
  },
]);
let detailsList = ref([
  {
    title: '用户电话',
    type: 'customerNumber',
    value: '',
  },
  {
    title: '热线/外显号码',
    type: 'hotline',
    value: '',
  },
  {
    title: '呼叫类型',
    type: 'callType',
    value: '',
  },
  {
    title: '接听状态',
    type: 'status',
    value: '',
  },
  {
    title: '通话时长',
    type: 'totalDuration',
    value: '',
  },
  {
    title: '开始时间',
    type: 'startTime',
    value: '',
  },
  {
    title: '接通时间',
    type: 'bridgeTime',
    value: '',
  },
  {
    title: '结束时间',
    type: 'endTime',
    value: '',
  },
  {
    title: '队列号',
    type: 'qno',
    value: '',
  },
  {
    title: '坐席工号',
    type: 'cno',
    value: '',
  },
  {
    title: '坐席电话',
    type: 'clientNumber',
    value: '',
  },
]);

// 处理通话详情数据
detailsList.value.forEach(item => {
  for (let key in props.detailsInfo) {
    let ite = props.detailsInfo[key];
    if (key === item.type) item.value = ite || '--';
  }
});
// 处理呼叫坐席
banquetList.value.forEach(item => {
  for (let key in props.banquetInfo) {
    let ite = props.banquetInfo[key];
    if (key === item.type) item.value = ite || '--';
  }
});
</script>
<style scoped lang="less">
.title {
  font-size: 14px;
  font-weight: bold;
  color: #101b25;
}
.module {
  width: 100%;
  .item-box {
    width: 50%;
    .title {
      font-size: 14px;
      color: #708293;
      width: 56px;
    }
    .content {
      font-size: 14px;
      color: #101b25;
      .flag-box {
        width: 8px;
        height: 8px;
        background: #28c445;
        border-radius: 50%;
      }
    }
  }
}
</style>
