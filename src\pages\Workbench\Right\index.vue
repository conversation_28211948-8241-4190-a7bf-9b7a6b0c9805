<template>
  <div class="w-[540px] bg-slate-100">
    <PatientTodo :key="globalStore.rerenderKey" />
    <AddressList :key="globalStore.rerenderKey" />
    <Chat />
  </div>
</template>

<script setup lang="ts">
import PatientTodo from './components/PatientTodo/index.vue';
import AddressList from './components/AddressList/index.vue';
import Chat from './components/Chat/index.vue';
import store from '@/store';

const globalStore = store.useGlobal();
defineOptions({
  name: 'WorkBenchRight',
});
</script>
<style scoped lang="less">
// todo
</style>
