<template>
  <div class="normal-subform-wrap">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="flex flex-wrap p-12"
    >
      <div v-for="subItem in item" :key="subItem?.key" class="item">
        <span class="label">{{ subItem?.value }}:</span>
        <div v-if="subItem?.uiMethod === 'subform'" class="sub-item-warp">
          <template v-for="(sub, i) in subItem.items[0]" :key="sub.key">
            <span v-if="mode === 'view'">
              {{ normaFormData[subItem.key]?.[sub.key] ?? '--' }}
            </span>
            <InputNumber
              v-else
              :mode="mode"
              :max="sub.uiRules?.max"
              :min="sub.uiRules?.min"
              :step="sub.uiRules?.step"
              :precision="sub.uiRules?.precision"
              :placeholder="sub.value"
              :value="normaFormData[subItem?.key]?.[sub.key]"
              @change="val => handleChange(`${subItem.key}.${sub.key}`, val)"
            />
            <span v-if="i !== subItem.items[0].length - 1" class="my-0 mx-12">
              {{ subItem?.uiOptions?.seperate }}
            </span>
            <span v-if="sub.unit" class="unit">{{ sub.unit }}</span>
          </template>
        </div>
        <div v-else>
          <span v-if="mode === 'view'">
            {{ normaFormData[subItem.key] ?? '--' }}
          </span>
          <InputNumber
            v-else
            :mode="mode"
            :max="subItem?.uiRules?.max"
            :min="subItem?.uiRules?.min"
            :step="subItem?.uiRules?.step"
            :precision="subItem?.uiRules?.precision"
            :placeholder="subItem.name"
            :value="normaFormData[subItem.key]"
            :control="subItem.ui_options?.control"
            @change="val => handleChange(subItem.key, val)"
          />
        </div>
        <span class="unit">{{ subItem.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, set } from 'lodash-es';
import InputNumber from './InputNumber.vue';
import { Base } from './type';
interface IProps extends Base {
  items?: any[];
}
const props = defineProps<IProps>();
const normaFormData = ref({});
const emit = defineEmits(['change']);

watch(
  () => props.value,
  val => {
    normaFormData.value = cloneDeep(val || {});
  },
  { immediate: true, deep: true }
);

const handleChange = (key, val) => {
  set(normaFormData.value, key, val);
  emit('change', cloneDeep(normaFormData.value));
};

defineOptions({
  name: 'NormalSubForm',
});
</script>

<style scoped lang="less">
.item {
  display: flex;
  align-items: center;
  > span {
    display: inline-block;
    min-width: 40px;
  }
  .label {
    margin-right: 12px;
  }
  .unit {
    margin-left: 12px;
  }
}
</style>
