<template>
  <div class="common-sty">
    <div class="font-bold">检查项</div>
    <div v-if="info?.length" class="content">
      <el-scrollbar>
        <div class="flex mt-2xs">
          <div
            v-for="(item, index) in info"
            :key="index"
            class="cursor-pointer mr-lg pb-3xs item-change"
            :class="{ active: index === active }"
            @click="changeIndexChecked(item, index)"
          >
            {{ item.reportName }}
          </div>
        </div>
      </el-scrollbar>
      <table v-if="dataList?.length" class="inspection-table mt-2xs w-full">
        <thead>
          <tr>
            <th>结论</th>
            <th>时间</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in dataList" :key="index">
            <td style="width: 50%">
              {{ item.title }}
            </td>
            <td style="width: 50%">{{ item.time }}</td>
          </tr>
        </tbody>
      </table>
      <div v-else class="text-center py-xl">暂无数据</div>
    </div>
    <div v-else class="text-center py-3xs">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { queryStructuredEcgDetailApi } from '@/api/managementSituation';
import dayjs from 'dayjs';
import { PropType } from 'vue';
import useGlobal from '@/store/module/useGlobal';

defineProps({
  info: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});
const useGlobalInfo = useGlobal();

let active = ref(-1);
let changeIndexChecked = (item: { indexTermId: number }, index: number) => {
  active.value = index;
  getDataDetails(item.indexTermId);
};

interface dataInfo {
  title: string;
  time: string;
}
let dataList = ref<dataInfo[]>([]);
// 获取心电图详情数据
let getDataDetails = (indexTermId: number) => {
  let form = {
    patientId: useGlobalInfo.userId,
    indexTermId,
  };
  queryStructuredEcgDetailApi(form).then((res: any) => {
    let arr: dataInfo[] = [];
    if (res.data && res.data.length) {
      res.data.forEach(item => {
        let title = '';
        item.conclusions.forEach((ite, index) => {
          let flag = index < item.conclusions.length - 1 ? '；' : '';
          title += ite.name + flag;
        });
        arr.push({
          time:
            (item.checkTime && dayjs(item.checkTime).format('YYYY-MM-DD')) ||
            '--',
          title: title || '--',
        });
      });
    }
    dataList.value = arr;
  });
};
</script>

<style scoped lang="less">
.content {
  :deep(.el-scrollbar__wrap) {
    border-bottom: 1px solid #e9e8eb;
  }
  .item-change {
    color: #333;
    white-space: nowrap;

    &.active {
      color: #1255e2;
      border-bottom: 2px solid #1255e2;
    }
  }
}
.inspection-table {
  border-spacing: 0;
  thead {
    padding: 12px 32px;
    background: #f7f7fb;
    tr {
      font-weight: bold;
      th {
        padding: 12px 0 12px 32px;
        margin: 0;
      }
    }
  }
  tbody {
    tr {
      background-color: #fff;
      td {
        padding: 12px 0 12px 32px;
        margin: 0;
      }
    }
    tr:nth-child(even) {
      background-color: #f7f7fb;
    }
  }
}
</style>
