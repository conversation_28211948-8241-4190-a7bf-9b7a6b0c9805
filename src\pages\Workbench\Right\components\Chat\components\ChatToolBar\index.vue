<template>
  <div class="relative h-48 flex items-center pl-16">
    <input
      ref="imageRef"
      hidden
      accept="image/*"
      multiple
      type="file"
      @change="fileChange('image', $event)"
    />
    <input
      ref="videoRef"
      hidden
      accept="video/*"
      multiple
      type="file"
      @change="fileChange('video', $event)"
    />
    <el-tooltip content="发送图片" effect="light" placement="top">
      <div class="item" @click="imageRef.click()">
        <img :src="sendImg" />
      </div>
    </el-tooltip>
    <el-tooltip content="发送视频" effect="light" placement="top">
      <div class="item" @click="videoRef.click()"><img :src="sendVideo" /></div>
    </el-tooltip>
    <el-tooltip content="发送表情" effect="light" placement="top">
      <div class="item" @click.stop="showEmote = true">
        <img :src="sendEmote" />
      </div>
    </el-tooltip>
    <el-tooltip content="聊天记录" effect="light" placement="top">
      <div class="item" @click.stop="showRecord = true">
        <img :src="record" />
      </div>
    </el-tooltip>
    <el-tooltip content="用药助手" effect="light" placement="top">
      <div class="item" @click.stop="showMedicalAssistant = true">
        <img :src="medicate" />
      </div>
    </el-tooltip>
    <el-tooltip content="常用语" effect="light" placement="top">
      <div class="item" @click.stop="$props.showExpression">
        <img :src="expression" />
      </div>
    </el-tooltip>
    <el-tooltip content="问题答复" effect="light" placement="top">
      <div class="item" @click.stop="props.showQuestionAnswer">
        <img :src="questionAnswer" />
      </div>
    </el-tooltip>
    <Emote v-if="showEmote" v-bind="attrs" />
    <MedicalAssistant
      :medicated-assistant-visible="showMedicalAssistant"
      @close="showMedicalAssistant = false"
    />
    <ChatRecord
      :visible="showRecord"
      :team-id="imStore.curTeamId"
      @close="showRecord = false"
    />
  </div>
</template>

<script setup lang="ts">
import sendImg from '@/assets/imgs/chat/sendImg.png';
import sendVideo from '@/assets/imgs/chat/sendVideo.png';
import sendEmote from '@/assets/imgs/chat/sendEmote.png';
import medicate from '@/assets/imgs/chat/medicate.png';
import record from '@/assets/imgs/chat/record.png';
import expression from '@/assets/imgs/chat/usefulExpressions.png';
import questionAnswer from '@/assets/imgs/chat/questionAnswer.png';
import store from '@/store';
import Emote from './components/Emote/index.vue';
import MedicalAssistant from './components/MedicalAssistant/index.vue';
import ChatRecord from './components/ChatRecord/index.vue';
import { useEventListener } from '@vueuse/core';

interface IProps {
  showQuestionAnswer: () => void;
  showExpression: () => void;
}
const imStore = store.useIM();
const imageRef = shallowRef();
const videoRef = shallowRef();
const showEmote = ref(false);
const showMedicalAssistant = ref(false);
const showRecord = ref(false);
const props = defineProps<IProps>();
const attrs = useAttrs() as any;
defineOptions({
  name: 'ChatToolbar',
});
const fileChange = (type: string, e: Event) => {
  const files: any = (e.target as HTMLInputElement).files ?? [];
  imStore.loading = true;
  for (const file of files) {
    imStore.sendBlobMsg({
      to: imStore.curTeamId,
      type,
      blob: file,
    });
  }
};
onMounted(() => {
  useEventListener(document, 'click', () => {
    showEmote.value = false;
  });
});
defineExpose({
  setEmoteStatus: (val: boolean) => {
    showEmote.value = val;
  },
});
</script>

<style scoped lang="less">
.item {
  cursor: pointer;
  display: flex;
  text-align: center;
  align-items: center;
  height: 20px;
  user-select: none;
  &:not(:last-child) {
    margin-right: 24px;
  }
  img {
    width: 18px;
  }
}
</style>
