import { http } from '@/network';
import {
  IApiIndexAbnormalDistributionQuery,
  IApiIndexAbnormalDistributionQueryParams,
  IApiIndexContentQuery,
  IApiIndexContentQueryParams,
  IApiIndexDataDeleteParams,
  IApiIndexEvenDistributionQuery,
  IApiIndexEvenDistributionQueryParams,
  IApiIndexFromQueryParams,
  IApiIndexLineChartQuery,
  IApiIndexLineChartQueryParams,
  IApiIndexQuery,
  IApiIndexSubordinateQuery,
  IApiIndexSubordinateQueryParams,
  IApiIndexThresholdInfoQuery,
  IApiIndexThresholdInfoQueryParams,
  IApiIndexUpdateThresholdParams,
  IApiIndexWatchMonitorParams,
  IApiPatientReportBloodDetail,
  IApiPatientReportBloodDetailParams,
  IApiPatientReportListParams,
  IApiPatientReportPicturesParams,
  IApiPatientReportPictures,
  IApiCaseHistoryHeartPictureParams,
  IApiCaseHistoryHeartPicture,
} from '@/interface/type';

interface IPatientId {
  patientId: number | string;
}

// ------------------报告原文------------------

/** 全部报告项查询 */
export function getReportListAll(params?: { nonAiReport: boolean }) {
  return http.post({
    url: '/api/case/history/medicine/examination/key/list',
    data: params,
  });
}

/** 报告列表查询 */
export function getReportTableList(params: IApiPatientReportListParams) {
  return http.post({
    url: '/api/patient/report/list',
    data: params,
  });
}

/** 删除报告 */
export function removeReport(params: { reportId: number }) {
  return http.post({
    url: '/api/patient/report/remove',
    data: params,
  });
}

// ------------------指标分析------------------

/** 查询患者存在的指标项 */
export function getIndexQuery(params: IPatientId) {
  return http.post<IApiIndexQuery>({
    url: '/api/index/query',
    data: params,
  });
}

/** 数据新增 */
export function updateIndex(params: any) {
  return http.post({
    url: '/api/index/data/modify',
    data: params,
  });
}

/** 调整阈值 */
export function updateThreshold(params: IApiIndexUpdateThresholdParams) {
  return http.post({
    url: '/api/index/update/threshold',
    data: params,
  });
}

/** 调整阈值 */
export function getThreshold(params: IApiIndexThresholdInfoQueryParams) {
  return http.post<IApiIndexThresholdInfoQuery>({
    url: '/api/index/threshold/info/query',
    data: params,
  });
}

/** 指标分析表格查询 */
export function getIndexFromQuery(params: IApiIndexFromQueryParams) {
  return http.post({
    url: '/api/index/from/query',
    data: params,
  });
}

/** 查询指标下级 */
export function getIndexSubordinateQuery(
  params: IApiIndexSubordinateQueryParams
) {
  return http.post<IApiIndexSubordinateQuery>({
    url: '/api/index/subordinate/query',
    data: params,
  });
}

/** 查询文案数据 */
export function getIndexContentQuery(params: IApiIndexContentQueryParams) {
  return http.post<IApiIndexContentQuery>({
    url: '/api/index/content/query',
    data: params,
  });
}

/** 删除数据 */
export function removeIndex(params: IApiIndexDataDeleteParams) {
  return http.post({
    url: '/api/index/data/delete',
    data: params,
  });
}

/** 折线图查询 */
export function getLineChartQuery(params: IApiIndexLineChartQueryParams) {
  return http.post<Required<IApiIndexLineChartQuery>>({
    url: '/api/index/line/chart/query',
    data: params,
  });
}

/** 平均分布查询 */
export function getEvenDistributionQuery(
  params: IApiIndexEvenDistributionQueryParams
) {
  return http.post<Required<IApiIndexEvenDistributionQuery>>({
    url: '/api/index/even/distribution/query',
    data: params,
  });
}

/** 异常分布查询 */
export function getAbnormalDistributionQuery(
  params: IApiIndexAbnormalDistributionQueryParams
) {
  return http.post<Required<IApiIndexAbnormalDistributionQuery>>({
    url: '/api/index/abnormal/distribution/query',
    data: params,
  });
}

/** 动态监测血压 */
export function addWatchMonitor(params: IApiIndexWatchMonitorParams) {
  return http.post({
    url: '/api/index/watch/monitor',
    data: params,
  });
}

/** 动态血压详情查询 */
export function getReportBloodDetail(
  params: IApiPatientReportBloodDetailParams
) {
  return http.post<IApiPatientReportBloodDetail>({
    url: '/api/patient/report/blood/detail',
    data: params,
  });
}

/**获取图片档案 */
export function getReportPictures(params: IApiPatientReportPicturesParams) {
  return http.post<IApiPatientReportPictures>({
    url: '/api/patient/report/pictures',
    data: params,
  });
}
/**获取心血管画像数据 */

export function getPciHeartData(params: IApiCaseHistoryHeartPictureParams) {
  return http.post<IApiCaseHistoryHeartPicture>({
    url: '/api/case/history/heart/picture',
    data: params,
  });
}
