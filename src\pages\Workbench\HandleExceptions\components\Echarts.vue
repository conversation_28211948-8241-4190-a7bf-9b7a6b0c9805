<template>
  <div class="echarts mt-16">
    <div class="unit ml-12">{{ indexInfo.nuit }}</div>
    <div id="main" :key="Math.random()" ref="main" class="main"></div>
  </div>
</template>
<script setup lang="ts">
import * as echarts from 'echarts';

const props = defineProps({
  indexInfo: {
    type: Object,
    default: () => {},
  },
});
let { indexInfo } = props;

let ydata = ref<any>([]);
let main = ref(null);

watch(
  () => indexInfo,
  () => {
    setTimeout(() => {
      handleData();
    }, 500);
  },
  {
    deep: true,
    immediate: true,
  }
);

let init = () => {
  let myChart = echarts.init(main.value);
  let option: any;

  option = {
    tooltip: {
      trigger: 'axis',
    },
    dataZoom: [
      {
        show: true,
        realtime: true,
        start: 30,
        end: 70,
        xAxisIndex: [0, 1],
      },
      {
        type: 'inside',
        realtime: true,
        start: 30,
        end: 70,
        xAxisIndex: [0, 1],
      },
    ],
    grid: {
      left: '2%',
      right: '5%',
      bottom: '18%',
      top: '5%',
      containLabel: true,
    },
    toolbox: {
      feature: {},
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: indexInfo.xData,
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          // 分割线的样式
          color: '#D7DCEA',
          type: 'dashed',
        },
      },
    },
    series: ydata.value,
  };

  option && myChart.setOption(option);
};

onMounted(() => {
  setTimeout(() => {
    handleData();
  });
});

// 数据处理
let handleData = () => {
  if (main.value) {
    ydata.value = [];
    indexInfo.yData.forEach(item => {
      ydata.value.push({
        type: 'line',
        data: item,
        symbol: 'none',
      });
    });

    main.value.removeAttribute('_echarts_instance_');
    init();
  }
};
</script>
<style scoped lang="less">
.echarts {
  .unit {
    font-size: 12px;
    color: #7a8599;
  }
  .main {
    height: 296px;
  }
}
</style>
