import { PieECOption } from '../type';
import { PieSeriesOption } from 'echarts/charts';
export const getPieEchartsOptions = (config: any): PieECOption => {
  const {
    title = {},
    tooltip = { trigger: 'item' },
    legend,
    seriesConfig = [],
  } = config;

  return {
    title,
    tooltip,
    legend,
    series: generatePieSeries(seriesConfig),
  };
};

export const generatePieSeries = (
  series: Array<PieSeriesOption>
): PieECOption['series'] => {
  if (!series?.length) return [];
  return series.map(item => {
    return {
      type: 'pie',
      radius: ['40%', '70%'],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      startAngle: 0,
      minShowLabelAngle: 0,
      ...item,
    };
  }) as PieECOption['series'];
};
