import { defineConfig } from 'vitepress';
import { vitepressDemoPlugin } from 'vitepress-demo-plugin';
import path from 'path';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: '医患管理体系',
  description: '医患管理体系业务组件文档',
  head: [
    ['link', { rel: 'icon', type: 'image/png', href: '/logo.png' }],
    ['script', { src: '//unpkg.com/@element-plus/icons-vue' }],
  ],
  markdown: {
    config(md) {
      md.use(vitepressDemoPlugin, {
        demoDir: path.resolve(__dirname, '../demos'),
        lightTheme: 'github-light',
        darkTheme: 'github-dark',
      });
    },
  },
  vite: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '../../src'),
      },
    },
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    plugins: [
      AutoImport({
        eslintrc: {
          enabled: false,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({
            prefix: 'Icon',
          }),
        ],
        imports: ['vue', 'vue-router'],
      }),
      Components({
        dirs: [],
        resolvers: [
          ElementPlusResolver(),
          IconsResolver({
            enabledCollections: ['ep'],
          }),
        ],
      }),
      Icons({
        autoInstall: true,
      }),
    ],
  },
  locales: {},
  themeConfig: {
    // https://vitepress.dev/zh/reference/default-theme-config
    logo: '/logo.png',
    outline: {
      label: '页面导航',
    },
    nav: [
      { text: 'Home', link: '/' },
      { text: 'Examples', link: '/markdown-examples' },
    ],
    sidebar: [
      {
        text: 'Examples',
        items: [
          { text: 'Markdown Examples', link: '/markdown-examples' },
          { text: 'Runtime API Examples', link: '/api-examples' },
        ],
      },
      {
        text: 'Form 表单组件',
        items: [
          { text: 'Select 选择器', link: '/components/select' },
          { text: 'InputNumber 数字输入框', link: '/components/input-number' },
          { text: 'TableForm 表格表单', link: '/components/table-form' },
          { text: 'SingleDynamicForm 单项动态表单', link: '/components/single-dynamic-form' },
        ],
      },
    ],
    socialLinks: [
      {
        icon: 'gitlab',
        link: 'https://gitlab.hrttest.cn/frontend/web/workstation-web',
      },
    ],
  },
});
