import { http } from '@/network';
import {
  IApiResearchDoubtInfoParams,
  IApiResearchDoubtInfo,
  IApiResearchDoubtReceiveParams,
  IApiResearchDoubtReceive,
  IApiResearchDoubtReplyParams,
  IApiResearchDoubtReply,
  IApiResearchDoubtRejectParams,
  IApiResearchDoubtReject,
  IApiResearchDoubtSubmitParams,
  IApiResearchDoubtSubmit,
  IApiResearchDoubtWithdrawParams,
  IApiResearchDoubtWithdraw,
  IApiResearchDoubtAnotherParams,
  IApiResearchDoubtAnother,
  IApiResearchDoubtLocationParams,
  IApiResearchDoubtLocation,
  IApiResearchDoubtSnapshotCompareParams,
  IApiResearchDoubtSnapshotCompare,
} from '@/interface/type';

/** 获取质疑信息 */
export const getResearchDoubtInfo = (data: IApiResearchDoubtInfoParams) => {
  return http.post<IApiResearchDoubtInfo>({
    url: '/api/research/doubt/info',
    data,
  });
};

/** 接受质疑 */
export const recieveDoubt = (data: IApiResearchDoubtReceiveParams) => {
  return http.post<IApiResearchDoubtReceive>({
    url: '/api/research/doubt/receive',
    data,
  });
};

/** 质疑情况说明提交 */
export const doubtReply = (data: IApiResearchDoubtReplyParams) => {
  return http.post<IApiResearchDoubtReply>({
    url: '/api/research/doubt/reply',
    data,
  });
};

/** 拒绝质疑 */
export const rejectDoubt = (data: IApiResearchDoubtRejectParams) => {
  return http.post<IApiResearchDoubtReject>({
    url: '/api/research/doubt/reject',
    data,
  });
};

/** 定向提交 */
export const doubtSubmit = (data: IApiResearchDoubtSubmitParams) => {
  return http.post<IApiResearchDoubtSubmit>({
    url: '/api/research/doubt/submit',
    data,
  });
};

/** 撤回质疑 */
export const doubtWithdraw = (data: IApiResearchDoubtWithdrawParams) => {
  return http.post<IApiResearchDoubtWithdraw>({
    url: '/api/research/doubt/withdraw',
    data,
  });
};

/** 获取上一条或者下一条质疑 */
export const getAnotherDoubt = (data: IApiResearchDoubtAnotherParams) => {
  return http.post<IApiResearchDoubtAnother>({
    url: '/api/research/doubt/another',
    data,
  });
};

/** 获取质疑定位信息 */
export const getDoubtLocation = (data: IApiResearchDoubtLocationParams) => {
  return http.post<IApiResearchDoubtLocation>({
    url: '/api/research/doubt/location',
    data,
  });
};

/** 获取质疑快照 */
export const getDoubtSnapshot = (
  data: IApiResearchDoubtSnapshotCompareParams
) => {
  return http.post<IApiResearchDoubtSnapshotCompare>({
    url: '/api/research/doubt/snapshot/compare',
    data,
  });
};
