export interface IDiseaseData {
  diseaseIds?: number[];
}
export interface IDiseaseItem {
  diseaseId: number;
  diseaseName: string;
  pId: number | null;
  children: IDiseaseItem[];
  originChildren?: IDiseaseItem[];
  chooseType: 'title' | 'radio' | 'checkbox';
  level?: number;
  isLeaf?: boolean;
}
export type IDiseaseList = IDiseaseItem[];
export type IDiseaseMap = Record<number, IDiseaseItem>;
export interface IProps {
  diseaseList: IDiseaseList;
  width?: string;
  dialogWidth?: string;
  diseaseData: IDiseaseData;
  enableFilter?: boolean;
  title?: string;
}
export interface ICheckListProps extends Omit<IProps, 'width' | 'dialogWidth'> {
  dataMap: IDiseaseMap;
  num: number;
  itemLayout?: 'block' | 'inline';
}
export interface ITreeNodeProps {
  selectedIds: number[];
  isFirst?: boolean;
  level?: number;
  nodeData: IDiseaseItem;
  searchWords: string;
  dataMap: IDiseaseMap;
  itemLayout?: ICheckListProps['itemLayout'];
}
export interface IPathItem {
  id: number;
  text: string;
}
export interface IShowNameList {
  ids: number[];
  selectedPath: IPathItem[][];
  showNameList: IPathItem[];
  resultTreeData?: IDiseaseList;
}
