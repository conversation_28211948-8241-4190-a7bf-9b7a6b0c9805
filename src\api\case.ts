import { http } from '@/network';

/**
 * 获取病例JSON Schema
 * @param cards 需要展示的模块列表
 */
export function getCaseSchema(cards: string[]): Promise<Record<string, any>> {
  return http.post({
    url: '/api/case/schema',
    data: { cards },
  });
}

/**
 * 获取病例数据
 * @param params 查询参数
 * @param url 接口地址
 */
export async function getCaseData({
  params,
  url,
}: {
  params: Record<string, any>;
  url?: string;
}): Promise<Record<string, any>> {
  return http.get({
    url: url || '/api/case/data',
    params,
  });
}

/**
 * 保存病例数据
 * @param sourceId 病例ID，新建时为空
 * @param data 病例数据
 * @param mode 模式：create/edit
 */
export async function saveCaseData({
  sourceId,
  data,
  url,
}: {
  sourceId?: number;
  data: Record<string, any> & {
    /** 病例类型 */
    source_type: number;
    /** 病例ID */
    source_id: string;
    /** 是否为ocr传参 */
    ocr?: boolean;
    /** 是否为实习生端传参 */
    entry_task?: boolean;
    /** 子任务ID */
    sub_task_id?: number;
    /** 日期 */
    date?: string;
  };
  url?: string;
}): Promise<number> {
  return http.post({
    url: url || '/api/case/save',
    method: 'post',
    data: {
      ...(data || {}),
      source_id: sourceId || undefined,
      patient_history_id: sourceId || undefined,
    },
  });
}
