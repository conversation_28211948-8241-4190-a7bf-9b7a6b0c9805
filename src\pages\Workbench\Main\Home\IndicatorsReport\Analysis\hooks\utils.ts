import dayjs from 'dayjs';
import {
  IApiIndexAbnormalDistributionQueryIndexDataDTOList,
  IApiIndexEvenDistributionQuery,
  IApiIndexLineChartQuery,
} from '@/interface/type';
import { getLineEchartsOptions } from '@/components/BaseChart/options/line';
import { BarECOption, LineECOption } from '@/components/BaseChart';
import { getBMI } from '@/utils';
import { getBarEchartsOptions } from '@/components/BaseChart/options/bar';

// 开启 dataZoom 数据量
const dataZoomLimit = 50;

export const formatTime = (time?: string | number) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

export const getEchartsLineConfig = (
  data: Required<IApiIndexLineChartQuery>,
  checkType: string,
  indexType?: string
): LineECOption => {
  const {
    unit,
    dataList,
    baseLineDto: {
      maxLowPressureLine,
      minLowPressureLine,
      maxHighPressureLine,
      minHighPressureLine,
      maxHeartLine,
      minHeartLine,
      maxFatBloodLine,
      minFatBloodLine,
    },
  } = data;
  const xAxisData = dataList?.map(item =>
    formatTime(item.recordingTime).replace(' ', '\n')
  ) as string[];
  const dataZoom = { enable: xAxisData.length >= dataZoomLimit };
  const getCurrentData = (key: string) =>
    dataList.map(item => item[key as keyof typeof item]) as Array<
      number | string
    >;

  let mapKey = ['44', '45', '10', '11', '12', '46'].includes(String(checkType))
    ? checkType
    : '-1';

  const combinationKey = indexType ? mapKey + '-' + indexType : '';
  if (['8-2', '11-1', '12-1'].includes(combinationKey)) mapKey = combinationKey;
  const seriesMap: Record<string, any[]> = {
    '44': [
      {
        name: '收缩压',
        color: '#E58B48',
        markLineData: [
          {
            yAxis: maxHighPressureLine ? Number(maxHighPressureLine) : 0,
            name: `收缩压超高(${maxHighPressureLine || 0})`,
          },
          {
            yAxis: minHighPressureLine ? Number(minHighPressureLine) : 0,
            name: `收缩压超低(${minHighPressureLine || 0})`,
          },
        ],
        data: getCurrentData('highPressure') ?? [],
      },
      {
        name: '舒张压',
        color: '#0A73E4',
        markLineData: [
          {
            yAxis: maxLowPressureLine ? Number(maxLowPressureLine) : 0,
            name: `舒张压超高(${maxLowPressureLine || 0})`,
          },
          {
            yAxis: minLowPressureLine ? Number(minLowPressureLine) : 0,
            name: `舒张压超低(${minLowPressureLine || 0})`,
          },
        ],
        data: getCurrentData('lowPressure') ?? [],
      },
    ],
    '10': [
      {
        name: '随机',
        color: '#11A0AD',
        data: getCurrentData('giu') ?? [],
      },
      {
        name: '餐后2小时',
        color: '#8A3FD4',
        data: getCurrentData('nonFastingGiu') ?? [],
      },
      {
        name: '空腹',
        color: '#363687',
        data: getCurrentData('fastingGiu') ?? [],
      },
    ],
    '45': [
      {
        name: '心率',
        markLineData: [
          {
            yAxis: maxHeartLine ? Number(maxHeartLine) : 0,
            name: `心率超高(${maxHeartLine || 0})`,
          },
          {
            yAxis: minHeartLine ? Number(minHeartLine) : 0,
            name: `心率超低(${minHeartLine || 0})`,
          },
        ],
        data: getCurrentData('heartRate') ?? [],
      },
    ],
    '46': [
      {
        name: 'BMI',
        data:
          dataList.map(item => {
            const { height, weight } = item;
            if (!height || !weight) return '';
            return getBMI(height, weight);
          }) ?? [],
      },
    ],
    '11-1': [
      {
        name: '差值',
        data:
          dataList.map(item => {
            return item.diff;
          }) ?? [],
      },
    ],
    '12-1': [
      {
        name: '射血分数',
        data:
          dataList.map(item => {
            return item.content;
          }) ?? [],
      },
    ],
    '8-2': [
      {
        name: '血脂',
        markLineData: [
          {
            yAxis: maxFatBloodLine ? Number(maxFatBloodLine) : 0,
            name: `上限(${maxFatBloodLine || 0})`,
          },
          {
            yAxis: minFatBloodLine ? Number(minFatBloodLine) : 0,
            name: `下限(${minFatBloodLine || 0})`,
          },
        ],
        data: getCurrentData('indexValue') ?? [],
      },
    ],
    '-1': [
      {
        data: getCurrentData('indexValue') ?? [],
      },
    ],
  };

  return getLineEchartsOptions({
    title: {
      subtext: unit ? `单位：${unit}` : undefined,
    },
    grid: {
      right: 120,
    },
    xAxisData,
    dataZoom,
    seriesConfig: seriesMap[mapKey],
  });
};

export const getEvenEchartsLineConfig = (
  data: Omit<Required<IApiIndexEvenDistributionQuery>, 'contentResponseDTO'>,
  checkType: string
): LineECOption => {
  const {
    indexDataDTOList,
    baseLineResponseDTO: {
      maxLowPressureLine,
      minLowPressureLine,
      maxHighPressureLine,
      minHighPressureLine,
      minHeartLine,
      maxHeartLine,
    },
  } = data;
  const xAxisData = indexDataDTOList?.map(item => item.key) as string[];
  const dataZoom = { enable: xAxisData.length >= dataZoomLimit, height: 20 };
  const getCurrentData = (key: string) =>
    indexDataDTOList.map(item => item[key as keyof typeof item]) as Array<
      number | string
    >;

  const seriesMap: Record<string, any[]> = {
    '44': [
      {
        name: '收缩压',
        color: '#E58B48',
        markLineData: [
          {
            yAxis: maxHighPressureLine ? Number(maxHighPressureLine) : 0,
            name: `收缩压超高(${maxHighPressureLine || 0})`,
          },
          {
            yAxis: minHighPressureLine ? Number(minHighPressureLine) : 0,
            name: `收缩压超低(${minHighPressureLine || 0})`,
          },
        ],
        data: getCurrentData('highValue') ?? [],
      },
      {
        name: '舒张压',
        color: '#0A73E4',
        markLineData: [
          {
            yAxis: maxLowPressureLine ? Number(maxLowPressureLine) : 0,
            name: `舒张压超高(${maxLowPressureLine || 0})`,
          },
          {
            yAxis: minLowPressureLine ? Number(minLowPressureLine) : 0,
            name: `舒张压超低(${minLowPressureLine || 0})`,
          },
        ],
        data: getCurrentData('lowValue') ?? [],
      },
    ],
    '45': [
      {
        name: '心率',
        markLineData: [
          {
            yAxis: maxHeartLine ? Number(maxHeartLine) : 0,
            name: `心率超高(${maxHeartLine || 0})`,
          },
          {
            yAxis: minHeartLine ? Number(minHeartLine) : 0,
            name: `心率超低(${minHeartLine || 0})`,
          },
        ],
        data: getCurrentData('heartRate') ?? [],
      },
    ],
  };
  return getLineEchartsOptions({
    grid: {
      right: 120,
    },
    xAxisData,
    dataZoom,
    seriesConfig: seriesMap[String(checkType)],
  });
};

export const getAbnormalEchartsLineConfig = (
  data: IApiIndexAbnormalDistributionQueryIndexDataDTOList[]
): BarECOption => {
  const xAxisData = data?.map(item => item.date) as string[];
  const dataZoom = { enable: xAxisData.length >= dataZoomLimit, height: 20 };

  return getBarEchartsOptions({
    grid: {
      top: 18,
      right: 20,
      bottom: 40,
    },
    xAxisData,
    dataZoom,
    seriesConfig: [
      {
        barWidth: '50%',
        data: data?.map(item => item.errorNum) || [],
      },
    ],
  });
};
