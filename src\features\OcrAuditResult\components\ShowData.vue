<template>
  <div class="h-636">
    <OccurrenceOfIllness
      :cards="getKeysByValue(sourceType)"
      mode="view"
      readonly
      :is-batch="false"
      :resource-id="props.sourceId"
      :source-type="sourceType"
      :query-params="queryParams"
      :nav-prefix="navPrefix"
    />
  </div>
</template>
<script setup lang="ts">
import { OccurrenceOfIllness } from '@/features';
import { SourceTypeValues } from '@/constant';
import { getKeysByValue } from '../hooks';

const props = defineProps({
  sourceId: { type: Number, default: 0 },
  sourceType: { type: Number as () => SourceTypeValues, default: 0 },
  queryParams: { type: Object, default: () => {} },
  navPrefix: { type: String, default: '' },
});
</script>
