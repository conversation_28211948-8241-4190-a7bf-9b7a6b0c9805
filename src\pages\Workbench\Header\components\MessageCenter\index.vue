<template>
  <Transition name="fade">
    <div v-if="dialogVisible" ref="messageRef" class="message-center">
      <div class="title">
        <span>消息中心</span>
        <el-icon @click="closeHandler">
          <Close style="color: #7a8599" />
        </el-icon>
      </div>
      <div v-loading="loading">
        <div class="btn-group">
          <div class="select-box">
            <el-select
              v-model="messageTypes"
              style="width: 150px"
              :teleported="false"
              @change="changeMessageType"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="flex">
            <div class="btn mr-8" @click="refresh">
              <el-icon :size="14">
                <i-ep-Refresh />
              </el-icon>
              刷新
            </div>
            <div class="btn" @click="clearAll">清除</div>
          </div>
        </div>
        <LoadMore :height="600" @load="loadmoreHandler">
          <div class="list">
            <template v-if="data.length">
              <div
                v-for="item in data"
                :key="item.content"
                class="message-item"
              >
                <Card
                  :data="item"
                  :config="CardMap[item.msgType]"
                  @click-card="() => clickHanlder(item.id)"
                />
              </div>
            </template>
            <template v-else>
              <div class="empty">暂无消息</div>
            </template>
          </div>
        </LoadMore>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core';
import { Close } from '@element-plus/icons-vue';
import { clearMessage } from '@/api/message';
import { getMessageList } from '@/api/message';
import LoadMore from '@/pages/Workbench/Left/components/LoadMore/index.vue';
import Card from '../MessageCard/index.vue';
import { CardMap } from '../Notification/config';
import bus from '@/lib/bus';
import { debounce } from 'lodash-es';
import { messageTypesMap } from '@/constant/message';

interface IProps {
  visible: boolean;
  resetNum: () => void;
}
defineOptions({
  name: 'Notification',
});

const props = defineProps<IProps>();
const emits = defineEmits(['close']);

const messageRef = shallowRef();
const dialogVisible = ref(false);
const data = ref<any[]>([]);
const pageNumber = ref(1);
const total = ref(105);
const loading = ref(false);

const closeHandler = () => {
  emits('close');
};
const clearAll = async () => {
  loading.value = true;

  await clearMessage({ messageTypes: messageTypesMap[messageTypes.value] });
  props.resetNum();
  loading.value = false;
  pageNumber.value = 1;
  data.value = [];
};
const clickHanlder = (id: string) => {
  const index = data.value.findIndex(v => v.id === id);
  data.value.splice(index, 1);
  // props.resetNum();
  closeHandler();
};
const loadmoreHandler = () => {
  if (data.value.length >= total.value) return;
  pageNumber.value += 1;
  getMessageListHandler();
};
const filterMsg = (data: any) => {
  const result = data.filter(v => !v.sysMsg);
  return result;
};
const getMessageListHandler = async () => {
  loading.value = true;
  const params = {
    pageNumber: pageNumber.value,
    pageSize: 10,
    messageTypes: messageTypesMap[messageTypes.value],
  };
  try {
    const res = await getMessageList(params);
    total.value = res.totals ?? 0;
    if (pageNumber.value === 1) {
      data.value = filterMsg(res.response);
    } else {
      data.value.push(...filterMsg(res.response));
    }
  } catch (error) {}
  loading.value = false;
};

const debounceGetList = debounce(() => {
  pageNumber.value = 1;
  getMessageListHandler();
}, 100);

const refresh = () => {
  pageNumber.value = 1;
  getMessageListHandler();
};
const messageTypes = ref(0);

const options = reactive([
  {
    label: '全部',
    value: 0,
  },
  {
    label: '聊天消息',
    value: 1,
  },
  {
    label: '患者动态',
    value: 2,
  },
  {
    label: '风险提醒',
    value: 3,
  },
  {
    label: '进度通知',
    value: 4,
  },
]);

const changeMessageType = () => {
  pageNumber.value = 1;
  getMessageListHandler();
};

onMounted(() => {
  onClickOutside(messageRef, e => {
    if (!(e as any)?.target?.classList?.contains('ms-trigger')) {
      closeHandler();
    }
  });
  bus.on('notification-message', () => {
    if (!props.visible) {
      debounceGetList();
    }
  });
  bus.on('refresh-message', () => {
    if (!props.visible) {
      debounceGetList();
    }
    props.resetNum();
  });
});
onUnmounted(() => {
  bus.off('refresh-message');
  bus.off('notification-message');
});
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
    if (val) {
      messageTypes.value = 0;
      getMessageListHandler();
    } else {
      loading.value = false;
      pageNumber.value = 1;
      data.value = [];
    }
  }
);
</script>

<style scoped lang="less">
.message-center {
  font-family:
    PingFangSC,
    PingFang SC;
  position: fixed;
  width: 320px;
  background: #fff;
  border-radius: 6px;
  height: 600px;
  overflow-y: auto;
  right: 0;
  top: 60px;
  z-index: 3000;
  color: #101b25;
  box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    height: 20px;
    font-weight: bold;
    padding: 16px 16px 12px;
    height: 48px;
    border-bottom: 1px solid #e9e8eb;
    :deep(.el-icon) {
      cursor: pointer;
    }
  }
  .btn-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    .btn {
      height: 42px;
      padding: 14px 0;
      text-align: right;
      font-size: 14px;
      color: #2e6be6;
      cursor: pointer;
    }
  }

  :deep(.el-scrollbar) {
    height: 500px;
  }
}
.list {
  padding: 0 16px;
}
.message-item {
  width: 288px;
  height: 70px;
  background: #f7f8fa;
  border-radius: 4px;
  margin-bottom: 8px;
}
.empty {
  color: #7a8599;
  margin-top: 100px;
  text-align: center;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateX(288px);
}
.select-box {
  :deep(.el-popper.is-pure) {
    inset: 36px auto auto 0px !important;
  }
  :deep(.el-select__popper) {
    height: 200px;
  }
}
</style>
