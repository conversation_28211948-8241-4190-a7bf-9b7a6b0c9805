<template>
  <div>
    <div class="base flex items-center">
      <div class="title">
        姓名：<span>{{ info.patientName }}</span>
      </div>
      <el-divider direction="vertical" />
      <div class="title">
        性别：<span>{{
          info.gender === 1 ? '男' : info.gender === 2 ? '女' : '--'
        }}</span>
      </div>
      <el-divider direction="vertical" />
      <div class="title">
        年龄：<span>{{ info.age || '--' }}</span>
      </div>
      <el-divider direction="vertical" />
      <div class="title">
        手术时间：<span>{{ info.operationTime }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  baseMsg: {
    type: Object,
    default: () => {},
  },
});
interface Info {
  patientName: string;
  gender: number;
  age: string;
  operationTime: string;
}
let info = ref<Info>({
  patientName: '--',
  gender: 0,
  age: '--',
  operationTime: '--',
});

watch(
  () => props.baseMsg,
  newValue => {
    info.value = newValue as Info;
  },
  {
    deep: true,
  }
);
</script>
<style scoped lang="less">
.base {
  .title {
    font-size: 14px;
    color: #7a8599;
    span {
      color: #3a4762;
    }
  }
  :deep(.el-divider) {
    margin: 0 24px;
  }
}
</style>
