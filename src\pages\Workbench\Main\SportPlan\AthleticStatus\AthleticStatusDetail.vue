<template>
  <div v-if="firstResult" class="curr-result-box">
    <div class="evaluation-recommendations p-16 flex">
      <div class="flex-1">
        评估结论：
        <span class="two">{{ firstResult.conclusion || '--' }}</span>
      </div>
      <div class="flex-1">
        <div class="til flex">
          <div>
            评估建议：
            <span class="one">{{ firstResult.suggestion || '--' }}</span>
          </div>
        </div>
        <div class="result">{{ firstResultDesc(firstResult) }}</div>
      </div>
    </div>
    <div class="date flex items-center justify-between">
      <div>
        <span class="mr-60">
          评估时间：{{
            firstResult.evaluationTime
              ? dayjs(firstResult.evaluationTime).format('YYYY-MM-DD')
              : '--'
          }}
        </span>
        <span>评估人：{{ firstResult.rehabName || '--' }}</span>
      </div>
      <el-button
        v-if="globalData.manageStatus != 4"
        type="primary"
        @click="emit('update:readOnly', false)"
      >
        变更
      </el-button>
    </div>
  </div>
  <CardWrapper title="评估详情" class="athletic-status-wrapper">
    <AthleticStatusEdit
      v-if="firstResult"
      :read-only="readOnly"
      :edit-info="firstResult"
    />
  </CardWrapper>
  <ChangeHistory :history-list="historyList" :type="1" />
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import CardWrapper from '@/components/CardWrapper/index.vue';
import AthleticStatusEdit from './AthleticStatusEdit.vue';
import ChangeHistory from '../components/ChangeHistory.vue';
import store from '@/store';

interface IProps {
  readOnly?: boolean | false;
  historyList?: any;
}
const props = defineProps<IProps>();
const globalData = store.useGlobal();

const emit = defineEmits(['update:readOnly']);

const firstResult = computed(() => {
  return props.historyList[0];
});
const firstResultDesc = computed(() => {
  return function (val) {
    let textArr = [];
    if (
      val.absoluteContraindication &&
      JSON.parse(val.relativeContraindication).length
    ) {
      textArr.push(
        `${JSON.parse(val.absoluteContraindication).length}项绝对禁忌症`
      );
    }
    if (
      val.relativeContraindication &&
      JSON.parse(val.relativeContraindication).length
    ) {
      textArr.push(
        `${JSON.parse(val.relativeContraindication).length}项相对禁忌症`
      );
    }
    return textArr.length > 0 ? `患者存在${textArr.join('；')}` : '';
  };
});
onMounted(() => {});
</script>
<script lang="ts">
export default {
  name: 'AthleticStatusDetail',
};
</script>
<style scoped lang="less">
.curr-result-box {
  //background: #fff;
  //border-radius: 6px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 6px;
  margin-bottom: 8px;

  .evaluation-recommendations {
    width: 100%;
    min-height: 78px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(186, 200, 212, 0.5);
    border-radius: 0 0 6px 6px;
    box-sizing: border-box;
    font-size: 16px;
    span.one {
      color: #e63746;
      font-weight: bold;
    }
    span.two {
      color: #e37221;
      font-weight: bold;
    }
    .result {
      font-size: 14px;
      color: #7a8599;
    }
  }
  .date {
    height: 52px;
    border-top: 1px solid #e9e8eb;
    margin: 0 16px;
    font-size: 14px;
    color: #7a8599;
  }
}
.athletic-status-wrapper {
  margin-bottom: 8px;
  .line {
    border-bottom: 1px solid #e9e8eb;
  }
  :deep(.edit-box) {
    padding: 0 16px;
    .label-til {
      padding: 16px 0 12px 0;
      color: #101b25;
      font-weight: bold;
      > span {
        color: #e63746;
        font-weight: normal;
        margin-left: 8px;
      }
    }
    .el-checkbox-group {
      .el-checkbox {
        height: 20px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
      }
      .el-checkbox__input {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__label {
        padding-left: 8px;
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        left: 5px;
        border-width: 2px;
      }
    }
    .el-textarea__inner {
      width: 100%;
      min-height: 64px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #b8becc;
      box-shadow: none;
      color: #3a4762;
      &::placeholder {
        color: #c8c9cc;
      }
    }
  }
  .evaluation-recommendations {
    width: 100%;
    min-height: 78px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(186, 200, 212, 0.5);
    border-radius: 0 0 6px 6px;
    box-sizing: border-box;
    .til {
      color: #101b25;

      span.one {
        color: #e63746;
      }
      span.two {
        color: #e37221;
      }
    }
    .result {
      color: #7a8599;
    }
  }
}
</style>
<style lang="less">
.athletic-status-wrapper {
  > div {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
}
.el-tabs__content {
}
.tips-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .desc {
    padding: 12px 25px 0 50px;
    color: #7a8599;
  }
  .btn-box {
    border: none;
    padding-top: 0;
  }
}
</style>
