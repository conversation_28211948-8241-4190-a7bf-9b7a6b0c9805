<template>
  <el-drawer
    append-to-body
    :model-value="props.drawer"
    :size="540"
    :destroy-on-close="true"
    :modal-class="'bind-drawer'"
    v-bind="attrs"
    :show-close="false"
    :modal="false"
  >
    <template #header>
      <div class="header">
        <div
          class="go-back w-66 h-32 flex justify-center items-center cursor-pointer"
          @click="close"
        >
          <el-icon :size="14">
            <i-ep-arrow-left />
          </el-icon>
          返回
        </div>
        <div class="flex items-center justify-between mt-16">
          <span class="bind-title">绑定记录</span>
          <el-icon :size="16" class="cursor-pointer" @click="close">
            <i-ep-close />
          </el-icon>
        </div>
      </div>
    </template>
    <template #default>
      <div class="header-middle">
        <el-checkbox-group v-model="checkList" @change="changeEquipmentType">
          <el-checkbox class="look-me" label="血压计" value="BPG,HP" />
          <el-checkbox class="look-me" label="体重秤" value="WS" />
          <el-checkbox class="look-me" label="智能手表" value="WATCH" />
        </el-checkbox-group>
      </div>
      <ul class="list-box">
        <li v-for="item in bindList" :key="item.id" class="item mb-16">
          <div class="item-title flex items-center justify-between">
            <div class="title-left flex items-center">
              <img
                v-if="item.deviceType == 'BPG' || item.deviceType == 'HP'"
                class="w-16 h-16 mr-8"
                src="@/assets/imgs/overview/icon-blood.png"
                alt=""
              />
              <img
                v-else-if="item.deviceType == 'WATCH'"
                class="w-16 h-16 mr-8"
                src="@/assets/imgs/overview/icon-watch.png"
                alt=""
              />
              <img
                v-else
                class="w-16 h-16 mr-8"
                src="@/assets/imgs/overview/icon-ws.png"
                alt=""
              />
              {{
                item.deviceType == 'HP'
                  ? '掌护血压计'
                  : item.deviceType == 'BPG'
                    ? '台式血压计'
                    : item.deviceType == 'WATCH'
                      ? '智能手表'
                      : '体重秤'
              }}
            </div>
            <div
              :class="{
                'aitive-one': item.recallStatus == 'RECALL',
                'aitive-two': item.recallStatus == 'RETENTION',
                'aitive-three': item.recallStatus == 'NOT_RECALL',
              }"
            >
              {{
                item.recallStatus == 'RECALL'
                  ? '已召回'
                  : item.recallStatus == 'RETENTION'
                    ? '患者自留'
                    : '未召回'
              }}
            </div>
          </div>
          <div class="item-number mt-8">
            {{
              item.deviceType == 'HP'
                ? 'IMEI'
                : item.deviceType == 'BPG'
                  ? 'SN'
                  : item.deviceType == 'WATCH'
                    ? 'IMEI'
                    : 'MAC'
            }}
            ：{{ item.deviceNo }}
          </div>
          <div class="item-list">
            <div
              v-for="(ite, index) in item.recordList"
              :key="index"
              class="item-data mt-12 flex"
            >
              <div class="isBind">
                设备{{ ite.operationType === 'BIND' ? '绑定' : '解绑' }}
              </div>
              <span class="w-162" style="white-space: nowrap">
                时间：{{ formatDate(ite.operationTime) }}
              </span>
              <span>
                操作人：
                <template v-if="!(ite.operatorType === -10)">
                  {{
                    ite.operatorType === 0
                      ? '未知'
                      : ite.operatorType === -1
                        ? '患者'
                        : ite.operatorType === 1
                          ? '健康顾问'
                          : ite.operatorType === 20
                            ? '后台解绑'
                            : '医生'
                  }}（
                </template>
                {{ ite.operatorName }}
                <template v-if="!(ite.operatorType === -10)">）</template>
              </span>
            </div>
          </div>
        </li>
      </ul>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { getDevicetListApi } from '@/api/overview';
import useGlobal from '@/store/module/useGlobal';
import { formatDate } from '@/utils/index';
const attrs = useAttrs();
const global = useGlobal();

const props = defineProps({
  drawer: {
    default: false,
    type: Boolean,
  },
});
watch(
  () => props.drawer,
  val => {
    if (val) {
      getRecordList();
    }
  },
  { immediate: true }
);
const emit = defineEmits(['closeDrawer']);
const close = () => {
  checkList.value = [];
  emit('closeDrawer');
};

onMounted(() => {
  getRecordList();
});

let checkList = ref<Array<string>>([]);
const changeEquipmentType = () => {
  getRecordList();
};
// 获取记录
const getRecordList = () => {
  let params = {
    patientId: global.userId,
    deviceType: checkList.value
      .join(',')
      .split(',')
      .filter(it => it),
  };
  getDevicetListApi(params)
    .then((res: any) => {
      res = res.sort((a, b) => {
        return b.operationTime - a.operationTime;
      });
      const mergedData = {};
      res.forEach(item => {
        if (!mergedData[item.deviceNo]) {
          // 如果还没有这个deviceNo的条目，‌直接添加
          mergedData[item.deviceNo] = { ...item, recordList: [] };
        }
        // 添加或更新操作类型和时间
        mergedData[item.deviceNo].recordList.push(item);
      });

      // 将合并后的对象转换为数组
      const result = Object.values(mergedData);

      bindList.value = result;
    })
    .catch(() => {});
};

let bindList = ref<any>([]);
</script>
<style lang="less">
.bind-drawer {
  height: initial;
  inset: 60px 0 0 calc(100% - 540px) !important;
  .header {
    margin-bottom: 0;
    padding: 12px 0;
    .bind-title {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
    .go-back {
      background: #ffffff;
      border-radius: 2px;
      color: #3a4762;
      border: 1px solid #dcdee0;
    }
  }
  .el-drawer__header {
    margin-bottom: 0;
    border-bottom: 1px solid #e9e8eb;
  }
  .el-drawer__body {
    padding: 0;
    .header-middle {
      padding: 16px;
      .look-me {
        .el-checkbox__inner {
          width: 16px;
          height: 16px;
        }
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #3a4762;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
          background: #2e6be6;
          border-color: #2e6be6;
        }
        .el-checkbox__inner:after {
          left: 5px;
          top: 2px;
        }
      }
    }
  }
  .list-box {
    padding: 0 16px;
    .item {
      background: #f7f8fa;
      border-radius: 4px;
      font-size: 14px;
      padding: 16px;
      .item-title {
        .title-left {
          font-weight: bold;
          color: #15233f;
        }
        .aitive-one {
          color: #939cae;
        }
        .aitive-two {
          color: #2fb324;
        }
        .aitive-three {
          color: #e63746;
        }
      }
      .item-number {
        color: #15233f;
        border-bottom: 1px solid #e8eaed;
        padding-bottom: 12px;
        padding-left: 26px;
      }
      .item-list {
        .isBind {
          color: #15233f;
          white-space: nowrap;
        }
        span {
          color: #7a8599;
          margin-left: 24px;
        }
      }
    }
  }
}
</style>
