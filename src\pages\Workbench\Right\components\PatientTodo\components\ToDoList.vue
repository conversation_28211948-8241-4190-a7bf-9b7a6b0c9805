<template>
  <div>
    <el-drawer v-model="drawer" direction="rtl" @close="close">
      <template #header>
        <div class="flex items-center">
          待办列表
          <img
            :src="addImg"
            alt=""
            class="w-14 h-14 cursor-pointer mt-2 ml-12"
            @click="addToDo"
          />
        </div>
      </template>
      <template #default>
        <div class="header-middle ml-16">
          <el-checkbox
            v-model="useTodoInfo.lookMySelf"
            label="仅看我的"
            size="large"
            class="look-me"
            @change="lookMyself"
          />
        </div>
        <ul class="list-box">
          <li
            v-for="item in todoList"
            :key="item.backlogId"
            v-infinite-scroll="load"
            class="item mb-8"
          >
            <div class="have-data">
              <div class="top flex items-center justify-between">
                <div class="top-left flex items-center">
                  <ShowImg :type="item.type" />
                  <span class="top-title ml-8">
                    {{ getType(item.type) }}
                    <span
                      v-if="getTime(item.term).includes('已逾期')"
                      class="overdue"
                    >
                      {{ getTime(item.term) }}
                    </span>
                    <span
                      v-if="
                        getTime(item.term).includes('今天') ||
                        getTime(item.term).includes('明天')
                      "
                      class="time-num"
                    >
                      {{ getTime(item.term) }}
                    </span>
                  </span>
                </div>
                <div class="top-right flex items-center">
                  <div
                    v-if="item.type === 10"
                    class="detele cursor-pointer mr-16"
                    @click="detele(item)"
                  >
                    删除
                  </div>
                  <div class="postpone cursor-pointer" @click="postpone(item)">
                    稍后提醒
                  </div>
                  <div
                    v-if="item.type !== 12"
                    class="handle cursor-pointer ml-16"
                    @click="handleToDo(item)"
                  >
                    处理
                  </div>
                </div>
              </div>
              <div class="bottom flex items-center mt-4 justify-between">
                <div class="ml-23 bottom-content">
                  <Text>{{ item.content }}</Text>
                </div>
                <div>
                  <span>
                    {{
                      convertDateTime(
                        item.type === 10 ? item.remindTime : item.term
                      ).integrityTime
                    }}
                  </span>
                  <span class="ml-24">{{ item.headName }}</span>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </template>
    </el-drawer>
    <SecondaryConfirmation
      :custom-type="customType"
      :custom-title="customTitle"
      @close="closeCustom"
    />
  </div>
</template>
<script lang="ts" setup>
import addImg from '@/assets/imgs/callCenter/add.png';
import SecondaryConfirmation from './SecondaryConfirmation.vue';
import { todoListApi } from '@/api/todo';
import { getUserRoles, getType, convertDateTime, getTime } from '../index';
import Text from '@/components/Text/index.vue';
import ShowImg from './ShowImg.vue';
import useTodo from '@/store/module/useTodo';
let useTodoInfo = useTodo();
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();

import bus from '@/lib/bus';
import useUserStore from '@/store/module/useUserStore';
bus.on('updete-todo-list', () => {
  queryInfo.value.pageNumber = 1;
  todoList.value = [];
  getTodoList();
});

let drawer = ref(true);
const props = defineProps({
  showTodoList: {
    default: false,
    type: Boolean,
  },
});

const emit = defineEmits(['todoCommit']);

let lookMyself = () => {
  queryInfo.value.onlyMine = Number(useTodoInfo.lookMySelf);
  queryInfo.value.pageNumber = 1;
  getTodoList();
};

watch(
  () => useGlobalInfo.userId,
  () => {
    if (useGlobalInfo.userId) {
      queryInfo.value.patientId = useGlobalInfo.userId;
      getTodoList();
    }
  }
);

interface info {
  backlogId: number;
  content: string;
  headId: number;
  headRole: number;
  patientId: number;
  headName: string;
  imageUrl: string;
  overdueTime: string;
  term: string;
  type: number;
}
const userStore = useUserStore();
let todoList = ref<info[]>([]);
let totalNumber = ref<number>(0);
let queryInfo = ref({
  pageNumber: 1,
  pageSize: 10,
  headId: userStore.accountId,
  headRole: getUserRoles(),
  onlyMine: Number(useTodoInfo.lookMySelf),
  patientId: useGlobalInfo.userId,
});
let getTodoList = () => {
  todoListApi(queryInfo.value).then((res: any) => {
    let { code, data } = res;
    if (code === 'E000000') {
      if (data && data.backlogResponseList) {
        if (queryInfo.value.pageNumber === 1) {
          todoList.value = data.backlogResponseList;
        } else {
          todoList.value = [...todoList.value, ...data.backlogResponseList];
        }
        totalNumber.value = Math.ceil(data.totals / 10);
      } else {
        todoList.value = [];
        queryInfo.value.pageNumber = 1;
      }
    }
  });
};

// 加载到底部分页加载数据
const load = () => {
  if (queryInfo.value.pageNumber < totalNumber.value) {
    queryInfo.value.pageNumber++;
    getTodoList();
  }
};

// 新增待办
let addToDo = () => {
  useTodoInfo.setStatus(1);
  emit('todoCommit');
};
// 延期
let postpone = (item: info) => {
  useTodoInfo.setStatus(2);
  emit('todoCommit');
  useTodoInfo.setTodoInfo(item);
};
// 处理
let handleToDo = (item: any) => {
  if (item.type === 1) {
    useTodoInfo.setStatus(3);
    init(item);
  } else if (item.type === 2) {
    useTodoInfo.setStatus(3);
    init(item);
  } else if (item.type === 3) {
    useTodoInfo.setStatus(4);
    init(item);
  } else if (item.type === 4 || item.type === 5) {
    useTodoInfo.setStatus(5);
    init(item);
  } else if (item.type === 9) {
    useTodoInfo.setStatus(9);
    init(item);
  } else if (item.type === 6) {
    useTodoInfo.setStatus(6);
    init(item);
  } else if (item.type === 7) {
    useTodoInfo.setStatus(7);
    init(item);
  } else if (item.type === 8) {
    useTodoInfo.setStatus(8);
    init(item);
  } else if (item.type === 10) {
    customType.value = true;
    customTitle.value = '完成';
    useTodoInfo.setTodoInfo(item);
  } else if (item.type === 11) {
    useTodoInfo.setStatus(11);
    init(item);
  } else if ([13, 14, 15, 16, 17, 18, 19].includes(item.type)) {
    useTodoInfo.setStatus(999);
    init(item);
  }
};
// 删除
let customType = ref<boolean>(false);
let customTitle = ref<string>('');
let detele = (item: info) => {
  customType.value = true;
  customTitle.value = '删除';
  useTodoInfo.setTodoInfo(item);
};
let closeCustom = () => {
  customType.value = false;
  getTodoList();
};

let init = (item: any) => {
  useTodoInfo.setTodoInfo(item);
  emit('todoCommit');
};

// 关闭抽屉
let close = () => {
  queryInfo.value.pageNumber = 1;
  todoList.value = [];
  useTodoInfo.setStatus(0);
  emit('todoCommit');
};

watch(
  () => props.showTodoList,
  newData => {
    drawer.value = newData;
    if (newData) {
      queryInfo.value.onlyMine = Number(useTodoInfo.lookMySelf);
      getTodoList();
    }
  },
  { immediate: true, deep: true }
);
</script>
<style scoped lang="less">
:deep(.el-drawer) {
  height: calc(100% - 60px);
  top: 60px;

  .el-drawer__header {
    margin-bottom: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e9e8eb;
    font-size: 16px;
    font-weight: 600;
    color: #101b25;
  }
  .el-drawer__body {
    display: flex;
    flex-direction: column;
    padding: 0;
    .header-middle {
      .look-me {
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #3a4762;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
          background: #2e6be6;
          border-color: #2e6be6;
        }
      }
    }

    .list-box {
      flex: 1;
      background: #fff;
      overflow-y: scroll;
      box-sizing: border-box;
      padding: 16px;
      padding-top: 8px;
      .item {
        height: 70px;
        background: #f7f8fa;
        border-radius: 4px;
        padding: 12px;
        box-sizing: border-box;
        .have-data {
          .top {
            .top-left {
              .top-title {
                font-size: 14px;
                font-weight: 700;
                color: #3a4762;
                .overdue {
                  color: #e63746;
                }
                .time-num {
                  color: #e37221;
                }
              }
            }
            .top-right {
              font-size: 14px;
              .detele {
                color: #e63746;
              }
              .postpone {
                color: #e37221;
              }
              .handle {
                color: #2e6be6;
              }
            }
          }
          .bottom {
            font-size: 14px;
            color: #7a8599;
            .bottom-content {
              white-space: nowrap; /* 防止换行 */
              overflow: hidden; /* 隐藏溢出部分 */
              text-overflow: ellipsis; /* 显示省略号 */
              flex: 1;
            }
          }
        }
      }
    }

    /*修改滚动条样式*/
    .list-box::-webkit-scrollbar {
      width: 0;
      height: 0;
      /**/
    }

    .list-box::-webkit-scrollbar-track {
      background: rgb(239, 239, 239);
      border-radius: 2px;
    }

    .list-box::-webkit-scrollbar-thumb {
      background: #bfbfbf;
      border-radius: 0;
    }
  }
}
</style>
