<template>
  <Drawer
    :visible="dStore.visible"
    :title="dStore.title"
    size="large"
    modal
    @update:visible="dStore.setVisible(false)"
  >
    <div class="main">
      <div class="header absolute top-0 w-full">
        <span class="name">{{ patientInfo.patientName }}</span>
        <el-divider direction="vertical" />
        <span>{{ patientInfo.gender }}</span>
        <el-divider direction="vertical" />
        <span>{{ patientInfo.age }}岁</span>
      </div>
      <OccurrenceOfIllness
        :key="renderKey"
        mode="view"
        :cards="dStore.illnessCards"
        :resource-id="dStore.sourceId"
        :source-type="dStore.sourceType"
        nav-prefix="intern"
        :additional-data="{
          all: {
            entry_task: true,
            sub_task_id: dStore.submitSourceId || dStore.taskId,
            source_type: dStore.sourceType,
            patient_id: dStore.patientId,
          },
        }"
        :query-params="{
          all: {
            sub_task_id: dStore.taskId,
          },
          attachment: {
            entry_task: false,
            sourceId: dStore.caseId || dStore.sourceId,
          },
        }"
        @mode-change="mode => (isModuleCompleted = mode === 'view')"
      />
    </div>
    <template #footer>
      <div class="p-sm border-t flex-c">
        <el-button @click="dStore.setVisible(false)">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSubmit">
          资料提交
        </el-button>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import Drawer from '@/components/Drawer/index.vue';
import { OccurrenceOfIllness } from '@/features';
import { submitTask } from '@/api/intern';
import useInternDrawer from '@/store/module/useInternDrawer';
import { getPatientInfoBase } from '@/api/overview';
import { formatTimeTemplate } from '@/utils';
import { useRenderRefresh } from '@/hooks/useRefresh';

const labels = ['住院', '门诊', '复查', '入组'];
const emits = defineEmits<{ (e: 'update'): void }>();
const dStore = useInternDrawer();
const patientInfo = ref({
  gender: '未知',
  patientName: '--',
  age: '--',
});
const loading = ref(false);
/** 当前组件所有段落是否处于查看状态 */
const isModuleCompleted = ref(true);
const handleSubmit = async () => {
  if (!isModuleCompleted.value) return ElMessage.error('请完善所有段落内容！');
  loading.value = true;
  try {
    await submitTask({ taskId: dStore.submitSourceId as number });
    ElMessage.success('操作成功！');
    emits('update');
    dStore.setVisible(false);
  } finally {
    loading.value = false;
  }
};
const data = computed(() => ({ id: dStore.sourceId }));
const { renderKey } = useRenderRefresh(data);

/** 获取用户基础信息 */
const getPatientInfo = async (pId: number) => {
  const {
    gender,
    age = '--',
    patientName = '--',
  } = await getPatientInfoBase({ patientId: pId });
  patientInfo.value = {
    gender: gender === 1 ? '男' : gender === 2 ? '女' : '未知',
    age: String(age),
    patientName,
  };
};
const locationStr = computed(() => {
  const { sourceId, sourceType, sourceTime } = dStore;
  if (!sourceId) return '';
  return (
    formatTimeTemplate(sourceTime, 'YYYY-MM-DD') + ' ' + labels[sourceType]
  );
});
const imgViewerProvideData = computed(() => ({
  sourceId: dStore.sourceId,
  sourceType: dStore.sourceType,
  locationStr: locationStr.value,
}));

// 提供注入
provide('imgViewerData', imgViewerProvideData);
watch(
  () => dStore.patientId,
  patientId => getPatientInfo(patientId)
);
</script>

<style scoped lang="less">
.main {
  display: flex;
  flex-direction: column;
  padding: 16px 24px 0;
  position: relative;
  height: 100%;
}
.header {
  background: #f7f8fa;
  font-size: 14px;
  padding: 12px;
  color: #7a8599;
  position: relative;
  z-index: 1;
  .name {
    color: #3a4762;
  }
  &:after {
    content: '';
    width: 100%;
    height: 16px;
    background: #fff;
    position: absolute;
    bottom: -16px;
    left: 0;
  }
}
</style>
