<template>
  <div class="cardiac-arrhythmia">
    <div class="title">起搏器植入</div>
    <div class="main">
      <div class="main-item">
        <div class="nopx-title">起搏器类型：</div>
        <el-checkbox-group v-model="info.type">
          <el-checkbox
            v-for="item in pacemakerType"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="main-item">
        <div class="nopx-title">起搏器厂商：</div>
        <el-select
          v-model="info.manufacturer.type"
          clearable
          placeholder="请选择"
          style="width: 240px"
        >
          <el-option
            v-for="item in pacemakerFactory"
            :key="item.value"
            style="width: 240px"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
        <el-input
          v-model="info.manufacturer.remark"
          :disabled="info.manufacturer.type !== 4"
          style="width: 240px; margin-left: 6px"
          clearable
          placeholder="请输入"
          maxlength="10"
        />
      </div>
      <div class="main-item">
        <div class="nopx-title">起搏器编码：</div>
        <el-select
          v-model="info.code"
          style="width: 240px"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in pacemakerCode"
            :key="item.value"
            style="width: 240px"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </div>
      <div class="main-item">
        <div class="main-item">
          <div class="nopx-title">置入时间：</div>
          <el-date-picker
            v-model="info.imbeddingTime"
            type="datetime"
            clearable
            style="width: 240px"
            placeholder="选择日期时间"
            value-format="x"
            :picker-options="pickerOptions"
            @change="changeDateHanlder"
          />
        </div>
        <div class="main-item">
          <div class="nopx-title">剩余电量：</div>
          <el-input-number
            v-model="info.electricQuantity"
            class="custom-el-input-number"
            :precision="0"
            :min="1"
            :max="100"
            style="width: 120px"
            placeholder="请输入"
          />
          <span class="unit">%</span>
        </div>
      </div>
      <div class="main-item">
        <div class="main-item">
          <div class="nopx-title">测试仪：</div>
          <el-input
            v-model="info.testInstrument"
            clearable
            style="width: 240px"
            placeholder="请输入"
            maxlength="10"
          />
        </div>
        <div class="main-item">
          <div class="nopx-title">电极类型：</div>
          <el-input
            v-model="info.electrodeType"
            clearable
            style="width: 240px"
            placeholder="请输入"
            maxlength="10"
          />
        </div>
      </div>
      <div class="main-item">
        <div class="main-item">
          <div class="nopx-title">电极位置：</div>
          <el-input
            v-model="info.electrodeLocation"
            clearable
            style="width: 240px"
            placeholder="请输入"
            maxlength="10"
          />
        </div>
        <div class="main-item">
          <div class="nopx-title">手术方式：</div>
          <el-input
            v-model="info.surgeryType"
            clearable
            style="width: 240px"
            placeholder="请输入"
            maxlength="10"
          />
        </div>
      </div>
      <div class="main-table">
        <el-table :data="info.node">
          <el-table-column type="index" width="150" label="植入部位">
            <template #default="scope">
              <el-select
                v-model="scope.row.part"
                clearable
                style="width: 120px"
                placeholder="请选择"
              >
                <el-option :value="1" label="左房" />
                <el-option :value="2" label="右房" />
                <el-option :value="3" label="左室" />
                <el-option :value="4" label="右室" />
                <el-option :value="5" label="左束支电极" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="测试脉宽(ms)">
            <template #default="scope">
              <el-input
                v-model="scope.row.pulseWidth"
                class="custom-el-input-number"
                style="width: 90px"
                placeholder="请输入"
              />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="阈值(v)">
            <template #default="scope">
              <el-input
                v-model="scope.row.threshold"
                class="custom-el-input-number"
                style="width: 90px"
                placeholder="请输入"
              />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="阻抗(Ω)">
            <template #default="scope">
              <el-input
                v-model="scope.row.impedance"
                class="custom-el-input-number"
                style="width: 90px"
                placeholder="请输入"
              />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="感知(mV)">
            <template #default="scope">
              <el-input
                v-model="scope.row.perception"
                class="custom-el-input-number"
                style="width: 90px"
                placeholder="请输入"
              />
            </template>
          </el-table-column>
          <el-table-column>
            <template #default="scope">
              <el-link
                v-if="info.node?.length > 1"
                type="danger"
                :underline="false"
                @click="handleDel(scope.row.id)"
              >
                删除
              </el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-button
          v-show="info.node?.length < 5"
          type="text"
          icon="el-icon-plus"
          @click="handleAddItem"
        >
          添加植入节点
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, isArray, some, uniqueId } from 'lodash-es';
import { useInstance } from '@/hooks';
import { pacemakerJson } from '../../hooks';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

let pickerOptions = (time: { getTime: () => number }) => {
  if (time.getTime() > Date.now()) {
    return true;
  } else {
    return false;
  }
};

const { instance } = useInstance();

const info = ref({});

const pacemakerType = pacemakerJson.find(item => item.key === 'type')?.options;

const pacemakerFactory = pacemakerJson.find(
  item => item.key === 'manufacturer'
)?.options;

const pacemakerCode = pacemakerJson.find(item => item.key === 'code')?.options;

const changeDateHanlder = (val: string | number | Date) => {
  if (new Date(val) > Date.now()) {
    info.value.imbeddingTime = undefined;
    instance.$message.error('不能选择未来时间！');
  }
};

function handleAddItem() {
  info.value.node?.push({
    id: uniqueId(),
  });
}

function handleDel(id: any) {
  const list = info.value.node;
  info.value.node = list?.filter((item: { id: any }) => item.id !== id);
}

// 处理心律失常器械植入数据: false | null
function handleDealPacemaker(this: { handleDealPacemaker: () => {} | null }) {
  const data = cloneDeep(info.value);
  const { manufacturer, type, node } = data;
  if (!type?.length) delete data['type'];
  if (!node?.length) delete data['node'];
  if (!manufacturer?.type) {
    delete data['manufacturer'];
  } else {
    if (manufacturer?.type === 4 && !manufacturer?.remark) {
      ElMessage({
        message: '请填写起搏器厂商！',
        type: 'warning',
      });
      return false;
    } else if (manufacturer?.type !== 4) {
      if (data?.manufacturer) data.manufacturer.remark = undefined;
    }
  }
  data.node = data.node?.filter(
    (item: { [x: string]: undefined; id?: any }) => {
      const keys = Object.keys(item);
      delete item.id;
      return keys.some(v => item[v] !== '' && item[v] !== undefined);
    }
  );
  // 判断值是否有效
  const isValid = some(data, val => {
    if (isArray(val)) {
      if (!val?.length) return false;
    }
    return val === 0 || Boolean(val);
  });
  return isValid ? data : null;
}

watch(
  () => props.data,
  data => {
    const { type, manufacturer, node } = data;

    info.value = {
      ...data,
      type: type ?? [],
      manufacturer: manufacturer ?? { type: undefined, remark: undefined },
      node: node?.length
        ? node.map((item: any) => ({ ...item, id: uniqueId() }))
        : [{ id: uniqueId() }],
    };
  },
  {
    deep: true,
    immediate: true,
  }
);

defineExpose({
  handleDealPacemaker,
});
</script>

<style scoped lang="less">
:deep(.el-input__inner),
:deep(.el-input__icon) {
  height: 32px;
  line-height: 32px;
}

:deep(.custom-el-input-number) {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  .el-input-number__increase,
  .el-input-number__decrease {
    display: none;
  }
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    padding: 0 15px;
    text-align: left;
  }
}

.title {
  color: #101b25;
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 16px;
}

.main {
  font-size: 14px;
  color: #203549;
  width: 100%;

  &-item {
    display: flex;
    align-items: center;
    padding: 8px 0;

    .nopx-title {
      width: 86px;
      text-align: right;
    }

    & > .main-item:first-child {
      margin-right: 70px;
    }
  }

  &-table {
    border-top: 1px solid #e9e8eb;
    padding-top: 16px;

    :deep(.el-table) {
      &:before {
        height: 0;
      }

      th {
        color: #203549;
      }

      th,
      td {
        padding: 8px 0;
        border: none;
      }

      .el-table__empty-block {
        display: none;
      }
    }
  }
}
.unit {
  margin-left: 2px;
}
</style>
