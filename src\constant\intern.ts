const generateMap = (list: any) => {
  return list.reduce(
    (pre, cur) => ({
      ...pre,
      [cur.value]: cur.name,
    }),
    {}
  );
};

export const TASK_TYPE = [
  {
    value: -999,
    name: '全部',
  },
  {
    value: 0,
    name: '住院',
  },
  {
    value: 1,
    name: '门诊',
  },
  {
    value: 2,
    name: '复查',
  },
  {
    value: 3,
    name: '入组',
  },
];
export const TASK_STATUS = [
  {
    value: -999,
    name: '全部',
  },
  {
    value: 0,
    name: '待接单',
  },
  {
    value: 1,
    name: '进行中',
  },
  {
    value: 2,
    name: '已完成',
  },
];
export const TASK_SECTION = [
  {
    value: 1,
    name: '入院记录',
  },
  {
    value: 2,
    name: '手术记录',
  },
  {
    value: 3,
    name: '出院记录',
  },
  {
    value: 4,
    name: '门诊记录',
  },
  {
    value: 5,
    name: '门诊处方',
  },
  {
    value: '0-6',
    name: '住院检查',
  },
  {
    value: '1-6',
    name: '门诊检查',
  },
  {
    value: '2-6',
    name: '复查项目',
  },
  {
    value: '3-6',
    name: '住院检查',
  },
];
export const TASK_REASON = [
  {
    value: 0,
    name: '记录新增',
  },
  {
    value: 1,
    name: '原有内容错误',
  },
  {
    value: 2,
    name: '资料补充',
  },
  {
    value: -1,
    name: '其他',
  },
];
// 1已锁单 2已完成 3处理中 4已释放 5已提交 6已驳回
export const SUB_TASK = [
  {
    value: 0,
    name: '待处理',
  },
  {
    value: 1,
    name: '待处理',
  },
  {
    value: 2,
    name: '已完成',
  },
  {
    value: 3,
    name: '处理中',
  },
  {
    value: 4,
    name: '已释放',
  },
  {
    value: 5,
    name: '已提交',
  },
  {
    value: 6,
    name: '已驳回',
  },
];

export const GENDER_MAP = {
  1: '男',
  2: '女',
};

export const TASK_TYPE_MAP = generateMap(TASK_TYPE);
export const TASK_STATUS_MAP = generateMap(TASK_STATUS);
export const TASK_SECTION_MAP = generateMap(TASK_SECTION);
export const TASK_REASON_MAP = generateMap(TASK_REASON);
export const SUB_TASK_MAP = generateMap(SUB_TASK);
