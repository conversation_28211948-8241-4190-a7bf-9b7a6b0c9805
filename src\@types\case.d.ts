import { FormCategoryValues, FormModeValues } from '@/constant';

declare namespace Case {
  // 病例模式类型
  type ModeType = 'view' | 'create' | 'edit';

  // 病例组件Props
  interface CaseProps {
    cards: FormCategoryValues[]; // 控制病例下具体展示哪些模块
    mode: FormModeValues; // 控制病例的状态
    caseId?: number; // 病例ID，创建时为空
  }

  // 子组件状态管理对象
  interface ModuleState {
    key: FormCategoryValues; // 模块key
    mode: FormModeValues; // 当前模式
    schema: any; // 模块Schema
    data: any; // 模块数据
  }

  // 子组件Props
  interface ModuleProps {
    schema: any; // JSON Schema
    mode: FormModeValues; // 控制状态
    data: any; // 初始化表单数据
  }

  // 子组件事件
  interface ModuleEmits {
    (e: 'modeChange', mode: FormModeValues): void;
    (e: 'change', data: any): void;
    (e: 'save'): void;
    (e: 'cancel'): void;
  }
}
