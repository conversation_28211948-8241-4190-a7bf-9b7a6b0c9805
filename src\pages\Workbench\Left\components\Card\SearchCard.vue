<template>
  <div class="card relative">
    <Avator
      :team-type="data.teamType"
      :name="getAvatorName(data.patientName)"
    />
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import Avator from './Avator.vue';
import { ICard } from '@/store/module/useUserList';
import { getAvatorName } from '../../utils';
export interface IProps {
  data: ICard;
}
defineProps<IProps>();
defineOptions({
  name: 'SearchCard',
});
</script>

<style scoped lang="less"></style>
