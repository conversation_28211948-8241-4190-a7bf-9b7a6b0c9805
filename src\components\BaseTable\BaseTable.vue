<template>
  <div class="table-wrapper">
    <el-table
      ref="tableRef"
      v-bind="$attrs"
      v-loading="loading"
      :row-key="rowKey"
      :data="processTableData"
      class="custom-table"
      header-row-class-name="table-head"
    >
      <!-- 默认插槽 -->
      <slot></slot>

      <template v-for="item in columns" :key="item">
        <!-- radio || index || expand -->
        <el-table-column
          v-if="item.type && columnTypes.includes(item.type)"
          v-bind="item"
          :align="item.align ?? 'center'"
        >
          <template #default="scope">
            <!-- expand -->
            <template v-if="item.type === 'expand'">
              <component :is="item.render" v-bind="scope" v-if="item.render" />
              <slot v-else :name="item.type" v-bind="scope"></slot>
            </template>

            <!-- radio -->
            <el-radio
              v-if="item.type === 'radio'"
              v-model="radio"
              :label="scope.row[rowKey]"
            >
              <i></i>
            </el-radio>
          </template>
        </el-table-column>

        <!-- other -->
        <TableColumn v-if="!item.type && item.prop" :column="item">
          <template v-for="slot in Object.keys($slots)" #[slot]="scope">
            <slot :name="slot" v-bind="scope"></slot>
          </template>
        </TableColumn>
      </template>

      <!-- 无数据 -->
      <template #empty>
        <div class="leading-none">
          <slot name="empty">
            <el-empty :image-size="120" />
          </slot>
        </div>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <slot name="pagination">
      <Pagination
        v-if="pagination"
        :pageable="pageable"
        :handle-size-change="handlePaginationSizeChange"
        :handle-current-change="handlePaginationCurrentChange"
      />
    </slot>
  </div>
</template>

<script setup lang="ts">
import { type ElTable } from 'element-plus';
import { useTable } from './hooks/useTable';
import { ColumnProps, TablePageable, TypeProps } from './type';
import Pagination from './components/Pagination.vue';
import TableColumn from './components/TableColumn.vue';

export interface TableProps {
  /** 列配置项 */
  columns?: ColumnProps[];
  /** 静态 table data 数据，若存在则不会使用 requestApi 返回的 data */
  data?: any[];
  /** 请求表格数据的 api */
  requestApi?: (params: any) => Promise<any>;
  /** 是否自动执行请求 api ==> 非必传（默认为true）*/
  requestAuto?: boolean;
  /** 返回数据的回调函数，可以对数据进行处理 */
  dataCallback?: (data: any) => any;
  /** 是否需要分页组件 */
  pagination?: boolean;
  /** 初始化请求参数 默认为{} */
  initParam?: any;
  /** 初始化请求参数改变是否重新请求数据 默认为true */
  reloadWithInitParam?: boolean;
  /** 行数据的Key，非必传（默认为 id） */
  rowKey?: string;
}

const props = withDefaults(defineProps<TableProps>(), {
  columns: () => [],
  data: undefined,
  requestApi: undefined,
  requestAuto: true,
  pagination: true,
  dataCallback: undefined,
  initParam: {},
  rowKey: 'id',
  reloadWithInitParam: true,
});

const emits = defineEmits<{ (e: 'on-change', item: TablePageable): void }>();

// 单选值
const radio = ref('');

// table 实例
const tableRef = ref<InstanceType<typeof ElTable>>();

// column 列类型
const columnTypes: TypeProps[] = ['radio', 'index', 'expand'];

// 表格操作 Hooks
const {
  tableData,
  getTableList,
  reset,
  pageable,
  handleSizeChange,
  handleCurrentChange,
  loading,
} = useTable(
  props.requestApi,
  props.initParam,
  props.pagination,
  props.dataCallback
);

// 初始化表格数据
onMounted(() => {
  props.requestAuto && getTableList();
  setTableDataPageable(props.data);
});

// 处理表格数据
const processTableData = computed(() => {
  if (!props.data) return tableData.value;
  if (!props.pagination) return props.data;
  return props.data.slice(
    (pageable.value.pageNum - 1) * pageable.value.pageSize,
    pageable.value.pageSize * pageable.value.pageNum
  );
});

const handlePaginationSizeChange = (val: number) => {
  handleSizeChange(val);
  handlePaginationEmit();
};

const handlePaginationCurrentChange = (val: number) => {
  handleCurrentChange(val);
  handlePaginationEmit();
};

const handlePaginationEmit = () => {
  if (props.pagination) emits('on-change', pageable.value);
};

// 监听页面 initParam 改化 且 reloadWithInitParam为true时，重新获取表格数据
watch(
  () => props.initParam,
  () => {
    if (props.reloadWithInitParam) reset();
  },
  { deep: true }
);

// 监听页面 data 改化，重新获取分页total数据
watch(() => props.data, setTableDataPageable, { deep: true });

// 设置静态 table data 数据 分页total
function setTableDataPageable(list: TableProps['data']) {
  list && (pageable.value.total = list.length);
}

defineExpose({
  element: tableRef,
  tableData: processTableData,
  radio,
  pageable,
  getTableList,
  reset,
  handleSizeChange,
  handleCurrentChange,
});
</script>
<style lang="less" scoped>
:deep(.custom-table) {
  .table-head {
    .el-table__cell {
      background-color: #f7f8fa;
      border: none;
      .cell {
        font-size: var(--text-size-sm);
        font-weight: normal;
        color: #708293;
      }
    }
  }
}
</style>
