<template>
  <div class="add-report">
    <!-- 顶部结构 -->
    <div class="title-box flex items-center justify-between">
      <div class="left-title flex items-center">
        <img :src="conditionImg" alt="" class="w-14 h-18 mr-6" />
        <span>阶段性总结报告</span>
      </div>
      <div
        class="right-title cursor-pointer flex items-center"
        @click="previewRepor"
      >
        <el-icon><i-ep-View /></el-icon>
        <span class="ml-4">预览</span>
      </div>
    </div>

    <!-- 报告类型 -->
    <CardWrapper class="mt-2xs !rounded-b-none !mb-0" title="报告类型">
      <el-select v-model="reportType" style="width: 240px" placeholder="请选择">
        <el-option
          v-for="item in reportTypes"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </CardWrapper>
    <!-- 报告主题 -->
    <CardWrapper class="!rounded-none !mb-0" title="报告主题">
      <el-input
        v-model="reportTheme"
        placeholder="请输入报告主题"
        show-word-limit
        maxlength="15"
      />
    </CardWrapper>
    <!-- 患者信息 -->
    <CardWrapper class="!rounded-t-none" title="患者信息">
      <BaseMsg :base-msg="baseMsg" />
    </CardWrapper>

    <!-- 出院诊断 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox v-model="diagnose.checked" label="选择" size="large" />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">出院诊断</div>
        <el-input
          v-model="diagnose.value"
          placeholder="请输入出院诊断"
          show-word-limit
          maxlength="1000"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
        />
      </div>
    </div>

    <!-- 手术信息 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox v-model="operation.checked" label="选择" size="large" />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">手术信息</div>
        <el-input
          v-model="operation.value"
          placeholder="请输入手术信息"
          show-word-limit
          maxlength="1000"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
        />
      </div>
    </div>

    <!-- 当前用药 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox v-model="medication.checked" label="选择" size="large" />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">当前用药</div>
        <EditDrugTable ref="RefEditDrugTable" :params-data="medication.value" />
      </div>
    </div>

    <!-- 指标项 -->
    <IndexItem ref="RefIndexItem" v-model:checked="indexItem.checked" />

    <!-- 检查项 -->
    <CheckItem ref="RefCheckItem" v-model:checked="checkItem.checked" />

    <!-- 本次复查结论 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox
          v-model="reviewConclusion.checked"
          label="选择"
          size="large"
        />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">本次复查结论</div>
        <el-input
          v-model="reviewConclusion.value"
          placeholder="请输入本次复查结论"
          show-word-limit
          maxlength="1000"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
        />
      </div>
    </div>

    <!-- 本次复查医生建议 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox v-model="doctorAdvice.checked" label="选择" size="large" />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">本次复查医生建议</div>
        <el-input
          v-model="doctorAdvice.value"
          placeholder="请输入本次复查医生建议"
          show-word-limit
          maxlength="1000"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
        />
      </div>
    </div>

    <!-- 需专家确认问题 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox
          v-model="confirmationIssue.checked"
          label="选择"
          size="large"
        />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">需专家确认问题</div>
        <el-input
          v-model="confirmationIssue.value"
          placeholder="请输入需专家确认问题"
          show-word-limit
          maxlength="1000"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          resize="none"
        />
      </div>
    </div>

    <!-- 图片档案 -->
    <div class="report-box">
      <div class="check-box">
        <el-checkbox v-model="pictureList.checked" label="选择" size="large" />
      </div>
      <div class="content-box mt-16">
        <div class="title mb-8">图片档案</div>
        <UploadImages
          v-model:img-list="pictureList.value"
          :disabled="msgStatus === 'view'"
          :is-view="msgStatus === 'view'"
        />
      </div>
    </div>

    <!-- 确认取消 -->
    <div class="btns flex items-center justify-end mt-8">
      <el-button
        class="save w-76 h-32 flex items-center justify-center cursor-pointer"
        @click="save"
      >
        保存
      </el-button>
    </div>

    <!-- 预览 -->
    <PreviewReport
      v-model:preVisible="previewReportVisible"
      :report-info="previewReportInfo"
      :base-info="baseMsg"
    />

    <!-- 是否直接发送 -->
    <Transmission
      v-model:visible="showSendMessageDialog"
      :report-id="reportId"
      :report-name="reportTheme"
    />
  </div>
</template>
<script setup lang="ts">
import conditionImg from '../../img/condition-img.png';
import CardWrapper from '@/components/CardWrapper/index.vue';
import BaseMsg from './components/BaseMsg.vue';
import IndexItem from './components/IndexItem.vue';
import CheckItem from './components/CheckItem.vue';
import EditDrugTable from '@/components/DrugInfo/components/EditDrugTable.vue';
import UploadImages from '@/components/UploadImages/index.vue';
import { dialogTip, timestampToDate } from '../../hooks';
import PreviewReport from '../PreviewReport/index.vue';
import useGlobal from '@/store/module/useGlobal';
import {
  addReportingApi,
  queryPatientBaseMsgApi,
} from '@/api/managementSituation';
import { getDrugPatientCurrent } from '@/api/drug';
import { ITabProps } from '@/store/module/useTabs';
import { cloneDeep, findIndex } from 'lodash-es';
import store from '@/store';
import useReport from '@/store/module/useReport';
import Transmission from './components/Transmission.vue';

const props = defineProps<ITabProps>();
const useGlobalInfo = useGlobal();
const tabStore = store.useTabs();
const useReportInfo = useReport();

onMounted(() => {
  getBaseInfo();
  getCurrentDrugList();
});

const RefEditDrugTable = shallowRef();
const RefCheckItem = shallowRef();
const RefIndexItem = shallowRef();

// 报告状态
const msgStatus = ref('edit');

/** 报告类型 */
const reportType = ref(1);
const reportTypes = [
  { id: 1, name: '复查报告' },
  { id: 2, name: '阶段性总结报告' },
  { id: 3, name: '调药请示' },
];

// 报告主题
const reportTheme = ref('阶段性管理报告');
/** 段落信息 */
// 出院诊断
const diagnose = reactive({
  checked: false,
  value: '',
});
// 手术信息
const operation = reactive({
  checked: false,
  value: '',
});
// 当前用药
const medication = reactive({
  checked: false,
  value: [],
});
// 指标项
const indexItem = reactive({
  checked: false,
  value: [],
});
// 检查项
const checkItem = reactive({
  checked: false,
  value: [],
});
// 本次复查结论
const reviewConclusion = reactive({
  checked: false,
  value: '',
});
// 本次复查医生建议
const doctorAdvice = reactive({
  checked: false,
  value: '',
});
// 需专家确认问题
const confirmationIssue = reactive({
  checked: false,
  value: '',
});
// 图片档案
const pictureList = reactive({
  checked: false,
  value: [],
});

// 基础信息
const baseMsg = ref({
  patientName: '--',
  gender: 0,
  age: '--',
  operationTime: '--',
  theme: '--',
});
// 查询患者相关信息
const getBaseInfo = () => {
  queryPatientBaseMsgApi({ patientId: useGlobalInfo.userId }).then(
    (res: any) => {
      if (res?.data) {
        let {
          patientName,
          gender,
          age,
          surgeryDate,
          diagnosis,
          surgeryCon = '',
          reviewCon = '',
        } = res.data;
        baseMsg.value = {
          patientName,
          gender,
          age,
          operationTime: surgeryDate ? timestampToDate(surgeryDate) : '--',
          theme: reportTheme.value,
        };

        diagnose.value = diagnosis?.join('；');
        operation.value = surgeryCon;
        reviewConclusion.value = reviewCon;

        handleInitData(patientName);
      }
    }
  );
};

// 根据不同入口初始化默认数据
const handleInitData = (patientName?: string) => {
  const pReportTheme = props.data?.reportTheme;
  if (!pReportTheme) return;
  reportTheme.value = `${patientName || ''} ${pReportTheme}`;

  diagnose.checked = true;
  operation.checked = true;
  medication.checked = true;
  indexItem.checked = true;
  checkItem.checked = true;
  reviewConclusion.checked = true;
  doctorAdvice.checked = true;
};

let getCurrentDrugList = async () => {
  if (!useGlobalInfo.userId) return;
  await getDrugPatientCurrent({ patientId: useGlobalInfo.userId })
    .then((res: any) => {
      medication.value =
        res?.drugList?.map((v: { drugAmount: { [x: string]: any } }) => {
          delete v.drugAmount['custom'];
          return v;
        }) || [];
    })
    .catch(() => {
      medication.value = [];
    });
};

const formatVal = () => {
  let errMsg = '';
  if (!reportTheme.value) {
    errMsg = '请填写报告主题！';
  } else if (doctorAdvice.checked && !doctorAdvice.value) {
    errMsg = '请填写本次复查医生建议！';
  } else if (confirmationIssue.checked && !confirmationIssue.value) {
    errMsg = '请填写需专家确认问题！';
  }

  if (errMsg) {
    dialogTip(errMsg);
    return false;
  }

  // 指标项
  indexItem.value =
    RefIndexItem.value.checkedList.map(
      ({ indexTermId, name, indexType, checkType, unit }: any) => {
        return {
          unit,
          indexType,
          checkType,
          indexTermId,
          indexName: name,
        };
      }
    ) || [];
  // 检查项
  checkItem.value = RefCheckItem.value.checkList || [];
  // 当前用药
  const { dealedParamsData } = RefEditDrugTable.value?.submit();
  medication.value = dealedParamsData || [];

  baseMsg.value.theme = reportTheme.value;

  const [startTime, endTime] = RefIndexItem.value.time;
  return cloneDeep({
    startTime,
    endTime,
    patientId: useGlobalInfo.userId,
    reportType: reportType.value,
    theme: reportTheme.value,
    diagnose,
    operation,
    patientDrug: medication.checked ? medication.value : [],
    drugCheck: medication.checked,
    medication,
    patientIndex: indexItem,
    ecgData: checkItem,
    reviewCon: reviewConclusion,
    suggest: doctorAdvice,
    issue: confirmationIssue,
    accessory: pictureList.value,
    imageCheck: pictureList.checked,
    pictureList,
  });
};

const closeTab = () => {
  const tabs = tabStore.getCurrentPatientTabs('ManagementSituation') || [];
  const index = findIndex(tabs, { data: { isNewReport: true } });
  if (index > 1) {
    const data = tabs[index - 1]?.data || {};
    useReportInfo.setRseportInfo({ ...data });
  }
  props.deleteTabItem?.();
};
// 是否直接发送Msg卡片弹框
const showSendMessageDialog = ref(false);
const reportId = ref(0);
const loading = ref(false);
const save = () => {
  const query = formatVal();
  if (!query) return;
  loading.value = true;
  addReportingApi(query)
    .then(res => {
      if (res.code === 'E000000') {
        dialogTip('保存成功！', 'success');
        showSendMessageDialog.value = true;
        reportId.value = res.data?.id;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
watch(showSendMessageDialog, isShow => {
  if (!isShow) {
    closeTab();
  }
});

// 预览
const previewReportVisible = ref(false);
const previewReportInfo = ref({});
const previewRepor = () => {
  const val = formatVal();
  if (!val) return;
  previewReportInfo.value = val;
  previewReportVisible.value = true;
};
</script>
<style scoped lang="less">
.add-report {
  .title-box {
    padding: 16px;
    background: #fff;
    .left-title {
      span {
        font-weight: bold;
        font-size: 16px;
        color: #3a4762;
      }
    }
    .right-title {
      font-size: 14px;
      color: #2e6be6;
    }
  }

  .report-box {
    margin-top: 8px;
    padding: 0 16px 16px;
    background: #ffffff;
    border-radius: 6px;
    .check-box {
      border-bottom: 1px solid #e9e8eb;
    }
    .content-box {
      font-weight: bold;
      font-size: 16px;
      color: #101b25;
    }
  }
  .btns {
    padding: 24px;
    .save {
      background: #0a73e4;
      border-radius: 2px;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
.add-report::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  width: 0;
}
.add-report::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 0;
}
</style>
