import { getReportListAll } from '@/api/indicatorsReport';
import { IDiseaseItem } from '@/components/DiseaseSelector/type';
import { isEmpty } from 'lodash-es';
import { useCache } from '@/hooks';

/** 获取检查项目信息列表 */
export interface IExaminationItem {
  indexTermId: number;
  key: string | null;
  name: string;
  reportType: number | null;
  reportSort: number | null;
}
const reportTypeMap: Record<string, string> = {
  1: '生化',
  2: '临检',
  3: '免疫',
  4: '心电',
  5: '超声',
  6: '造影',
  7: '放射',
  8: 'MRI',
  9: '内镜',
  10: '其他',
  11: '自定义检查',
};
function formatExaminationData(list: IExaminationItem[]) {
  const keys = Object.keys(reportTypeMap);
  const res: Array<IDiseaseItem & Pick<IExaminationItem, 'reportSort'>> =
    keys.map((key, index) => ({
      chooseType: 'title',
      diseaseName: reportTypeMap[key],
      diseaseType: Number(key),
      diseaseId: -100000 + index,
      pId: null,
      children: [],
      reportSort: null,
    }));

  for (const v of list) {
    const { reportType, name = '', indexTermId = -1 } = v;
    const cur = res.find(i => i.diseaseType === reportType);
    if (cur) {
      cur.reportSort = v.reportSort;
      cur.children.push({
        ...v,
        children: [],
        chooseType: 'checkbox',
        diseaseId: indexTermId,
        diseaseName: name,
        pId: cur.diseaseId,
      });
    }
  }

  return res;
}

/** filterProcessedKeys: 过滤processedData中指定key */
export const useExaminationList = (filterProcessedKeys?: string[]) => {
  const { data, status, error, refresh } = useCache<IExaminationItem[]>(
    'Examination_List',
    () => getReportListAll({ nonAiReport: true }) as Promise<IExaminationItem[]>
  );

  const processedData = computed(() => {
    /** 全部里面过滤掉动态血压（indexTermId:-2），报告指标=》报告原文专用 */
    const checkOptions = formatExaminationData(
      data.value?.filter(
        item =>
          item.indexTermId !== -2 && !filterProcessedKeys?.includes(item.key!)
      ) || []
    );
    /** 过滤掉没有子项的项 */
    return checkOptions?.filter(checkOption => !isEmpty(checkOption?.children));
  });

  return {
    rawData: data,
    processedData,
    status,
    error,
    refresh,
  };
};
