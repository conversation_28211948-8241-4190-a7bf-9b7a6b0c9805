<template>
  <div class="flex-1">
    <CustomTabs group="ManagementSituation" @tab-change="tabChange" />
  </div>
</template>

<script setup lang="ts">
import CustomTabs from '@/pages/Workbench/Main/components/CustomTabs.vue';
import store from '@/store';
const tabs = store.useTabs();
import home from './home/<USER>';
import { queryPatientOrderApi } from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();
import useReport from '@/store/module/useReport';
let useReportInfo = useReport();

// 查询患者订单管理周期
let queryPatientOrderList = async (
  patientId: number | undefined,
  changeMainTab?: boolean
) => {
  await queryPatientOrderApi({ patientId }).then((res: any) => {
    const data = res.data || [];
    data.forEach((item: any) => {
      tabs.addTab(
        {
          name: `${item.productName}:${item.type === 1 ? '新购' : '续费'}`,
          group: 'ManagementSituation',
          component: home,
          data: item,
          key: item.orderId,
          disableCache: true,
          changeMainTab,
          mainTabCode: 4,
          closeable: false,
        },
        100
      );
    });
    useReportInfo.setRseportInfo(data[data.length - 1]);
    if (useGlobalInfo.manageTabReadyCallback) {
      useGlobalInfo.manageTabReadyCallback?.();
      useGlobalInfo.manageTabReadyCallback = undefined;
    }
  });
};

watch(
  () => useGlobalInfo.userId,
  () => {
    if (useGlobalInfo.userId) {
      queryPatientOrderList(useGlobalInfo.userId, false);
    }
  }
);

let tabChange = (val: { data: undefined }) => {
  useReportInfo.setRseportInfo(val.data);
};

onMounted(() => {
  if (useGlobalInfo.userId) {
    queryPatientOrderList(useGlobalInfo.userId);
    useGlobalInfo.manageTabReady = true;
  }
});
</script>
