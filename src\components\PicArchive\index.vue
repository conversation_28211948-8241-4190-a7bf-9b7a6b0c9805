<template>
  <div v-loading="state.loading" class="scrollbar-wrapper">
    <el-scrollbar
      ref="scrollbarRef"
      :height="scrollbarHeight"
      @scroll="onScroll"
    >
      <div>
        <div
          v-for="imgGroup in imgInfoList"
          :key="imgGroup.groupId"
          class="img-group pb-md"
        >
          <div class="group-label">
            <div class="label-icon"></div>
            {{ imgGroup.label }}
            <el-checkbox
              v-if="checkEdit"
              v-model="imgGroup.isChecked"
              class="pl-2xs"
              :true-value="1"
              :false-value="0"
              :indeterminate="imgGroup.indeterminate"
              @change="handleParentCheck(imgGroup)"
            >
              全选
            </el-checkbox>
          </div>
          <div class="img-box">
            <div class="img-list">
              <div
                v-for="(imgInfo, index) in imgGroup.urls"
                :key="index"
                class="mr-8 mb-8 img-item"
              >
                <ImgPreview
                  :key="index"
                  :url="imgInfo.url"
                  :width="100"
                  :fixed="true"
                  :type="imgGroup.label"
                  :lazy="true"
                />
                <div v-if="checkEdit" class="check-box">
                  <el-checkbox
                    v-model="imgInfo.isChecked"
                    size="large"
                    :true-value="1"
                    :false-value="0"
                    @change="handleUrlCheck(imgGroup)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-if="!imgInfoList.length" description="暂无相关图片档案" />
    </el-scrollbar>
  </div>
</template>

<script lang="ts" setup>
import ImgPreview from '@/components/ImgPreview/index.vue';
import { getUuid } from '@/utils';

import useTabs from '@/store/module/useTabs';

const tabStore = useTabs();

import { getReportPictures } from '@/api/indicatorsReport';

import useGlobal from '@/store/module/useGlobal';

import { ImgItemList } from './type';

import dayjs from 'dayjs';

interface Props {
  scrollbarHeight?: string;
  checkEdit?: boolean;
}

withDefaults(defineProps<Props>(), {
  scrollbarHeight: 'calc(100vh - 164px)',
  checkEdit: false,
});

const globalData = useGlobal();

const emits = defineEmits(['getCheckedUrls']);

const scrollbarRef = ref();

const state = reactive({
  loading: false,
  _scrollTop: 0, // 记录数据加载前滚动条的位置，用来手动定位scrollbar
  page: 1,
  pageSize: 20,
  total: 0,
});

let imgInfoList = ref<ImgItemList[]>([]);

const onScroll = (options: any) => {
  if (state.loading === true) {
    return;
  }
  let wrapRef = scrollbarRef.value.wrapRef;
  scrollbarRef.value.moveY = (wrapRef.scrollTop * 100) / wrapRef.clientHeight;
  scrollbarRef.value.moveX = (wrapRef.scrollLeft * 100) / wrapRef.clientWidth;
  let poor = wrapRef.scrollHeight - wrapRef.clientHeight;
  // 判断滚动到底部
  if (options.scrollTop + 2 >= poor) {
    state._scrollTop = options.scrollTop;
    if (!state.loading && imgInfoList.value.length < state.total) {
      getImgList(1);
    }
  }
};

const getCheckedUrl = () => {
  let urlList: string[] = [];
  imgInfoList.value.forEach((item: ImgItemList) => {
    item.urls.forEach(urlInfo => {
      if (urlInfo.isChecked) {
        urlList.push(urlInfo.url);
      }
    });
  });
  return urlList;
};

// 处理父级复选框的选中状态
const handleParentCheck = (parent: ImgItemList) => {
  parent.urls.forEach(urlItem => {
    urlItem.isChecked = parent.isChecked;
  });
  parent.indeterminate = false;
};

// 处理子级复选框的选中状态
const handleUrlCheck = (parent: ImgItemList) => {
  const checkedCount = parent.urls.filter(urlItem => urlItem.isChecked).length;
  const totalCount = parent.urls.length;
  // 更新父级复选框的 indeterminate 状态
  parent.indeterminate = checkedCount > 0 && checkedCount < totalCount;
  // 更新父级复选框的选中状态
  parent.isChecked = checkedCount === totalCount ? 1 : 0;
};

watch(
  () => imgInfoList,
  () => {
    emits('getCheckedUrls', getCheckedUrl());
  },
  { deep: true, immediate: true }
);

const getImgList = (type: number = 0) => {
  if (!globalData.userId) {
    imgInfoList.value = [];
    return;
  }
  state.loading = true;
  getReportPictures({
    patientId: globalData.userId as number,
    page: state.page,
    pageSize: state.pageSize,
  }).then(res => {
    state.total = res.total ?? 0;
    let dealedImgList: ImgItemList[] =
      res.contents?.map(item => {
        return {
          isChecked: 0,
          groupId: getUuid(),
          label:
            dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') +
            ' ' +
            (item.userName + '上传' ?? '--'),
          urls:
            item.url?.map(url => {
              return {
                url,
                isChecked: 0,
              };
            }) ?? [],
        };
      }) ?? [];
    if (type) {
      imgInfoList.value.push(...dealedImgList);
      state.page++;
    } else {
      imgInfoList.value = [];
      imgInfoList.value = dealedImgList;
    }
    state.loading = false;
  });
};

watch(
  () => globalData.userId,
  () => {
    state.page = 1;
    state.total = 0;
    getImgList();
  }
);
watch(
  () => tabStore.mainActiveTab,
  () => {
    if (tabStore.mainActiveTab === 5) {
      state.page = 1;
      state.total = 0;
      getImgList();
    }
  }
);
onMounted(() => {
  getImgList();
});
</script>

<style lang="less" scoped>
.scrollbar-wrapper {
  background: #ffffff;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 16px 0;

  :deep(.el-checkbox) {
    height: auto;
    .el-checkbox__inner {
      height: 18px;
      width: 18px;
    }
  }
}
.group-label {
  font-weight: bold;
  font-size: 14px;
  color: #3a4762;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  .label-icon {
    width: 6px;
    aspect-ratio: 3/8;
    background: #2e6be6;
    border-radius: 2px;
    margin-right: 10px;
  }
}
.img-box {
  box-sizing: border-box;
  padding: 0 16px;
}
.img-list {
  background-color: #f7f8fa;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 8px 12px;
  display: flex;
  flex-wrap: wrap;
  .img-item {
    position: relative;
    .check-box {
      position: absolute;
      right: 2px;
      top: 10px;
    }
  }
}
:deep(.el-checkbox__inner::after) {
  box-sizing: content-box;
  content: '';
  border: 1px solid transparent;
  border-left: 0;
  border-top: 0;
  height: 10px;
  left: 6px;
  position: absolute;
  top: 1px;
  width: 3px;
  transition: transform 0.15s ease-in 50ms;
  transform-origin: center;
}
</style>
