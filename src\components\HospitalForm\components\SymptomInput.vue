<template>
  <div v-loading="loading" class="input-box-t">
    <el-input
      v-model="context"
      type="textarea"
      :rows="4"
      resize="none"
      :placeholder="
        ocr ? '无结果，可在【原文】中复制内容手动识别' : placeholder
      "
      @input="textChange"
    />
    <div
      v-if="context && !structedDisabled"
      class="text-btn"
      @click="startContent"
    >
      结构化
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['getTextToOcr', 'update:content']);
import { SymptomInputProps } from '../type';
const props = defineProps<SymptomInputProps>();
const context = ref<string>('');
const loading = ref<boolean>(false);
watch(
  () => props.content,
  val => {
    context.value = val;
  },
  {
    immediate: true,
  }
);
const startContent = () => {
  loading.value = true;
  emit('getTextToOcr', context.value);
};
const closeLoading = () => {
  loading.value = false;
};
defineExpose({
  closeLoading,
});
const textChange = (value: any) => {
  emit('update:content', value);
};
</script>

<style scoped lang="less">
.input-box-t {
  position: relative;
  :deep(.el-textarea__inner) {
    border-radius: 2px;
    font-size: 14px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    color: #203549;
    line-height: 20px;
    padding-bottom: 20px;
  }
  :deep(.el-textarea__inner::placeholder) {
    color: #de4747;
  }
  /* 火狐 */
  :deep(.el-textarea__inner:-moz-placeholder) {
    color: #de4747;
  }
  /*ie*/
  :deep(.el-textarea__inner:-ms-input-placeholder) {
    color: #de4747;
  }
  .text-btn {
    position: absolute;
    right: 12px;
    bottom: 6px;
    font-size: 12px;
    font-weight: 400;
    color: #ffffff;
    box-sizing: border-box;
    padding: 4px 12px;
    background-color: #0a73e4;
    border-radius: 2px;
    cursor: pointer;
  }
}
</style>
