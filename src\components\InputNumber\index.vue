<template>
  <el-input-number
    class="custom-el-input-number"
    placeholder="请输入"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
defineOptions({
  name: 'InputNumber',
});
</script>

<style lang="less">
.custom-el-input-number {
  .el-input-number__increase,
  .el-input-number__decrease {
    display: none;
  }
  .el-input__wrapper {
    padding: 0;
  }
  .el-input__inner {
    padding: 0 12px;
    text-align: left;
  }
}
</style>
