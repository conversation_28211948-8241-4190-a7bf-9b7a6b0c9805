<template>
  <div v-if="mode === 'view'" class="break-all">{{ value ?? '--' }}</div>
  <el-input
    v-else
    :model-value="value"
    resize="none"
    :placeholder="placeholder ?? '请输入'"
    class="text-input"
    :maxlength="maxlength ?? 30"
    style="max-width: 320px"
    @input="textChangeHandler"
  />
</template>

<script setup lang="ts">
import { Base } from './type';

interface IProps extends Base {
  placeholder?: string;
  maxlength?: number;
}
defineProps<IProps>();
const emit = defineEmits(['change']);

const textChangeHandler = (val: string | undefined) => {
  emit('change', val);
};

defineOptions({
  name: 'TextInput',
});
</script>
