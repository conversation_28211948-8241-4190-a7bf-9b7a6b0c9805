<template>
  <div class="copy-img-wrapper">
    <div
      ref="copyImgContentRef"
      class="copy-img-content"
      :style="{
        width: imgContentWidth,
        height: imgContentHeight,
        top: `${top}px`,
        left: `${left}px`,
        right: 0,
        bottom: 0,
        transform: `scale(${props.scale}) rotateZ(${props.deg}deg)`,
      }"
    >
      <img
        ref="copyImgRef"
        class="copy-img"
        :src="props.url"
        :style="{ cursor: imgCursor }"
        @load="handleImgLoad"
        @mousemove="handleImgMousemove"
        @mousedown="handleImgMousedown"
        @contextmenu="handleContextmenu"
      />
      <canvas
        ref="copyImgCanvasRef"
        class="copy-img-canvas"
        :width="imgContentWidth"
        :height="imgContentHeight"
      ></canvas>
      <div
        v-if="btnsData.visible"
        class="copy-img-btns"
        :style="{ top: `${btnsData.top}px`, left: `${btnsData.left}px` }"
      >
        <div class="copy-img-btn" @mousedown.stop @click="handleBtnCopy">
          复制
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { throttle } from 'lodash-es';
import { useOcrScan } from '@/store/module/useOcrScan';

interface Props {
  url: string;
  scale: number;
  deg: number;
  fullscreen: boolean;
}
interface BtnsData {
  visible: boolean;
  top: number;
  left: number;
}
interface Bounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}
type Point = [number, number];
type Polygon = Point[];

const props = defineProps<Props>();
const ocrStore = useOcrScan();

const copyImgContentRef = ref();
const copyImgRef = ref();
const copyImgCanvasRef = ref();
const imgContentWidth = ref<string>('100%');
const imgContentHeight = ref<string>('100%');
// 图片的本身尺寸和展示尺寸的比例
const naturalShowScale = ref<number>(1);
const isDragging = ref<boolean>(false);
const isInTextArea = ref<boolean>(false);
const startPos = ref<Point>([0, 0]);
const imgCursor = ref<string>('move');
const selectedTexts = ref<string[]>([]);
const left = ref<number>(0);
const top = ref<number>(0);
const btnsData = reactive<BtnsData>({
  visible: false,
  top: 0,
  left: 0,
});
let ctx: CanvasRenderingContext2D;

// 图片经过scale缩放后需要重新计算比例
const imgScale = computed(() => naturalShowScale.value / props.scale);
// 只在角度为0度和有文字坐标数据时才能复制文字，ai那边只返回了0度的文字坐标
const copyTextDisabled = computed(
  () => Boolean(props.deg) || !ocrStore.ocrTextCoords.length
);

watch(
  () => selectedTexts.value,
  newValue => {
    // 当选区不存在时，需要移除handleKeyboardCopy复制事件，恢复系统的默认复制事件
    if (!newValue.length) {
      document.removeEventListener('copy', handleKeyboardCopy);
      btnsData.visible = false;
    }
  }
);
watch(
  () => props.fullscreen,
  () => {
    fullscreenChange();
  }
);

const handleImgLoad = () => {
  imgContentWidth.value = `${copyImgRef.value.width}px`;
  imgContentHeight.value = `${copyImgRef.value.height}px`;
  getNaturalShowScale();
};
// 图片的mousemove事件，用于判断当前鼠标是否移入文本域内
const handleImgMousemove = throttle((event: MouseEvent) => {
  if (copyTextDisabled.value) {
    isInTextArea.value = false;
    imgCursor.value = 'move';
  } else if (!isDragging.value) {
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    const realX = (event.clientX - rect.left) * imgScale.value;
    const realY = (event.clientY - rect.top) * imgScale.value;

    isInTextArea.value = judgePointInTextArea([realX, realY]);
    imgCursor.value = isInTextArea.value ? 'text' : 'move';
  }
}, 30);
const handleImgMousedown = (event: MouseEvent) => {
  event.stopPropagation();
  event.preventDefault();

  // 这里鼠标右键有其他功能，所以这儿判断一下不是鼠标右键触发的事件
  if (event.button !== 2) {
    cancelSelected();
    isDragging.value = true;
    document.addEventListener('mousemove', handleImgDragMousemove);
    document.addEventListener('mouseup', handleImgMouseup);

    if (isInTextArea.value) {
      const element = event.target as HTMLElement;
      const rect = element.getBoundingClientRect();
      const realX = (event.clientX - rect.left) * imgScale.value;
      const realY = (event.clientY - rect.top) * imgScale.value;

      startPos.value = [realX, realY];
    }
  }
};
// 拖拽过程时图片的mousemove事件
const handleImgDragMousemove = (event: MouseEvent) => {
  if (isInTextArea.value) {
    selectImgTextMousemove(event);
  } else {
    left.value += event.movementX;
    top.value += event.movementY;
  }
};
// 选择图片上的文字
const selectImgTextMousemove = throttle((event: MouseEvent) => {
  const rect = copyImgRef.value.getBoundingClientRect();
  const realX = (event.clientX - rect.left) * imgScale.value;
  const realY = (event.clientY - rect.top) * imgScale.value;
  const [startX, startY] = startPos.value;
  const selectedPolygon: Polygon = [
    [startX, startY],
    [realX, startY],
    [realX, realY],
    [startX, realY],
  ];
  const textArr: string[] = [];

  cancelSelected();
  for (let i = 0; i < ocrStore.ocrTextCoords.length; i++) {
    const item = ocrStore.ocrTextCoords[i];
    const coords = item.tokenBoxes;

    if (judgePolygonsIntersect(selectedPolygon, coords)) {
      const bounds = getBounds(coords);

      textArr.push(item.tokenText);
      ctx.fillStyle = 'rgba(0, 123, 255, 0.3)';
      ctx.fillRect(
        bounds.minX / naturalShowScale.value,
        bounds.minY / naturalShowScale.value,
        (bounds.maxX - bounds.minX) / naturalShowScale.value,
        (bounds.maxY - bounds.minY) / naturalShowScale.value
      );
    }
  }
  selectedTexts.value = textArr;
}, 30);
const handleImgMouseup = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleImgDragMousemove);
  document.removeEventListener('mouseup', handleImgMouseup);
  if (selectedTexts.value.length) {
    document.addEventListener('copy', handleKeyboardCopy);
  }
};
const handleDocumentMouseDown = () => {
  cancelSelected();
};
// 通过键盘(command + c / ctrl + c)发起的复制
const handleKeyboardCopy = (event: ClipboardEvent) => {
  event.preventDefault();
  if (selectedTexts.value.length) {
    event.clipboardData?.setData('text/plain', selectedTexts.value.join(''));
    ElMessage.success('已复制');
  }
};
const handleContextmenu = (event: MouseEvent) => {
  event.preventDefault();
  // 因为navigator.clipboard.writeText方法需要https协议或者localhost环境才能生效，这儿判断下
  if (
    (location.protocol === 'https:' || location.hostname === 'localhost') &&
    selectedTexts.value.length
  ) {
    btnsData.visible = true;
    btnsData.left = event.offsetX;
    btnsData.top = event.offsetY;
  }
};
// 点击复制按钮的复制
const handleBtnCopy = () => {
  if (selectedTexts.value.length) {
    navigator.clipboard.writeText(selectedTexts.value.join('')).then(() => {
      btnsData.visible = false;
      ElMessage.success('已复制');
    });
  }
};
const fullscreenChange = async () => {
  imgContentWidth.value = '100%';
  imgContentHeight.value = '100%';
  left.value = 0;
  top.value = 0;
  await nextTick();

  // 全屏切换时，需要等元素渲染完重新计算图片的本身尺寸和展示尺寸的比例
  const observer = new ResizeObserver(() => {
    handleImgLoad();
    // 取消监听
    observer.unobserve(copyImgContentRef.value);
  });

  observer.observe(copyImgContentRef.value);
};
// 获取图片的本身尺寸和展示尺寸的比例
const getNaturalShowScale = () => {
  naturalShowScale.value =
    (copyImgRef.value.naturalWidth / copyImgRef.value.width +
      copyImgRef.value.naturalHeight / copyImgRef.value.height) /
    2;
};
const cancelSelected = () => {
  ctx.clearRect(
    0,
    0,
    copyImgCanvasRef.value.width,
    copyImgCanvasRef.value.height
  );
  selectedTexts.value = [];
};
// 判断点是否在多边形内
const judgePointInPolygon = (point: Point, polygon: Polygon): boolean => {
  const [x, y] = point;
  const n = polygon.length;
  let inside = false;

  for (let i = 0, j = n - 1; i < n; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];

    const intersect =
      yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi;
    if (intersect) inside = !inside;
  }

  return inside;
};
// 判断点是否在文本区域内
const judgePointInTextArea = (point: Point): boolean => {
  let inside = false;

  for (let i = 0; i < ocrStore.ocrTextCoords.length; i++) {
    const item = ocrStore.ocrTextCoords[i];

    if (judgePointInPolygon(point, item.tokenBoxes)) {
      inside = true;
      break;
    }
  }

  return inside;
};
// 获取多边形的最小和最大坐标
const getBounds = (polygon: Polygon): Bounds => {
  const xArr = polygon.map(point => point[0]);
  const yArr = polygon.map(point => point[1]);
  const minX = Math.min(...xArr);
  const maxX = Math.max(...xArr);
  const minY = Math.min(...yArr);
  const maxY = Math.max(...yArr);

  return { minX, maxX, minY, maxY };
};
// 判断两个多边行是否相交
const judgePolygonsIntersect = (
  polygon1: Polygon,
  polygon2: Polygon
): boolean => {
  const bounds1 = getBounds(polygon1);
  const bounds2 = getBounds(polygon2);

  return !(
    bounds2.minX > bounds1.maxX ||
    bounds2.maxX < bounds1.minX ||
    bounds2.minY > bounds1.maxY ||
    bounds2.maxY < bounds1.minY
  );
};

onMounted(() => {
  ctx = copyImgCanvasRef.value.getContext('2d');
  document.addEventListener('mousedown', handleDocumentMouseDown);
});
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleDocumentMouseDown);
});
</script>

<style lang="less" scoped>
.copy-img-wrapper {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  touch-action: none;
  position: relative;
  overflow: hidden;
  .copy-img-content {
    position: absolute;
    user-select: none;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    .copy-img {
      max-width: 100%;
      max-height: 100%;
      position: static !important;
    }
    .copy-img-canvas {
      position: absolute;
      left: 0;
      top: 0;
      pointer-events: none;
    }
  }
  .copy-img-btns {
    position: absolute;
    z-index: 9999999;
    background: #fff;
    width: 80px;
    border-radius: 2px;
    box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.9);
    box-sizing: border-box;
    padding: 6px 0;
    .copy-img-btn {
      box-sizing: border-box;
      padding: 6px 12px;
      font-size: 12px;
      font-weight: 400;
      color: #203549;
    }
    .copy-img-btn:hover {
      background-color: #ecf4fc;
      user-select: none;
    }
  }
}
</style>
