<template>
  <HrtDialog
    v-model="props.visible"
    size="extraLarge"
    :title="titleInfo.dialogTitle"
    ignore-max-height
    draggable
    :width="1200"
    @update:visible="closeDialog"
  >
    <div class="p-16 main flex justify-between">
      <div class="data w-576" :style="oldStyle">
        <div class="title" :style="titleInfo.oldTitleStyle">
          {{ titleInfo.oldTitle }}
        </div>
        <div class="content">
          <slot name="old"></slot>
        </div>
      </div>
      <div class="data w-576">
        <div class="title" :style="titleInfo.newTitleStyle">
          {{ titleInfo.newTitle }}
        </div>
        <div class="content">
          <slot name="new"></slot>
        </div>
      </div>
    </div>
    <template v-if="!hideFooter" #footer>
      <div class="footer px-24 py-16">
        <div class="tips">{{ tips }}</div>
        <el-button class="cancle-btn common" @click="closeDialog">
          取消
        </el-button>
        <el-button type="primary" :loading="saveLoading" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </HrtDialog>
</template>
<script setup lang="ts">
import { HrtDialog } from '@hrt/components';
import { SourceTypeValues } from '@/constant';
import { CSSProperties } from 'vue';

interface IProps {
  /* 弹窗是否显示 */
  visible: boolean;
  /* 记录类型 */
  sourceType?: SourceTypeValues;
  /* 各个title显示 */
  titleInfo?: {
    oldTitle: string; // 老数据展示的title
    oldTitleStyle?: CSSProperties;
    newTitle: string; // 新数据展示的title
    newTitleStyle?: CSSProperties;
    dialogTitle: string; // 弹窗的title
  };
  /* 底部提示文案 */
  tips?: string;
  /* 是否需要点击确认按钮回调 */
  isNeedCallback?: boolean;
  hideFooter?: boolean;
  saveLoading?: boolean;
  titleBold?: boolean;
  oldStyle?: CSSProperties;
}

const props = withDefaults(defineProps<IProps>(), {
  visible: false,
  sourceType: 0,
  titleInfo: () => ({
    oldTitle: '老数据',
    newTitle: '新数据',
    dialogTitle: '数据对比',
  }),
  tips: '确认后内容即刻生效，不可撤回',
  isNeedCallback: false,
  hideFooter: false,
  saveLoading: false,
});

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'handleCallback'): void;
}>();

const closeDialog = () => {
  emits('update:visible', false);
};

const submit = () => {
  if (props.isNeedCallback) emits('handleCallback');
};
</script>
<style scoped lang="less">
.main {
  .data {
    .title {
      color: #3a4762;
    }
    .content {
      background: #f7f8fa;
      border-radius: 6px;
      margin-top: 12px;
    }
  }
}
.footer {
  font-size: 14px;
  box-shadow: 0px -1px 2px 0px rgba(58, 71, 98, 0.23);
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .warning {
    color: #e63746;
  }
  .tips {
    color: #939cae;
  }
  .common {
    width: 70px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 2px;
    margin-left: 16px;
  }
  .cancle-btn {
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #7a8599;
  }
  .sure-btn {
    background: #2e6be6;
    color: #ffffff;
  }
}
</style>
