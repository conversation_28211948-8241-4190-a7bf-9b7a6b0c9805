<template>
  <el-dialog
    :model-value="dialogVisible"
    :show-close="false"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="1000"
    top="5vh"
    class="picarchive-dialog"
    @close="closeDialog"
  >
    <template #header>
      <div class="flex box-border justify-between px-24 py-16">
        <div class="font-bold text-base">图片档案</div>
        <el-icon :size="16" class="cursor-pointer" @click="closeDialog">
          <i-ep-close />
        </el-icon>
      </div>
    </template>
    <div class="pl-sm leading-normal">
      <PicArchive
        scrollbar-height="600px"
        :check-edit="true"
        @get-checked-urls="getUrls"
      />
    </div>
    <template #footer>
      <div class="dialog-footer p-xl pt-0">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import PicArchive from '@/components/PicArchive/index.vue';

interface Props {
  dialogVisible: boolean;
}

const emits = defineEmits(['update:dialogVisible', 'getCheckedUrls']);

defineProps<Props>();

let urls = reactive([]);

const getUrls = urlList => {
  urls = urlList;
};

const closeDialog = () => {
  urls = [];
  emits('update:dialogVisible', false);
};
const confirmDialog = () => {
  emits('getCheckedUrls', urls);
  emits('update:dialogVisible', false);
};
provide('imgViewerData', {});
</script>
<style lang="less">
.picarchive-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 0 !important;
    margin: 0;
    border-bottom: 1px solid #ebeef5;
  }
  .el-dialog__body {
    padding: 0;
    height: 736px;
  }
}
</style>
