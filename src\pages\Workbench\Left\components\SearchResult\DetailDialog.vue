<template>
  <LoadMore :height="320" @load="loadmore">
    <SearchCard
      v-for="(item, index) in data"
      :key="index"
      :data="item"
      class="dialog-card-item"
    >
      <div class="flex-1 items-center ml-16 overflow-hidden">
        <BasicInfo type="chat" :data="item" :size="'large'">
          <span
            class="text-[#2E6BE6] cursor-pointer"
            @click="() => clickHanlder(item)"
            >查看上下文 &gt;</span
          >
        </BasicInfo>
        <ChatRecord :time="item.chatTime" :text="getChatText(item, false)" />
      </div>
    </SearchCard>
  </LoadMore>
</template>

<script setup lang="ts">
import SearchCard from '../Card/SearchCard.vue';
import BasicInfo from '../Card/BasicInfo.vue';
import ChatRecord from '../Card/ChatRecord.vue';
import LoadMore from '../LoadMore/index.vue';
import { getChatText } from '../../utils';
interface IProps {
  data: any[];
  highlightKey: string;
}
const emits = defineEmits(['clickItem', 'loadDetail']);
const props = defineProps<IProps>();
defineOptions({
  name: 'DetailDialog',
});

const getHighlightKey = (item: any) => {
  if (item.chatType !== 0) return '';
  return props.highlightKey;
};

const loadmore = () => {
  emits('loadDetail');
};
const clickHanlder = (item: any) => {
  emits('clickItem', item);
};
</script>

<style scoped lang="less">
.dialog-card-item {
  font-size: 14px;
  padding: 12px;
  background: unset;
  height: 78px;
  margin-bottom: 0;
  box-shadow: unset;
  border-radius: 6px;
  display: flex;
  align-items: center;
  &:after {
    position: absolute;
    content: '/';
    font-size: 0;
    width: calc(100% - 72px);
    height: 0;
    border-bottom: 1px solid #e9e8eb;
    right: 0;
    bottom: 0px;
  }
}
</style>
