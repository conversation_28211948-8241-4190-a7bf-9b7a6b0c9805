<template>
  <div class="h-90 address-book">
    <div class="header flex items-center justify-between">
      <div class="left flex items-center">
        医患沟通
        <span
          class="address flex items-center ml-6 cursor-pointer"
          @click="queryAddress"
        >
          <img :src="addressBookImg" alt="" class="w-18 h-18 mr-2" />
          通讯录</span
        >
      </div>
      <div class="right flex items-center cursor-pointer" @click="queryAllBook">
        通话记录
        <el-icon size="12px">
          <i-ep-arrow-right color="#2E6BE6" />
        </el-icon>
      </div>
    </div>
    <div class="book-list flex items-center mt-8">
      <img class="w-16 h-16 ml-12" :src="exhalation" alt="" />
      <span class="ml-8">{{ bookObj.name }}</span>
      <span class="ml-35">{{ bookObj.time }}</span>
      <span class="ml-35">{{ bookObj.num }}</span>
      <span class="ml-35">{{ bookObj.relation }}：{{ bookObj.ipone }}</span>
    </div>

    <!-- 呼叫列表 -->
    <TelRecord
      :visible="drawer"
      :record-type="2"
      :immediate="true"
      @change="bookDataChange"
      @close="drawer = false"
    />
    <!-- 通讯录 -->
    <TelephoneDirectory />
  </div>
</template>

<script setup lang="ts">
import exhalation from '@/assets/imgs/callCenter/exhalation.png';
import addressBookImg from '@/assets/imgs/callCenter/address-book-img.png';
import TelephoneDirectory from './components/TelephoneDirectory.vue';
import TelRecord from './components/TelRecord.vue';
import { provide } from 'vue';
import bus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();

// 通讯录
// 查看所有的通讯录
let isTelephone = ref<boolean>(false);
provide('isTelephone', isTelephone);
let queryAddress = () => {
  if (useGlobalInfo.userId) {
    isTelephone.value = true;
    provide('isTelephone', isTelephone);
    bus.emit('get-address-books');
  }
};

let drawer = ref(false);

// 查看所有的通话记录
let queryAllBook = () => {
  if (useGlobalInfo.userId) {
    drawer.value = true;
  }
};

let bookObj = ref({
  time: '--',
  num: '--',
  relation: '--',
  ipone: '--',
  name: '--',
});

const bookDataChange = (item: any) => {
  bookObj.value = item;
};
</script>
<style scoped lang="less">
.address-book {
  padding: 16px;
  box-sizing: border-box;
  background: #fff;
  box-shadow: -4px 2px 7px 0px rgba(200, 201, 204, 0.3);
  border-radius: 6px 0px 0px 6px;
  .header {
    .left {
      font-size: 16px;
      font-weight: 600;
      color: #101b25;
      .address {
        font-size: 14px;
        color: #2e6be6;
      }
    }
    .right {
      font-size: 14px;
      color: #2e6be6;
    }
  }
  .book-list {
    width: 100%;
    height: 32px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 14px;
  }
}
</style>
