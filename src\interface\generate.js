const fs = require('fs');
const swaggerJson = require('./swaggerApi.json');
const axios = require('axios');
const { exec } = require('child_process');

const getJsonUrl = pid =>
  `https://yapi.hrttest.cn/api/plugin/exportSwagger?type=OpenAPIV2&pid=${pid}&status=all&isWiki=false`;
const logUrl = 'https://yapi.hrttest.cn/api/user/login';
const reqParams = { email: '<EMAIL>', password: 123456 };
const jsonUrlConfig = [{ prefix: '', pid: 239 }];

const targetName = 'type.d.ts';
const filePath = './src/interface/';
const type2JsType = {
  integer: 'number',
  number: 'number',
  string: 'string',
  boolean: 'boolean',
};
const itemType = `
/**
 * 获取数组中 item 类型
 */
export type ItemType<T> = T extends Array<infer U> ? U : never;
`;
let preStr = '';
let curName = '';
let curKey = '';
let curDescription = '';
function clearGlobal() {
  preStr = '';
  curName = '';
  curKey = '';
  curDescription = '';
}

async function getLocalPaths() {
  const result = getPaths(swaggerJson);
  return result;
}
async function getRemotePaths() {
  const resultPaths = [];
  for (const item of jsonUrlConfig) {
    const resJson = await getJson(item);
    const pathList = getPaths(resJson);
    const resPathList = rebuildTitle(pathList, item.prefix);
    resultPaths.push(...resPathList);
  }
  return resultPaths;
}
async function start(useRemoteJson) {
  if (!fs.existsSync(filePath)) {
    fs.mkdirSync(filePath, { recursive: true });
  }
  const result = useRemoteJson ? await getRemotePaths() : await getLocalPaths();
  log('开始转换 TS 类型');

  let content = itemType;
  result.forEach(item => {
    clearGlobal();
    const parametersStr = generateParametersInterface(item);
    content += preStr + parametersStr;
    clearGlobal();
    const responseStr = generateResponseInterface(item);
    content += preStr + responseStr;
  });
  log('开始生成 type.d.ts 文件');
  fs.writeFileSync(filePath + '/' + targetName, content);
  log('生成 type.d.ts 文件完成');
  prettierCode();
}
function prettierCode() {
  log('开始 prettier 格式化');
  exec('npx prettier --write ./src/interface/type.d.ts', error => {
    if (error) {
      log(`执行出错: ${error}`);
      return;
    }
    log('prettier 格式化完成');
  });
}
function rebuildTitle(list, prefix = '') {
  return list.map(v => {
    return {
      ...v,
      title: `I${capitalizeFirstLetter(prefix)}${v.title.slice(1)}`,
    };
  });
}
function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
function formatSpecialLetters(str) {
  return str
    .replaceAll('-', '')
    .replace(/\{([a-zA-Z0-9]+)\}/g, function (_, key) {
      return `dynamic${capitalizeFirstLetter(key)}`;
    });
}
function getPaths(json) {
  const result = [];
  Object.keys(json.paths).forEach(key => {
    const title = key
      .split('/')
      .filter(v => v.length)
      .map(v => formatSpecialLetters(v))
      .map(v => capitalizeFirstLetter(v))
      .join('');
    result.push({ value: json.paths[key], title: `I${title}` });
  });
  return result;
}
function getComments(text) {
  return `
/** ${text} */
`;
}
function getType(item) {
  if (item.type === 'object')
    return getObjectType(item.properties, item.required);
  if (item.type === 'array') return getArrayType(item.items);
  return `${type2JsType[item.type]}`;
}
function getArrayType(items, isRoot) {
  const { type, properties, required: requiredList } = items;
  let str = '';
  if (type === 'object') {
    const newKey = curName + capitalizeFirstLetter(curKey || 'item');
    // 生成子类型
    let string = `
      \n
      /**
       * ${curDescription} 
       */
      export interface ${newKey} {
      `;
    let contentString = '';
    curName = newKey;
    const keys = Object.keys(properties);
    for (const key of keys) {
      curKey = key;
      const isRequired = requiredList && requiredList.includes(key);
      const item = properties[key];
      curDescription = item.description;
      contentString += item.description ? getComments(item.description) : '\n';
      contentString += `${key}${isRequired ? '' : '?'}: ${getType(item)};`;
      curName = newKey;
    }
    string += contentString;
    string += `
}`;
    if (contentString) {
      preStr += string;
    }
    if (!isRoot) {
      if (contentString) {
        str += `${newKey}[]`;
      } else {
        str = 'any[]';
      }
    } else {
      str += `${newKey}[]`;
    }
  } else {
    str += `${getType(items)}[]`;
  }
  return str;
}
function getObjectType(properties, requiredList, isRoot = false) {
  let str = '';
  const oldCurName = curName;
  if (isRoot) {
    str = '{';
    const keys = Object.keys(properties);
    for (const key of keys) {
      curKey = key;
      const item = properties[key];
      curDescription = item.description;
      const isRequired = requiredList && requiredList.includes(key);
      str += item.description ? getComments(item.description) : '\n';
      str += `${key}${isRequired ? '' : '?'}: ${getType(item)};`;
      curName = oldCurName;
    }
    str += `
}`;
  } else {
    const newKey = curName + capitalizeFirstLetter(curKey);
    curName = newKey;
    // 生成数组子类型
    let string = `
      \n
      /**
       * ${curDescription} 
       */
      export interface ${newKey} {
      `;
    const keys = Object.keys(properties);
    for (const key of keys) {
      curKey = key;
      const isRequired = requiredList && requiredList.includes(key);
      const item = properties[key];
      curDescription = item.description;
      string += item.description ? getComments(item.description) : '\n';
      string += `${key}${isRequired ? '' : '?'}: ${getType(item)};`;
      curName = newKey;
    }
    string += `
  }`;
    preStr += string;
    str += `${newKey}`;
  }
  return str;
}
function getBaseType(data) {
  let str = '{';
  str += data.description ? getComments(data.description) : '\n';
  str += `data: ${getType(data)};`;
  str += `
}`;
  return str;
}
function generateResponseInterface(json) {
  const keys = Object.keys(json.value);
  const curItem = json.value[keys[0]];
  curName = json.title;
  const responseData =
    curItem.responses?.['200'].schema?.properties?.data ?? {};
  const { type, items, properties, required } = responseData;
  // 生成初始的字符串
  let string = `
\n
/**
 * ${curItem.summary}
 */
`;
  if (type === 'array') {
    string += `export type ${json.title} = `;
    string += getArrayType(items, true);
  } else if (type === 'object') {
    string += `export interface ${json.title}`;
    string += getObjectType(properties, required, true);
  } else {
    string += `export type ${json.title} = `;
    string += type2JsType[type];
  }
  // 生成完毕
  return string;
}
function generateParametersInterface(json) {
  const keys = Object.keys(json.value);
  const curItem = json.value[keys[0]];
  const parameters = curItem.parameters;
  if (parameters.length === 0) return '';
  const inQuery = parameters.filter(v => v.in === 'query');
  const inBody = parameters.filter(v => v.in === 'body');
  let string = '';
  if (inQuery.length) {
    // 生成 query 参数类型
    string += `
      \n
      /**
       * ${curItem.summary} - query 请求参数
       */
      export interface ${json.title}Query {
      `;
    curName = `${json.title}Query`;

    for (const v of inQuery) {
      const isRequired = v.required;
      string += v.description ? getComments(v.description) : '\n';
      curDescription = v.description;
      curKey = v.name;
      string += `${v.name}${isRequired ? '' : '?'}: ${getType(v)};`;
    }
    string += `
}`;
  }

  if (inBody.length) {
    // 生成 body 参数类型
    string += `
      \n
      /**
       * ${curItem.summary} - body 请求参数
       */
      `;
    curName = `${json.title}Params`;

    const responseData = inBody[0].schema ?? {};
    const { type, items, properties, required } = responseData;
    if (type === 'array') {
      string += `export type ${json.title}Params = `;
      string += getArrayType(items, true);
    } else if (type === 'object') {
      string += `export interface ${json.title}Params`;
      string += getObjectType(properties, required, true);
    } else {
      string += `export interface ${json.title}Params`;
      string += getBaseType(responseData);
    }
  }
  // 生成完毕
  return string;
}
function log(key = '', val = '') {
  console.log(`【${new Date().toLocaleString()}】: `, key, val);
}
async function getCookie() {
  log('开始获取 token');
  const res = await axios.post(logUrl, reqParams, {
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  });
  const cookies = res.headers['set-cookie'];
  const cookie = cookies[0].split(';')[0] + ';' + cookies[1].split(';')[0];
  return cookie;
}
let cookie = '';
async function getJson(item) {
  if (!cookie) {
    cookie = await getCookie();
  }
  log(`开始请求${item.prefix} swaggerJson`);
  const res = await axios.get(getJsonUrl(item.pid), {
    headers: {
      Cookie: cookie,
    },
  });
  return res.data;
}
start(true);
