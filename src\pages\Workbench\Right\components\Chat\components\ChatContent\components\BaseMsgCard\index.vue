<template>
  <div
    class="news-card"
    :class="[direction, isChecking ? 'checking' : '']"
    :style="{
      'justify-content': direction == 'left' ? 'flex-start' : 'flex-end',
    }"
  >
    <div class="content" :style="contentStyle">
      <div class="msg-wrap" @contextmenu.prevent="rightMenu()">
        <slot></slot>
        <span
          v-if="isChecking && ['image'].includes(msg.type)"
          class="checkbox"
        >
          <el-checkbox :checked="isChecked" @change="checkedChange" />
        </span>
      </div>
      <div
        v-if="showStatus"
        class="status"
        :style="{ color: isRead ? '#999999' : '' }"
      >
        {{ isRead ? '已读' : '未读' }}
      </div>
      <div
        v-if="msg.status === 'fail'"
        title="重新发送"
        class="retry"
        @click.stop="retry"
      >
        <img :src="retryImg" />
      </div>
      <div v-if="showContextmenu" class="contextmenu">
        <div
          v-if="direction === 'right' && msg.type === 'text'"
          @click="saveExpression"
        >
          存为常用语
        </div>
        <div v-if="direction === 'right'" @click="widthdrawMsg">撤回消息</div>
        <div
          v-if="['image', 'video'].includes(msg.type)"
          @click.stop="() => dowloadHandler(msg)"
        >
          下载
        </div>
        <div v-if="['image'].includes(msg.type)" @click="multipleSelect('add')">
          多选
        </div>
        <div v-if="['image'].includes(msg.type)" @click="directSave">
          定向保存
        </div>
        <div
          v-if="
            direction === 'left' &&
            msg.type === 'text' &&
            msg.sender.includes('user@')
          "
          @click="answer"
        >
          <img :src="qaImg" />
          问题答复
        </div>
      </div>
      <div
        v-if="sender && type !== 'custom' && msg.customContent?.type !== 9"
        class="sender"
      >
        {{ sender }}
      </div>
    </div>
    <span
      v-if="type === 'normal'"
      class="triangle"
      :class="
        msg.from?.startsWith('customer@') ||
        msg.from?.startsWith('rehab@') ||
        msg.from?.startsWith('assistant@')
          ? 'borderColor'
          : 'triangle'
      "
    ></span>
  </div>
</template>

<script setup lang="ts">
import qaImg from '@/assets/imgs/chat/questionImg.png';
import retryImg from '@/assets/imgs/chat/retransmissionMagImg.png';
interface IProps {
  activeId: string;
  direction?: 'left' | 'right';
  isChecking: boolean;
  isChecked: boolean;
  type: 'normal' | 'custom';
  isRead?: boolean;
  showStatus?: boolean;
  showContextmenu?: boolean;
  showName?: boolean;
  msg: any;
  sender: string;
  saveExpression: () => void;
  widthdrawMsg: () => void;
  answer: () => void;
  retry: () => void;
  directSave: () => void;
  multipleSelect: (type: 'add' | 'remove') => void;
}
const props = withDefaults(defineProps<IProps>(), {
  direction: 'right',
  type: 'normal',
  showStatus: true,
  showRevocation: false,
});
const emits = defineEmits(['rightMenu', 'menuEmit']);
defineOptions({
  name: 'BaseMsgCard',
});

const checkedChange = (val: boolean) => {
  props.multipleSelect(val ? 'add' : 'remove');
};
const rightMenu = () => {
  if (props.isChecking) return;
  emits('rightMenu');
};

const dowloadHandler = (msg: any) => {
  const url = msg.text;
  let filename = msg.file.name;
  if (msg.type === 'image' && msg.file.name === 'blob-undefined') {
    filename = props.activeId + '.' + msg.file.ext;
  }
  fetch(url)
    .then(res => res.blob())
    .then(res => {
      const href = URL.createObjectURL(res);
      const link = document.createElement('a');
      link.href = href;
      link.download = filename;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    });
};
const contentStyle = computed(() => {
  const res: any = {};
  const { type, direction, msg } = props;
  if (direction === 'right' && type === 'normal') {
    res.background = '#0a73e4';
  } else if (
    direction === 'left' &&
    (msg.from?.startsWith('customer@') ||
      msg.from?.startsWith('rehab@') ||
      msg.from?.startsWith('assistant@'))
  ) {
    res.background = '#DEEBF8';
    res.color = '#3A4762';
  } else {
    res.background = '#fff';
    res.color = '#203549';
  }
  if (type === 'normal') {
    res.padding = '8px';
  }
  return res;
});
</script>

<style scoped lang="less">
.news-card {
  display: flex;
  position: relative;
}
.content {
  max-width: calc(100% - 150px);
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  border-radius: 4px;
  border-top-right-radius: 0;
  position: relative;
  word-wrap: break-word;
  margin: 20px 16px 20px 0;
}
.status {
  position: absolute;
  left: -36px;
  color: red;
  top: calc(50% - 8px);
}
.retry {
  position: absolute;
  cursor: pointer;
  left: -28px;
  top: calc(50% - 8px);
  > img {
    width: 16px;
  }
}
.contextmenu {
  position: absolute;
  min-width: 94px;
  left: -135px;
  border-radius: 4px;
  cursor: pointer;
  color: #2b2b2b;
  top: calc(50%);
  background: #fff;
  transform: translateY(-50%);
  padding: 8px;
  > div {
    height: 32px;
    line-height: 32px;
    padding: 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      border-radius: 4px;
      background: #e6eeff;
      color: #2e6be6;
    }
    > img {
      width: 16px;
      margin-right: 4px;
    }
  }
}
.sender {
  color: #8193a3;
  position: absolute;
  font-size: 12px;
  top: -20px;
  right: -6px;
  text-align: right;
  min-width: 240px;
}
.triangle {
  display: inline-block;
  position: absolute;
  width: 0;
  height: 0;
  right: 10px;
  top: 20px;
  border: 4px solid #0a73e4;
  border-bottom-color: transparent;
  border-right-color: transparent;
}
.msg-wrap {
  display: flex;
  align-items: center;
  .checkbox {
    position: absolute;
    left: -20px;
  }
}
.left {
  .content {
    margin-left: 16px;
    margin-right: 0;
  }
  .contextmenu {
    left: unset;
    right: -106px;
  }
  .triangle {
    right: unset;
    left: 10px;
    border: 4px solid #fff;
    border-bottom-color: transparent;
    border-left-color: transparent;
  }
  .borderColor {
    right: unset;
    left: 10px;
    border: 4px solid #deebf8;
    border-bottom-color: transparent;
    border-left-color: transparent;
  }
  .sender {
    right: unset;
    text-align: left;
    left: -6px;
  }
}
.checking {
  .content {
    margin-left: 36px;
  }
  .triangle {
    left: 30px;
  }
  .sender {
    left: -26px;
  }
  &.right {
    .content {
      margin-right: 36px;
    }
    .checkbox {
      left: unset;
      right: -20px;
    }
    .triangle {
      left: unset;
      right: 30px;
    }
    .sender {
      right: -26px;
    }
  }
}
</style>
