import { http } from '@/network';
import {
  IApiCaseHistoryAccessoriesParams,
  IApiCaseHistoryAccessories,
  IApiCaseHistoryUploadAccessoryParams,
  IApiCaseHistoryUploadAccessory,
  IApiCaseHistoryAccessoryDeleteParams,
  IApiCaseHistoryAccessoryDelete,
} from '@/interface/type';

/** 附件池查询 */
export function getAccessory(params: IApiCaseHistoryAccessoriesParams) {
  return http.post<IApiCaseHistoryAccessories>({
    url: '/api/case/history/accessories',
    data: params,
  });
}

/** 附件池新增附件 */
export function accessoryUpload(params: IApiCaseHistoryUploadAccessoryParams) {
  return http.post<IApiCaseHistoryUploadAccessory>({
    url: '/api/case/history/upload/accessory',
    data: params,
  });
}
/** 附件池删除 */
export function accessoryDelete(params: IApiCaseHistoryAccessoryDeleteParams) {
  return http.post<IApiCaseHistoryAccessoryDelete>({
    url: '/api/case/history/accessory/delete',
    data: params,
  });
}
