<template>
  <div class="grid grid-cols-1 gap-8 h-full relative overflow-y-hidden">
    <div v-if="showHeader" class="bg-white p-16 sticky top-0 z-10">
      <slot name="header"></slot>
      <div v-if="showNav" class="flex items-center justify-center mt-16">
        <div
          v-for="(item, index) in moduleStates"
          :key="item.key"
          class="text-[#7a8599] text-sm relative cursor-pointer"
          @click="jump(item.key)"
        >
          <span
            :class="{ 'text-[#2e6be6] font-semibold': item.key === activeKey }"
          >
            {{ item.schema?.value }}
          </span>
          <span v-if="index < moduleStates.length - 1" class="text-center">
            ···
          </span>
        </div>
      </div>
      <Attachment
        :source-type="sourceType"
        :source-id="Number(resourceId)"
        :is-batch="isBatch"
        :query-params="queryParams"
        @update-source-id="updateSourceId"
        @toggle="() => (changeKey += 1)"
      />
    </div>
    <div class="grid grid-cols-1 gap-8 overflow-y-auto" @scroll="scrollHandler">
      <div v-if="isPending" class="p-5 bg-white rounded shadow">
        <el-skeleton :rows="10" animated />
      </div>
      <template v-else>
        <div
          v-for="module in moduleStates"
          :key="module.key"
          class="grid grid-cols-1 gap-8"
        >
          <DualLayer
            v-if="module.key === FormCategory.DIAGNOSE_REPORT"
            :nav-prefix="componentId"
            :schema="module.schema"
            :module="module"
            :mode="childrenModeMap[module.key]"
            :form-data="formData?.[module.key] ?? []"
            :card-style="cardStyle"
            :source-type="sourceType"
            :resource-id="resourceId"
            :additional-data="additionalData"
            :api-url-middleware="apiUrlMiddleware"
            :query-params="queryParams"
            :readonly="readonly"
            @refresh-list="
              () => fetchSingleCaseData(FormCategory.DIAGNOSE_REPORT)
            "
            @delete-item="id => deleteModuleHandler(id, module.key)"
            @resource-id-change="updateSourceId"
            @add-new-item="
              id => fetchSingleCaseData(FormCategory.DIAGNOSE_REPORT, id)
            "
          />
          <DoubtWrapper
            v-else-if="module.key === FormCategory.OPERATION_RECORD"
            :disabled="mode !== FormMode.VIEW"
            :unit-key="`${componentId}${module.key}`"
            :get-data="getSurgricalData"
          >
            <SurgicalInformation
              :id="`${componentId}${module.key}`"
              ref="surgicalInformation"
              :readonly="readonly"
              :action-type="
                childrenModeMap[module.key] === 'create'
                  ? 'add'
                  : childrenModeMap[module.key]
              "
              :source-type="sourceType"
              :source-id="resourceId"
              :additional-data="additionalData"
              :query-params="queryParams"
            />
          </DoubtWrapper>

          <CaseModule
            v-else
            :schema="module.schema"
            :mode="childrenModeMap[module.key]"
            :data="formData?.[module.key]"
            :card-style="cardStyle"
            :readonly="readonly"
            :nav-prefix="componentId"
            :on-save="
              () => handleModuleSave({ key: module.key, module: module.schema })
            "
            @mode-change="
              curMode => handleModuleStateChange(module.key, curMode)
            "
            @change="val => handleModuleDataChange(module.key, val)"
            @close="() => handleModuleCancel(module.key)"
          />
        </div>
      </template>
    </div>
    <slot name="footer"></slot>
  </div>
</template>

<script setup lang="ts">
import { Case } from '@/@types/case';
import { checkTaskContent, getCaseData, saveCaseData } from '@/api';
import Attachment from '@/components/Attachment/index.vue';
import {
  FormCategory,
  FormCategoryValues,
  FormMode,
  FormModeValues,
  RoleEnum,
  SourceType,
  SourceTypeValues,
} from '@/constant';
import { useFormJSONSchema } from '@/hooks';
import bus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
import useDoubt from '@/store/module/useDoubt';
import { dynamicFormDataFilter, dynamicFormValidator } from '@/utils';
import { isEmpty, throttle } from 'lodash-es';
import { CSSProperties, ref, useId, useTemplateRef, watch } from 'vue';
import SurgicalInformation from '../PatientRecord/AddHospitalized/SurgicalInformation.vue';
import CaseModule from './CaseModule.vue';
import DualLayer from './DualLayer.vue';
import DoubtWrapper from '@/features/DoubtWrapper/index.vue';

// 定义组件属性
const {
  additionalData = {},
  queryParams = {},
  apiUrlMiddleware,
  cardStyle = {},
  cards = [],
  mode = FormMode.VIEW,
  resourceId,
  showHeader = true,
  showNav = true,
  sourceType = SourceType.HOSPITAL,
  isBatch = true,
  readonly = false,
  inTimeKeys = ['', ''],
  beforeSubmitCallback,
} = defineProps<{
  /** 额外数据，用于保存模块数据时携带 */
  additionalData?: Partial<
    Record<FormCategoryValues | 'all', Record<string, any>>
  >;
  /** 额外数据，用于查询数据时携带 */
  queryParams?: Record<'all' | 'attachment' | string, Record<string, any>>;
  /** 接口地址中间件，用于动态生成接口地址 */
  apiUrlMiddleware?: (key: string, mode: FormModeValues) => string; // 控制病例的状态
  /** 卡片自定义样式 */
  cardStyle?: CSSProperties; // 病例ID，创建时为空
  /** 控制病例下具体展示哪些模块 */
  cards?: FormCategoryValues[];
  /** 数据 */
  mode?: FormModeValues; // 接口地址中间件
  resourceId?: number;
  /** 病历类型 0住院 1-门诊 2复查 3入组 */
  sourceType: SourceTypeValues;
  /** 是否显示头部 */
  showHeader?: boolean;
  showNav?: boolean;
  /** 全部附件是否支持批量扫描 */
  isBatch?: boolean;
  /** 只读 */
  readonly?: boolean;
  inTimeKeys?: [FormCategoryValues, string];
  beforeSubmitCallback?: (key: FormCategoryValues) => Promise<any>;
}>();

// 定义组件事件
const emit = defineEmits<{
  modeChange: [mode: FormModeValues];
  resourceIdChange: [resourceId: number];
  change: [data: Record<string, any>];
  updateInTime: [time: string];
}>();

const activeKey = ref<string>(cards?.[0] ?? '');
const childrenModeMap = ref<Record<string, FormModeValues>>({});
// 模块状态管理
const moduleStates = ref<Case.ModuleState[]>([]);
const { data, isPending } = useFormJSONSchema();
const componentId = useId() + '||';
const formData = ref<Record<string, any>>({});
const surgicalInformationRef = useTemplateRef('surgicalInformation');
const globalStore = useGlobal();
const doubtStore = useDoubt();
const changeKey = ref(1);
// 监听子模块状态变化
watch(
  childrenModeMap,
  newMode => {
    // 获取所有模块的状态
    const modes = Object.values(newMode);
    // 初始化最终状态为创建模式
    let finalMode: FormModeValues = FormMode.CREATE;
    // 如果有一个模块是编辑模式，整体切换到编辑模式
    if (modes.includes('edit')) {
      finalMode = 'edit';
    } else if (modes.every(mode => mode === 'view')) {
      // 如果所有模块都是查看模式，整体切换到查看模式
      finalMode = 'view';
    }
    // 触发模式变更事件，通知父组件状态变化
    emit('modeChange', finalMode);
  },
  { deep: true }
);

const getSurgricalData = () => {
  return surgicalInformationRef?.value?.[0]?.getData();
};
const scrollHandler = throttle(() => {
  changeKey.value += 1;
}, 16);

provide('position-change', changeKey);
function handleFormatDiagnoseReportData(data: any) {
  const res = data?.map((item: any) => {
    const keys = Object.keys(item);
    if (item.diagnose_report) {
      return {
        ...item.diagnose_report,
        [item.diagnose_report?.['key']]: item.diagnose_report?.items,
      };
    }
    const specialKeys = keys.filter(v => v !== 'diagnose_report');
    let res;
    specialKeys.forEach(key => {
      res = res || item[key];
    });
    return res;
  });
  return res?.filter(Boolean) ?? [];
}

async function fetchSingleCaseData(
  key: FormCategoryValues,
  sourceId = resourceId
) {
  if (!sourceId) return;
  /** 额外的参数 */
  const extraParams = {
    ...(queryParams?.[key] || {}),
    ...(queryParams?.all || {}),
  };
  /**
   * 业务逻辑判断
   * 如果是实习生端并且传入了sub_task_id，entry_task的值由接口返回的结果确定
   */
  if (
    extraParams?.['sub_task_id'] &&
    globalStore.currentRole === RoleEnum.INTERN
  ) {
    extraParams['entry_task'] =
      (await checkTaskContent(extraParams?.['sub_task_id'])) ??
      extraParams?.['entry_task'];
  }
  // 调用接口获取模块数据
  const res = await getCaseData({
    url: apiUrlMiddleware?.(key, mode) || `/api/case/history/${key}`,
    params: {
      source_id: sourceId,
      source_type: sourceType,
      ...extraParams,
    },
  });
  if (key === FormCategory.DIAGNOSE_REPORT) {
    formData.value[key] = handleFormatDiagnoseReportData(res);
  } else {
    formData.value[key] = res?.[key] ?? {};
    if (inTimeKeys[0] === key) {
      emit('updateInTime', formData.value[key][inTimeKeys[1]]);
    }
  }
}

function fetchAllCasesData() {
  if (mode === 'create' || !resourceId) return;
  for (const card of cards.filter(
    card => card !== FormCategory.OPERATION_RECORD
  )) {
    // 调用接口获取模块数据
    fetchSingleCaseData(card);
  }
}
const deleteModuleHandler = (id, key) => {
  formData.value[key] = formData.value[key].filter(v => v.id !== id);
  bus.emit('refresh-attachment', resourceId!);
};
watch(
  data,
  schemaRes => {
    if (isEmpty(schemaRes)) return;
    // 初始化模块状态
    moduleStates.value = cards.map(card => {
      const schema = schemaRes?.find((e: { key: string }) => e.key === card);
      return {
        key: card,
        mode: mode,
        schema,
        data: {},
      };
    });
  },
  { immediate: true }
);
// 初始化模块状态
async function initModuleStates() {
  try {
    // 如果不是创建模式，获取数据
    if (mode !== 'create' && resourceId) {
      fetchAllCasesData();
    }
  } catch (error) {
    ElMessage.error('加载数据失败，请刷新重试');
  }
}

// 处理模块状态变更
function handleModuleStateChange(
  key: FormCategoryValues,
  mode: FormModeValues
) {
  // 更新指定模块的状态
  childrenModeMap.value[key] = mode;
}

// 处理模块数据变更
function handleModuleDataChange(key: string, data: any) {
  // 更新指定模块的数据
  formData.value[key] = data;
  emit('change', formData.value);
}

/**
 * 用于保存单个模块的数据，并在保存成功后将该模块切换为查看模式
 * @param key - 模块的唯一标识符
 * @returns
 */
async function handleModuleSave({
  key,
  module,
}: {
  key: FormCategoryValues;
  module: any;
}) {
  if (beforeSubmitCallback) {
    const res = await beforeSubmitCallback(key);
    if (!res) return;
  }
  // 如果没有resourceId，说明是新建状态，不能单独保存模块
  if (!dynamicFormValidator(module, formData.value[key])) return;
  const filterData = dynamicFormDataFilter(module, formData.value[key]);
  try {
    const result = await saveCaseData({
      sourceId: resourceId,
      data: {
        ...(filterData || {}),
        ...(additionalData?.all || {}),
        ...(additionalData?.[key] || {}),
      },
      url: apiUrlMiddleware?.(key, mode) || `/api/case/history/${key}`,
    });
    if (result) {
      bus.emit('refresh-attachment', result);
      await fetchSingleCaseData(key, result);
      emit('resourceIdChange', result);
    }
    ElMessage.success('保存成功');
    handleModuleStateChange(key, 'view');
    return result;
  } catch (error) {
    console.error('保存模块数据失败', error);
    ElMessage.error('保存失败，请重试');
  }
}

function handleModuleCancel(key: FormCategoryValues) {
  fetchSingleCaseData(key);
}

/**
 * 锚点定位
 * @param id 锚点id
 */
const jump = (id: string) => {
  activeKey.value = id; // 设置选中的锚点为当前点击的
  const jumpId = `${componentId}${id}`;
  const scrollItem = document.getElementById(jumpId);
  scrollItem?.scrollIntoView({ behavior: 'smooth' });
};

/**
 * 更新病例id
 * @param id 病例id
 */
function updateSourceId(id: number) {
  emit('resourceIdChange', id);
}

/**
 * 刷新数据
 */
function refresh() {
  fetchAllCasesData();
  if (resourceId) {
    bus.emit('refresh-attachment', resourceId);
  }
  surgicalInformationRef.value?.[0]?.refresh?.();
}

watch(
  [() => mode, () => cards],
  () => {
    // 初始化模块状态
    initModuleStates();
    cards.forEach((key: FormCategoryValues) => {
      childrenModeMap.value[key] = mode;
    });
  },
  { immediate: true, once: true }
);

const doubtLocaitonHandler = () => {
  const { unitData, locationSourceId } = doubtStore;
  const unitType = unitData?.unitType?.toLowerCase();
  const unitCategory = unitData?.unitCategory?.toLowerCase();
  const jumpId =
    unitCategory === FormCategory.DIAGNOSE_REPORT
      ? (doubtStore.unitData?.healthCenterKey ?? '') + unitType
      : unitType;
  if (locationSourceId === resourceId) {
    jump(jumpId ?? '');
  }
};
onMounted(() => {
  bus.on('doubt-location', doubtLocaitonHandler);
});
onUnmounted(() => {
  bus.off('doubt-location', doubtLocaitonHandler);
});
defineOptions({
  name: 'OccurrenceOfIllness',
});

defineExpose({
  refresh,
  jump,
});
</script>
