import { createRouter, createWebHashHistory } from 'vue-router';
import { TOKEN_KEY } from '@/constant/cache';

const router = createRouter({
  history: createWebHashHistory(),
  routes: [],
});

const loginPath = '/login';

router.beforeEach((to, from, next) => {
  // 获取登录角色名
  let roleName = '';
  const userRoles = JSON.parse(localStorage.getItem('userRoles') ?? '[]');
  const currentRole = userRoles[0];
  // 医生：ASSISTANT, 健康管理师：CUSTOMER_SERVER, 运动康复师：REHAB, 实习生：INTERN,
  if (currentRole === 'CUSTOMER_SERVER') {
    roleName = '健康管理师';
  } else if (currentRole === 'REHAB') {
    roleName = '运动康复师';
  } else if (currentRole === 'INTERN') {
    roleName = '医学实习生';
  } else {
    roleName = '医生';
  }
  document.title = '哈瑞特' + roleName + '工作台';
  // token存在时不允许进入登录页
  const token = localStorage.getItem(TOKEN_KEY);
  if (token && to.path === loginPath) {
    const redirectPath = currentRole === 'INTERN' ? '/intern' : '/workbench';
    return next(redirectPath);
  }
  next();
});

export default router;
