import { cloneDeep } from 'lodash-es';

export const pciTypes = [
  {
    name: '右优势型冠脉',
    value: 1,
  },
  {
    name: '左优势型冠脉',
    value: 2,
  },
  {
    name: '均衡型',
    value: 3,
  },
];
export const LESSION = [
  {
    type: 0,
    lesionId: '',
    renderType: 'radio',
    name: '完全闭塞',
    children: [
      {
        name: '大于3个月或闭塞时间不详',
        value: 1,
      },
      {
        name: '钝性残端',
        value: 2,
      },
      {
        name: '桥侧枝',
        value: 3,
      },
      {
        name: '闭塞后的第一可见节段',
        value: 4,
      },
      {
        name: '钝边支小于1.5mm',
        value: 5,
      },
    ],
  },
  {
    type: 1,
    lesionId: '',
    name: '三叉病变',
    renderType: 'radio',
    children: [
      {
        name: '1个病变节段',
        value: 6,
      },
      {
        name: '2个病变节段',
        value: 7,
      },
      {
        name: '3个病变节段',
        value: 8,
      },
      {
        name: '4个病变节段',
        value: 9,
      },
    ],
  },
  {
    type: 2,
    lesionId: [],
    name: '分叉病变',
    children: [
      {
        name: 'A、B、C型病变',
        value: 10,
      },
      {
        name: '开口病变',
        value: 13,
      },
      {
        name: '严重钙化',
        value: 16,
      },
      {
        name: '戊、丁、己、G型病变',
        value: 11,
      },
      {
        name: '严重扭曲',
        value: 14,
      },
      {
        name: '血栓',
        value: 17,
      },
      {
        name: '角度小于 70°',
        value: 12,
      },
      {
        name: '角度小于 70°',
        value: 12,
      },
      {
        name: '弥漫病变/小血管病变',
        value: 18,
      },
    ],
  },
];
export const COMMON = {
  operationType: '',
  typeName: '',
  children: [],
  lesion: LESSION,
  guide: '',
  timi: '',
  ffr: '',
};
export const SURGERY_TYPE_LIST = [
  {
    ...cloneDeep(COMMON),
    operationName: '造影',
    operationId: 2,
  },
  {
    ...cloneDeep(COMMON),
    operationName: 'PTCA',
    operationId: 3,
  },
  {
    ...cloneDeep(COMMON),
    operationName: 'PCI',
    operationId: 1,
  },
  {
    operationName: '瓣膜置换术',
    operationId: 6,
    children: [
      {
        childOperationId: 7,
        childOperationName: '二尖瓣机械瓣膜置换术',
      },
      {
        childOperationId: 24,
        childOperationName: '二尖瓣生物瓣膜置换术',
      },
      {
        childOperationId: 8,
        childOperationName: '肺动脉瓣机械瓣膜置换术',
      },
      {
        childOperationId: 26,
        childOperationName: '肺动脉瓣生物瓣膜置换术',
      },
      {
        childOperationId: 27,
        childOperationName: '主动脉瓣机械瓣膜置换术',
      },
      {
        childOperationId: 9,
        childOperationName: '主动脉瓣生物瓣膜置换术',
      },
      {
        childOperationId: 29,
        childOperationName: '三尖瓣机械瓣膜置换术',
      },
      {
        childOperationId: 10,
        childOperationName: '三尖瓣生物瓣膜置换术',
      },
    ],
  },
  {
    operationName: '射频消融',
    operationId: 11,
    children: [
      {
        childOperationId: 12,
        childOperationName: '房颤',
      },
      {
        childOperationId: 13,
        childOperationName: '室上速',
      },
      {
        childOperationId: 14,
        childOperationName: '室速',
      },
    ],
  },
  {
    operationName: '封堵术',
    operationId: 5,
    children: [
      {
        childOperationId: 16,
        childOperationName: '左心耳封堵',
      },
      {
        childOperationId: 17,
        childOperationName: '房间隔封堵',
      },
      {
        childOperationId: 18,
        childOperationName: '室间隔封堵',
      },
    ],
  },
  {
    operationName: '手术用药',
    operationId: 19,
    children: [
      {
        childOperationId: 20,
        childOperationName: '抗血栓形成药物史 (肝素+GPI）',
      },
      {
        childOperationId: 21,
        childOperationName: '比伐卢定单独治疗',
      },
    ],
  },
  {
    operationName: '心律失常器械植入',
    operationId: 20,
    children: [],
  },
];
