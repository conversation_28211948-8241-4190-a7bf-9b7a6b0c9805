<template>
  <div class="nopx-container">
    <div class="header">
      <div>共选中({{ record.num }}/ {{ record.sum }}项)</div>
      <el-icon @click="onClose">
        <i-ep-close />
      </el-icon>
    </div>
    <div v-if="enableFilter" class="search">
      <el-input
        v-model="searchWords"
        size="default"
        placeholder="输入搜索内容"
        @input="searchChange"
      />
    </div>
    <el-scrollbar v-if="data.length">
      <div :key="refreshKey" class="content">
        <TreeNode
          v-for="(item, index) in data"
          :key="item.diseaseId + '-' + index"
          :search-words="searchWords"
          :selected-ids="choosedIds"
          :special-info="specialInfo"
          :special-config="specialConfig"
          :node-data="item"
          :data-map="dataMap"
          :item-layout="itemLayout"
          @on-change="onChange"
          @on-special-change="onSpecialChange"
        />
      </div>
    </el-scrollbar>
    <div v-else class="empty">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, debounce } from 'lodash-es';
import { ref, watchEffect } from 'vue';
import { ICheckListProps, IDiseaseItem, IDiseaseList } from '../type';
import { idsTransform, treeDataFilter } from '../util';
import TreeNode from './TreeNode.vue';

defineOptions({
  name: 'CheckList',
});

export type CheckListSelected = {
  selectedIds: number[];
  selectedItems: IDiseaseItem[];
};

const props = withDefaults(defineProps<ICheckListProps>(), {
  diseaseList: () => [],
  specialConfig: () => [],
  enableFilter: () => true,
  itemLayout: () => 'block',
});
const emit = defineEmits<{
  onChange: [data: Record<string, any>];
  onSpecialChange: [data: Record<string, any>];
  selected: [data: CheckListSelected];
  onClose: [];
}>();
const searchWords = ref('');
const originData = ref<IDiseaseList>([]);
const data = ref<IDiseaseList>([]);
const refreshKey = ref(1);
const choosedIds = ref<number[]>([]);
const specialInfo = ref({});
const record = ref({
  num: 0,
  sum: 0,
});
watchEffect(() => {
  record.value.num = props.num;
});

// 监听选中的ids, 获取选中的items
watch(choosedIds, val => {
  const selectedItems = Object.values(props.dataMap || []).filter(item =>
    val.includes(item.diseaseId)
  );
  emit('selected', { selectedIds: val, selectedItems });
});

watchEffect(() => {
  const { diseaseData, specialConfig = [] } = props;
  const { diseaseIds, specialData } = diseaseData;
  if (diseaseIds) {
    choosedIds.value = diseaseIds;
  }
  if (specialData) {
    const obj = specialConfig.reduce((pre, cur) => {
      return { ...pre, [cur.key]: specialData[cur.key] };
    }, {});
    specialInfo.value = obj;
  }
});
watchEffect(() => {
  const { diseaseList, dataMap } = props;
  if (diseaseList) {
    record.value.sum = Object.keys(dataMap).length;
    originData.value = cloneDeep(diseaseList);
    data.value = diseaseList;
  }
});
const searchChange = debounce(val => {
  const res = treeDataFilter(originData.value, val);
  data.value = res;
  refreshKey.value += 1;
}, 100);
const onSpecialChange = (
  data: ICheckListProps['diseaseData']['specialData']
) => {
  emit('onSpecialChange', { ...data });
};
const onChange = (ids: number[]) => {
  choosedIds.value = ids;
  const { showNameList, selectedPath } = idsTransform(ids, props.dataMap);
  record.value.num = showNameList.length;
  const params = {
    ids,
    selectedPath,
    showNameList,
  };
  emit('onChange', params);
};
const onClose = () => {
  searchWords.value = '';
  searchChange('');
  emit('onClose');
};
</script>

<style scoped lang="less">
.nopx-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  .header {
    display: flex;
    height: 40px;
    line-height: 40px;
    padding: 0 24px;
    align-items: center;
    border-bottom: 1px solid #dcdee0;
    > div {
      flex: 1;
    }
    > i {
      cursor: pointer;
    }
  }
  .search {
    padding: 12px 24px 0;
  }
  .content {
    max-height: calc(100% - 100px);
    display: flex;
    padding: 12px 0 120px;
    flex-wrap: wrap;
    overflow-y: auto;
  }
  .empty {
    padding: 48px 0;
    text-align: center;
  }
  .closeIcon {
    cursor: pointer;
  }
}
</style>
