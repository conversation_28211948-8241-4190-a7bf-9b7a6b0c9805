import STORE_NAMES from '@/constant/storeNames';
import { defineStore } from 'pinia';
import { getRecordData } from '@/api/ocr';
import useGlobal from '@/store/module/useGlobal';
import useInternDrawer from '@/store/module/useInternDrawer';

const globalData = useGlobal();
const internDrawerStore = useInternDrawer();

interface TypeCurrentData {
  caseType?: number;
  patientHistoryId?: number;
}

interface TypeInHospitalAndClinicParams {
  patientHistoryId?: number;
  caseType?: number;
}

export interface UseSaveInfoState {
  //强提醒弹窗
  showConfirmDialog: boolean;
  isCloseDialog: boolean;
  //出入院门诊原始数据
  outPatientClincInfo: {
    //原始数据
    originData: any;
    newData: {
      key: string;
      excludeKeys?: string[];
      data: any;
    };
    //保存方式
    saveType: number;
    currentData: TypeCurrentData;
    inHospitalAndClinicParams: TypeInHospitalAndClinicParams;
    outHospitalParams: TypeInHospitalAndClinicParams;
  };
}

export const useSaveOcrInfo = defineStore(STORE_NAMES.OCR_SAVE_INFO, {
  state: (): UseSaveInfoState => ({
    //强提醒弹窗
    showConfirmDialog: false,
    //是否直接关闭ocr弹窗
    isCloseDialog: false,
    //出入院门诊原始数据
    outPatientClincInfo: {
      //原始数据
      originData: {},
      newData: {
        key: '',
        data: {},
      },
      saveType: 0,
      currentData: {
        caseType: undefined,
        patientHistoryId: undefined,
      },
      inHospitalAndClinicParams: {
        patientHistoryId: globalData.userId as number,
        caseType: 0,
      },
      outHospitalParams: {
        patientHistoryId: globalData.userId ?? undefined,
        caseType: 0,
      },
    },
  }),
  actions: {
    getExtraParams() {
      const baseParams = {
        ocr: true,
        patient_id: globalData.userId,
      };
      if (globalData.currentRole === 4) {
        return {
          ...baseParams,
          entry_task: true,
          sub_task_id: internDrawerStore.taskId,
        };
      }
      return baseParams;
    },
    updateCurrentDataTimeAndId(value: {
      patientHistoryId: number | null;
      date: string;
      saveType: 0 | 1;
    }) {
      this.outPatientClincInfo.currentData.patientHistoryId =
        value.patientHistoryId as number;
      //根据头部参数修改是保存到已有记录还是新纪录
      this.outPatientClincInfo.saveType = value.saveType;
      this.outPatientClincInfo.inHospitalAndClinicParams.patientHistoryId =
        value.patientHistoryId as number;
      this.outPatientClincInfo.outHospitalParams.patientHistoryId =
        value.patientHistoryId as number;
    },

    //展示对比强提醒弹框
    showCompareDialog(annexType: number, param: any, key: string) {
      getRecordData(key, param).then((data: any) => {
        //赋值原始值
        this.outPatientClincInfo.originData = data[key];
        this.showConfirmDialog = true;
      });
    },
    /**保存入院门诊当前信息**/
    saveCurrentData(annexType: number, key: string, source_type: 0 | 1) {
      const params = {
        source_id: this.outPatientClincInfo.currentData.patientHistoryId,
        source_type: source_type,
      };
      this.showCompareDialog(annexType, params, key);
    },
  },
});
