<template>
  <div>
    <PatientRecordHead
      v-if="!locatedPatientHistoryId"
      @get-record-params="getRecordParams"
    />
    <SingleDynamicForm
      ref="admissionRecordRf"
      v-model="formData"
      class="mt-12"
      mode="edit"
      :category="FormCategory.ADMISSION_REPORT"
      hidden-file-upload
      block-label
      :exclude-validate-keys="['admission_date']"
      :extra-data="extraParams"
    />
  </div>
</template>
<script setup lang="ts">
import globalBus from '@/lib/bus';
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';
import { useOcrScan } from '@/store/module/useOcrScan';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';
import PatientRecordHead from './PatientRecordHead.vue';
import { FormCategory, VALIDATION_PASSED } from '@/constant';

const saveOcrStore = useSaveOcrInfo();
const locatedPatientHistoryId = inject('locatedPatientHistoryId');
const ocrStore = useOcrScan();
const formData = ref({});
const recordParams = ref<any>({});
const admissionRecordRf = shallowRef();

const getRecordParams = (value: {
  patientHistoryId: null | number;
  date: string;
  saveType: 0 | 1;
}) => {
  recordParams.value = value;
  saveOcrStore.updateCurrentDataTimeAndId(value);
};

const extraParams = computed(() => {
  return {
    source_type: 0,
    source_id: locatedPatientHistoryId
      ? locatedPatientHistoryId
      : recordParams.value.patientHistoryId,
    admission_date: recordParams.value.date ?? null,
    admission_accessory: [ocrStore.globalImgInfo.currentImgUrl.url],
    ...saveOcrStore.getExtraParams(),
  };
});
const saveData = async businessCallEnabled => {
  if (!recordParams.value.date && !locatedPatientHistoryId) {
    ElMessage.warning('请选择入院时间！');
    return;
  }
  return admissionRecordRf.value?.submit(businessCallEnabled)?.then(res => {
    console.log('$debug: res', res);
    if (res !== undefined && res !== VALIDATION_PASSED) {
      globalBus.emit('close-ocr-dialog');
      globalBus.emit('saved-ocr-info');
      saveOcrStore.showConfirmDialog = false;
      if (locatedPatientHistoryId || recordParams.value.patientHistoryId) {
        let id = locatedPatientHistoryId
          ? locatedPatientHistoryId
          : recordParams.value.patientHistoryId;

        globalBus.emit('refresh-attachment', id);
        globalBus.emit('refresh-record-data', id);
      } else {
        globalBus.emit('updata-review-list');
      }
      ElMessage.success('保存成功！');
      return true;
    }
    return res;
  });
};

const setFormData = (outData = null) => {
  const data = ocrStore.aiResultData[FormCategory.ADMISSION_REPORT];
  const finalData = outData ? outData : data;
  formData.value = finalData;
};

const getSaveData = () => {
  return {
    data: { ...formData.value },
    source_type: extraParams.value.source_type,
    key: FormCategory.ADMISSION_REPORT,
    excludeKeys: ['admission_date'],
  };
};

watch(
  () => ocrStore.aiResultData,
  () => {
    setFormData();
  },
  { deep: true }
);
defineExpose({
  saveData,
  setFormData,
  getSaveData,
});
onMounted(() => {
  setFormData();
});
</script>
<style scoped lang="less">
.my-radio {
  .radio-tips {
    color: #708293;
  }
  :deep(.el-radio__label) {
    font-size: 14px;
    font-weight: 400;
    color: #323233;
  }
  :deep(.el-radio__inner) {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }
  :deep(.el-radio__input.is-checked) {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
  }
}
.select-box {
  :deep(.el-select) {
    width: 240px;
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
.form-label {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  .required-icon {
    color: #ea1212;
  }
}
</style>
<style lang="less"></style>
