<template>
  <div class="flex-1 overflow-x-auto">
    <div class="min-w-[668px]">
      <el-tabs
        v-model="tabs.mainActiveTab"
        class="main-tabs"
        @tab-change="tabChange"
      >
        <el-tab-pane
          v-for="tab in validTabs"
          :key="tab.code + '_' + tab.key"
          lazy
          :label="tab.title"
          :name="tab.code"
          class="outer-tab-content"
        >
          <el-scrollbar>
            <component
              :is="ComponentMap[tab.code as keyof typeof ComponentMap]"
            />
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="tsx">
import store from '@/store';
import CustomTabs from './components/CustomTabs.vue';

const ComponentMap = {
  1: defineAsyncComponent(() => import('./SportPlan/index.vue')),
  2: defineComponent({
    setup: () => () => <CustomTabs group="patient_info" />,
  }),
  3: defineAsyncComponent(() => import('./PatientPortrait/index.vue')),
  4: defineAsyncComponent(() => import('./Management/index.vue')),
  5: defineAsyncComponent(() => import('./FileRecord/index.vue')),
  // 6: defineAsyncComponent(
  //   () => import('./DigitalPortrait/index.vue')
  // ),
};
const tabs = store.useTabs();
const validTabs = ref(tabs.getMainTabs());

const tabChange = (code: number) => {
  tabs.mainActiveTab = code;
};
onMounted(() => {
  tabs.mainActiveTab = validTabs.value[0].code;
});
defineOptions({ name: 'WorkBenchMain' });
</script>
<style lang="less">
.main-tabs {
  > .el-tabs__header {
    background-color: #fff;
    border-left: 1px solid #dcdee0;
    .el-tabs__nav {
      height: 48px;
    }
    .el-tabs__item:nth-child(2) {
      margin-left: 20px !important;
    }
    .el-tabs__item {
      color: #203549;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      height: 20px;
      margin-top: 16px;
    }
  }
  > .el-tabs__content {
    height: calc(100vh - 123px);
    overflow: auto;
  }
  > .el-tabs__item:hover {
    padding-left: 20px !important;
  }
  .outer-tab-content {
    height: calc(100vh - 123px);
    overflow-y: auto;
  }
}
</style>
