<template>
  <div
    class="flex-1 sport-main"
    :class="{
      wait: !useGlobal.manageStatus || useGlobal.manageStatus == 1,
    }"
  >
    <el-tabs v-model="activeIndex" type="border-card">
      <el-tab-pane label="运动评估" :name="1">
        <AthleticStatus @change-tab-index="changeTabIndex" />
      </el-tab-pane>
      <el-tab-pane label="风险评估" :name="2">
        <RiskAssessment @change-tab-index="changeTabIndex" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
// todo
import AthleticStatus from './AthleticStatus/index.vue';
import RiskAssessment from './RiskAssessment/index.vue';
import store from '@/store';
const activeIndex = ref(1);
const changeTabIndex = index => {
  activeIndex.value = index;
};
const useGlobal = computed(() => {
  return store.useGlobal();
});
watch(
  () => useGlobal.value.userId,
  val => {
    if (val) {
      activeIndex.value = 1;
    }
  },
  {
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'SportPlan',
};
</script>
<style scoped lang="less">
// todo
.sport-main {
  overflow: hidden;
  box-sizing: border-box;
  :deep(.el-tabs--border-card) {
    border-width: 0;
    background: none;
    font-size: 14px;
    & > .el-tabs__header {
      border: none;
      //box-shadow: 0px 10px 2px 0px rgba(0, 0, 0, 1);
      //border-bottom: 1px solid rgba(186, 200, 212, 0.5);
      .el-tabs__item {
        width: 130px;
        text-align: left;
        color: #3a4762;
        border: none;
        justify-content: left;
      }
      color: #3a4762;
      .el-tabs__item.is-active {
        color: #3a4762;
        border-radius: 6px 6px 0 0;
        box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
      }
    }
  }
  :deep(.el-tabs) {
    div::-webkit-scrollbar {
      width: 6px;
    }
    div::-webkit-scrollbar-thumb {
      width: 6px;
      background: #bebebe;
      border-radius: 5px;
    }
    .el-tabs__content {
      padding: 0;
      max-height: calc(100vh - 165px);
      overflow: auto;
    }
    .el-tabs__content {
      .el-tab-pane {
        height: calc(100vh - 165px);
        .athletic-status-wrapper.edit,
        .risk-assessment-wrapper.edit {
          max-height: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          > div.p-16 {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .edit-box {
              flex: 1;
              overflow-y: auto;
            }
          }
        }
      }
      .affix {
        width: 100%;
      }
    }
  }
}
.wait {
  :deep(.el-tabs) > .el-tabs__header {
    display: none;
  }
  :deep(.el-tabs) .el-tabs__header {
  }
}
</style>
