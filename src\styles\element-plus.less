// element-plus

:root {
  /* element plus  theme var */
  --el-color-primary: var(--color-primary);
  --el-border-radius-base: 2px;
}

/* 全局覆盖el-badge danger 样式默认颜色 */
.el-badge__content--danger {
  background-color: #dc0101 !important;
}

/* 根据UI规范 el-radio 选中文字无需高亮 */
.el-radio__input.is-checked + .el-radio__label {
  color: inherit !important;
}

/* useHandleData 二次确认弹框 */
.re-confirm-el-message-box {
  .el-message-box__btns {
    padding: 0 24px 14px;
    .el-button {
      width: 80px;
    }
  }
}
