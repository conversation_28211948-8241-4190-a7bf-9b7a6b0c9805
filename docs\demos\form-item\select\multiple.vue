<script setup lang="ts">
import { ref } from 'vue';
import Select from '@/components/FormItem/Select.vue';

const options = ref([
  {
    value: 1,
    label: '胸痛',
  },
  {
    value: 2,
    label: '头痛',
  },
  {
    value: 3,
    label: '呼吸困难',
  },
  {
    value: 4,
    label: '眼胀眼痛',
  },
  {
    value: 5,
    label: '晕倒晕厥',
  },
  {
    value: 6,
    label: '大汗淋漓',
  },
]);
const value = ref([]);
</script>

<template>
  <Select v-model="value" multiple :options="options" mode="edit" />
</template>

<style scoped lang="less"></style>
