# InputNumber 数字输入框

数字输入框组件允许用户输入或选择数值，支持精度控制、范围限制和单位显示。

## 基础用法

使用 `value` 绑定数值，通过 `precision` 控制小数位数，`min` 和 `max` 限制数值范围，通过 `@change` 事件响应值的变更。

<demo vue="form-item/input-number/base.vue" scope="vue"></demo>

## 带单位

通过 `unit` 属性设置单位显示。

<demo vue="form-item/input-number/with-unit.vue" scope="vue"></demo>

## 控制器

通过 `control` 属性设置控制器显示

<demo vue="form-item/input-number/with-control.vue" scope="vue"></demo>

## 查看模式

通过 `mode="view"` 设置为查看模式。

<demo vue="form-item/input-number/with-mode.vue" scope="vue"></demo>

## 属性

| 属性名         | 说明         | 类型                      | 默认值   |
|-------------|------------|-------------------------|-------|
| value       | 绑定值        | `number`                | -     |
| mode        | 显示模式       | `view \| create\| edit` | -     |
| precision   | 数值精度（小数位数） | `number`                | 3     |
| min         | 最小值        | `number`                | 0     |
| max         | 最大值        | `number`                | 99999 |
| step        | 步进值        | `number`                | 1     |
| control     | 是否显示控制按钮   | `boolean`               | true  |
| unit        | 单位         | `string`                | -     |
| style       | 自定义样式      | `CSSProperties`         | -     |
| placeholder | 占位提示文本     | `string`                | -     |

## 事件

| 事件名    | 说明     | 回调参数                      |
|--------|--------|---------------------------|
| change | 值变化时触发 | `(value: number) => void` |

## 功能特点

- 支持键盘输入和按钮调节
- 自动过滤非数字字符
- 支持小数位数限制
- 支持最大/最小值限制
- 支持查看/编辑模式切换
- 支持单位显示
- 支持自定义样式 