import type { AxiosRequestConfig } from 'axios';

// 用于存储每个请求的标识和取消函数
const pendingMap = new Map<string, AbortController>();

function getPendingUrl(config: AxiosRequestConfig) {
  const { url, method, params } = config;
  let data = config.data;
  if (typeof data === 'string') data = JSON.parse(data);
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
}

export default class AxiosCanceler {
  /**
   * 添加请求
   * @param config 请求配置
   */
  addPending(config: AxiosRequestConfig) {
    this.removePending(config);
    const url = getPendingUrl(config);
    const controller = new AbortController();
    config.signal = config.signal || controller.signal;
    if (!pendingMap.has(url)) pendingMap.set(url, controller);
  }

  /**
   * 清除所有等待中的请求
   */
  removeAllPending() {
    pendingMap.forEach(abortController => {
      if (abortController) abortController.abort();
    });
    this.reset();
  }

  /**
   * 移除请求
   * @param config 请求配置
   */
  removePending(config: AxiosRequestConfig) {
    const url = getPendingUrl(config);
    if (pendingMap.has(url)) {
      // 如果当前请求在等待中，取消它并将其从等待中移除
      const abortController = pendingMap.get(url);
      if (abortController?.signal) abortController.abort();
      pendingMap.delete(url);
    }
  }

  /**
   * 重置
   */
  reset() {
    pendingMap.clear();
  }
}
