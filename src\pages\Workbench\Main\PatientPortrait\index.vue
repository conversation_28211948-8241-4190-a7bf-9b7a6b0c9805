<template>
  <div class="patient-portrait flex-1">
    <CardiovascularImaging />
    <RiskAssessment />
    <Intervene />
    <AssessmentScale />
    <QualityLifeAssessment />
  </div>
</template>

<script setup lang="ts">
import AssessmentScale from './components/AssessmentScale/index.vue';
import CardiovascularImaging from './components/CardiovascularImaging/index.vue';
import QualityLifeAssessment from './components/QualityLifeAssessment/index.vue';
import RiskAssessment from './components/RiskAssessment/index.vue';
import Intervene from './components/LifeIntervene/index.vue';

defineOptions({ name: 'PatientPortrait' });
</script>
<style scoped lang="less">
.patient-portrait {
  padding: 0 2px;
}
</style>
