<template>
  <div>
    <Dialog :visible="dialogVisible" @close="close">
      <template #header>
        <img
          src="@/assets/imgs/chat/medicalAssistant.png"
          alt=""
          class="expressionsImg"
        />
        用药助手
      </template>
      <div class="content">
        <div class="searchBox">
          <el-select
            v-model="phraseValue"
            class="phraseValue"
            filterable
            clearable
            placeholder="请选择"
            @change="changePhrase"
          >
            <el-option
              v-for="item in phraseOptions"
              :key="item.typeId"
              :label="item.name"
              :value="item.typeId"
            />
          </el-select>
          <el-input
            v-model.trim="phraseInput"
            class="phraseInput"
            :suffix-icon="Search"
            placeholder="请输入关键字模糊检索"
            @input="searchPhrase"
          />
        </div>
        <div class="phraseList">
          <div class="main">
            <el-scrollbar :key="phraseInput">
              <div v-if="drugList.length" class="drugListBox">
                <div
                  v-for="(item, index) in drugList"
                  :key="index"
                  :class="
                    drugId === item.drugId
                      ? 'itmeName itmeNameActive'
                      : 'itmeName'
                  "
                  @click="queryDetails(item.drugId)"
                >
                  {{ item.drugName }}
                </div>
              </div>
            </el-scrollbar>
            <div v-if="datailsList.length" class="drugDetailsBox">
              <el-scrollbar>
                <div class="topTitle">药品详情</div>
                <div
                  v-for="(item, index) in datailsList"
                  :key="index"
                  class="itemDetails"
                >
                  <div class="titleBox">{{ item.title }}</div>
                  <div class="contentBox">{{ item.content }}</div>
                </div>
              </el-scrollbar>
            </div>

            <div v-else class="nullDataBox">
              <el-empty :image-size="200" />
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import Dialog from '../Dialog/index.vue';
import { Search } from '@element-plus/icons-vue';
import { debounce } from 'lodash-es';
import {
  getDrugSystemType,
  getDrugSystemList,
  getDrugSystemDetail,
} from '@/api/chat';
interface IProps {
  medicatedAssistantVisible: boolean;
}
defineOptions({
  name: 'MedicalAssistant',
});
const props = defineProps<IProps>();
const emits = defineEmits(['close', 'change-text']);
const dialogVisible = ref(false);
const phraseValue = ref();
const phraseInput = ref('');
const phraseOptions = ref<any[]>([]);
const drugList = ref<any[]>([]);
const drugId = ref();
const datailsList = ref<any[]>([]);

const close = () => {
  phraseValue.value = undefined;
  phraseInput.value = '';
  drugId.value = '';
  emits('close');
};
const getDrugTypeList = async () => {
  const res = await getDrugSystemType();
  phraseOptions.value = res.typeList ?? [];
};
const getDrugSpecStr = (obj: any) => {
  const ingredients = obj.ingredients + obj.contentUnit;
  const packageContent = obj.packageNum ? '*' + obj.packageNum + obj.unit : '';
  const packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
  return ingredients + packageContent + packageUnit;
};
const getDrugList = async () => {
  const params = {
    keyword: phraseInput.value,
    limit: false,
    typeId: phraseValue.value,
  };
  const res = await getDrugSystemList(params);
  drugList.value = res.drugList ?? [];
  if (drugList.value.length) {
    drugId.value = drugList.value[0].drugId;
    getDrugDetails(drugId.value);
  }
  datailsList.value = [];
};
const getDrugDetails = async (id: number) => {
  const params = { drugId: id };
  const res = await getDrugSystemDetail(params);
  let maxUsage = res.maxUsage;
  datailsList.value = [
    {
      title: '名称',
      content: res.drugName,
    },
    {
      title: '通用名称',
      content: res.commonName,
    },
    {
      title: '规格',
      content: getDrugSpecStr(res.drugSpec),
    },
    {
      title: '最大用量',
      content: (maxUsage?.ingredients ?? '') + (maxUsage?.contentUnit ?? ''),
    },
    {
      title: '用法',
      content: res.drugMode,
    },
    {
      title: '频率',
      content: res.drugUsage,
    },
    {
      title: '适应症状',
      content: res.drugApplicability,
    },
  ];
};
const queryDetails = (id: number) => {
  drugId.value = id;
  getDrugDetails(id);
};
const searchPhrase = debounce(() => {
  getDrugList();
}, 100);
const changePhrase = () => {
  getDrugList();
};

watch(
  () => props.medicatedAssistantVisible,
  val => {
    dialogVisible.value = val;
    if (val) {
      getDrugTypeList();
      getDrugList();
    }
  }
);
</script>

<style scoped lang="less">
.content {
  width: 100%;
  box-sizing: border-box;
  .searchBox {
    height: 32px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;

    .phraseValue {
      width: 120px;
    }

    .phraseInput {
      width: 240px;
      margin-left: 8px;
    }
  }
  .phraseList {
    padding-bottom: 20px;
    margin-top: 16px;
    .main {
      height: 300px;
      border-top: 1px solid #e9e8eb;
      border-bottom: 1px solid #e9e8eb;
      display: flex;
      .drugListBox {
        height: 100%;
        .itmeName {
          min-height: 52px;
          padding: 16px;
          box-sizing: border-box;
          background: #f5f6f8;
          font-size: 14px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #203549;
          cursor: pointer;
          width: 200px;
        }
        .itmeNameActive {
          background: #fff;
        }
      }
      .drugDetailsBox {
        flex: 1;
        padding-left: 24px;
        box-sizing: border-box;
        .topTitle {
          height: 52px;
          display: flex;
          align-items: center;
          font-size: 14px;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
          font-weight: 600;
          color: #101b25;
        }
        .itemDetails {
          display: flex;
          margin-bottom: 16px;
          .titleBox {
            width: 108px;
            font-size: 14px;
            font-family:
              PingFangSC-Regular,
              PingFang SC;
            font-weight: 400;
            color: #708293;
          }
          .contentBox {
            font-size: 14px;
            font-family:
              PingFangSC-Regular,
              PingFang SC;
            font-weight: 400;
            color: #203549;
            flex: 1;
            line-height: 20px;
          }
        }
      }
      .nullDataBox {
        display: flex;
        justify-content: center;
        flex: 1;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }
}
</style>
