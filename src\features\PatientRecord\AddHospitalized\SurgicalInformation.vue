<template>
  <CardWrapper
    class="outpatient-record-box mb-0"
    title="手术记录"
    :header-show="from !== 'ocr'"
  >
    <EditTitle
      v-if="msgStatus === 'view'"
      :edit-info="editInfo"
      :readonly="readonly"
      @edit-msg="editMsg"
    />
    <div class="edit-content">
      <div class="edit-item mb-24 flex">
        <div class="item-content">
          <div class="sd-box mb-16">
            <div
              v-for="(surgery, index) in surgeryRecords"
              :key="surgery.surgeryId"
              class="content-box mb-14"
            >
              <div class="title">
                <span>手术{{ toChineseNumber(index + 1) }}</span>
              </div>
              <template v-if="msgStatus === 'view'">
                <OutpatientStyle title="附件记录" :title-top="0">
                  <UploadImages
                    v-model:img-list="surgery.accessory"
                    :is-view="msgStatus === 'view'"
                    :disabled="msgStatus === 'view'"
                  />
                </OutpatientStyle>
                <OutpatientStyle title="手术日期" :title-top="0">
                  <div class="query-style">{{ surgery.surgeryTime }}</div>
                </OutpatientStyle>
                <OutpatientStyle title="手术结论" :title-top="0">
                  <div class="query-style">
                    <div>{{ surgery.surgeryText || '--' }}</div>
                    <div class="tag">
                      {{ surgery.conclusion }}
                    </div>
                  </div>
                </OutpatientStyle>
              </template>
              <div v-else class="box mt-8">
                <div class="total-label margin-b flex justify-between">
                  <span
                    class="delete-btn cursor-pointer"
                    @click="deleteSurgery(surgery, index)"
                  >
                    删除
                  </span>
                </div>
                <div v-if="from !== 'ocr'" class="row-item">
                  <div class="row-label">附件记录：</div>
                  <div>
                    <UploadImages
                      v-model:img-list="surgery.accessory"
                      :is-view="msgStatus === 'view'"
                      :disabled="msgStatus === 'view'"
                    />
                  </div>
                </div>
                <!-- <div class="row-item">
                  <div class="row-label">手术名称：</div>
                  <div
                    class="choose-text cursor-pointer"
                    @click="chooseSurgery(index)"
                  >
                    请选择手术
                  </div>
                </div> -->
                <div class="row-item flex-v-center">
                  <div class="row-label">手术时间：</div>
                  <div class="date-box" :style="{ width: '360px' }">
                    <el-date-picker
                      v-model="surgery.surgeryTime"
                      type="date"
                      placeholder="选择日期"
                      class="datepicker"
                      style="width: 100%"
                      value-format="x"
                      format="YYYY-MM-DD"
                      :clearable="false"
                      :disabled-date="pickerOptions"
                      @change="changeTime"
                    />
                    <img
                      :src="changeTimeIng"
                      alt=""
                      class="w-14 h-14 change-time-icon"
                    />
                  </div>
                </div>
                <div class="row-item">
                  <div class="row-label">手术结论：</div>
                  <div class="flex-1 relative">
                    <el-input
                      v-model="surgery.surgeryText"
                      :rows="4"
                      maxlength="1000"
                      type="textarea"
                    />
                    <div v-if="surgery.surgeryText" class="struct-btn">
                      结构化
                    </div>
                    <div
                      v-if="surgery.conclusion"
                      class="tag cursor-pointer"
                      @click="chooseSurgery(index)"
                    >
                      {{ surgery.conclusion }}
                    </div>
                    <div
                      v-if="!surgery.conclusion"
                      class="choose-text cursor-pointer mt-4"
                      @click="chooseSurgery(index)"
                    >
                      {{ surgery.conclusion ? '编辑' : '新增+' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="msgStatus !== 'view'"
              class="add-btn cursor-pointer mt-30 w-200"
              @click="beforeAddEmptySurgery"
            >
              +新增手术记录
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="msgStatus !== 'view' && from !== 'ocr'" class="btn-group">
      <el-button :loading="loading" type="primary" @click="saveInfo">
        保存
      </el-button>
      <el-button
        v-if="msgStatus === 'edit'"
        :loading="loading"
        @click="cancelEdit"
      >
        取消
      </el-button>
      <!-- <div class="btn confirm" @click="saveInfo">保存</div>
      <div
        v-if="msgStatus === 'edit'"
        class="btn cancel ml-8"
        @click="cancelEdit"
      >
        取消
      </div> -->
    </div>

    <SurgeryCheckDialog
      :choosed-surgery-info="choosedSurgeryInfo"
      :choose-surgery-visible="chooseSurgeryVisible"
      @get-surgery-conclusion="getSurgeryConclusion"
      @get-params="getParams"
      @close-surgery-visible="closeSurgeryVisible"
    />
  </CardWrapper>
</template>
<script setup lang="ts">
import { checkTaskContent } from '@/api';
import {
  addDiagnosisSurgeryApi,
  deleteOperationApi,
  queryOperationDetailsApi,
} from '@/api/review';
import changeTimeIng from '@/assets/imgs/callCenter/change-time.png';
import CardWrapper from '@/components/CardWrapper/index.vue';
import UploadImages from '@/components/UploadImages/index.vue';
import {
  FormCategory,
  FormCategoryValues,
  RoleEnum,
  SourceType,
  SourceTypeValues,
} from '@/constant';
import bus from '@/lib/bus';
import store from '@/store';
import useInternDrawer from '@/store/module/useInternDrawer';
import { toChineseNumber } from '@/utils';
import EditTitle from '../EditTitle.vue';
import { dialogTip, timestampToDate, timestampToDateTime } from '../hooks';
import OutpatientStyle from '../OutpatientStyle.vue';
import SurgeryCheckDialog from '../SurgeryCheckDialog/index.vue';

const useGlobalInfo = store.useGlobal();
const internDrawer = useInternDrawer();
const {
  actionType = 'add',
  sourceId = 0,
  sourceType = SourceType.HOSPITAL,
  from = '',
  readonly = false,
  additionalData = {},
  queryParams = {},
} = defineProps<{
  /** 操作类型 */
  actionType?: string;
  /** 病例🆔 */
  sourceId?: number;
  /** 病例类型 */
  sourceType?: SourceTypeValues;
  /** 来源 */
  from?: 'ocr' | string;
  readonly?: boolean;
  /** 额外数据，用于保存模块数据时携带 */
  additionalData?: Partial<
    Record<FormCategoryValues | 'all', Record<string, any>>
  >;
  /** 额外数据，用于查询数据时携带 */
  queryParams?: Record<'all' | string, Record<string, any>>;
}>();

interface surgeryInfo {
  surgeryTime: string;
  conclusion: string;
  surgeryInfo: string;
  accessory: any;
  surgeryId: string;
  visible: boolean;
  surgeryText: string;
}
const loading = ref(false);
let surgeryRecords = ref<surgeryInfo[]>([
  {
    surgeryId: '',
    surgeryTime: '',
    conclusion: '',
    surgeryInfo: '',
    surgeryText: '',
    visible: false,
    accessory: [],
  },
]);
let currentSurgeryIndex = ref(0);
let choosedSurgeryInfo = ref<any>([]);
let chooseSurgeryVisible = ref(false);
let patientHistoryId = ref<number>(0);

const emits = defineEmits<{
  (e: 'on-status-change', data: any): void;
  (e: 'update-source-id', id: number): void;
}>();

onMounted(() => {
  if (sourceId && from !== 'ocr') {
    getOnlySurgery(sourceId);
    patientHistoryId.value = sourceId;
  }
});

// 编辑
// 编辑人/时间
let editInfo = ref({
  name: '',
  time: '',
});
let msgStatus = ref(actionType);
let editMsg = () => {
  msgStatus.value = 'edit';
  emits('on-status-change', msgStatus.value);
};

// 关闭弹窗
let closeSurgeryVisible = () => {
  chooseSurgeryVisible.value = false;
};

//删除一条手术记录
let deleteSurgery = (item, index: any) => {
  ElMessageBox.confirm('是否删除本条手术记录?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      if (item.surgeryId) {
        deleteOperationApi({ surgeryId: item.surgeryId }).then(res => {
          if (res.code === 'E000000') {
            dialogTip('删除成功！', 'success');
            surgeryRecords.value.splice(index, 1);
          }
        });
      } else {
        dialogTip('删除成功！', 'success');
        surgeryRecords.value.splice(index, 1);
      }
    })
    .catch(() => {});
};

// 选择手术
let chooseSurgery = (index: any) => {
  currentSurgeryIndex.value = index;
  if (
    Array.isArray(
      surgeryRecords.value[currentSurgeryIndex.value].surgeryInfo
    ) &&
    surgeryRecords.value[currentSurgeryIndex.value].surgeryInfo.length
  ) {
    choosedSurgeryInfo.value =
      surgeryRecords.value[currentSurgeryIndex.value].surgeryInfo;
  } else {
    choosedSurgeryInfo.value = [];
  }
  chooseSurgeryVisible.value = true;
};

// 手术时间
let changeTime = () => {};
let pickerOptions = (time: { getTime: () => number }) => {
  return time.getTime() > Date.now();
};

// 新增手术记录
let beforeAddEmptySurgery = () => {
  if (surgeryRecords.value.length === 0) {
    addEmptySurgery();
  } else {
    if (
      surgeryRecords.value[surgeryRecords.value.length - 1].surgeryTime &&
      surgeryRecords.value[surgeryRecords.value.length - 1].conclusion &&
      surgeryRecords.value[surgeryRecords.value.length - 1].surgeryInfo
    ) {
      addEmptySurgery();
    } else {
      ElMessage({
        message: '请填写完成上一条手术记录!',
        type: 'warning',
      });
    }
  }
};
let addEmptySurgery = () => {
  surgeryRecords.value.push({
    surgeryId: '',
    surgeryTime: '',
    surgeryText: '',
    conclusion: '',
    surgeryInfo: '',
    visible: false,
    accessory: [],
  });
};
// 保存
let getSaveParams = () => {
  if (!surgeryRecords.value.length) {
    ElMessage({
      message: '暂无要提交的手术，请添加!',
      type: 'warning',
    });
    return;
  }
  if (
    surgeryRecords.value[surgeryRecords.value.length - 1].surgeryTime &&
    surgeryRecords.value[surgeryRecords.value.length - 1].conclusion &&
    surgeryRecords.value[surgeryRecords.value.length - 1].surgeryInfo
  ) {
    let surgeryList = JSON.parse(JSON.stringify(surgeryRecords.value));

    surgeryList.forEach((item: { surgeryInfo: string }) => {
      item.surgeryInfo = JSON.stringify(item.surgeryInfo);
    });
    /** 患者🆔，实习生端和其他端数据来源不同 */
    const patientId =
      useGlobalInfo.currentRole === RoleEnum.INTERN
        ? internDrawer.patientId
        : useGlobalInfo.userId;
    return {
      group: sourceType === 3 ? 1 : 0,
      caseType: sourceType === 3 ? 0 : sourceType,
      patientId,
      patientHistoryId: sourceId,
      surgeryList,
    };
  } else {
    ElMessage({
      message: '请填写完所有手术记录!',
      type: 'warning',
    });
  }
  return false;
};
const submit = async params => {
  loading.value = true;
  return addDiagnosisSurgeryApi({
    ...(params || {}),
    ...(additionalData?.[FormCategory.OPERATION_RECORD] || {}),
    ...(additionalData?.all || {}),
  })
    .then((res: any) => {
      if (res.code === 'E000000') {
        patientHistoryId.value = res.data;
        ElMessage({
          message: '保存成功',
          type: 'success',
        });
        if (from !== 'ocr') {
          msgStatus.value = 'view';
          emits('update-source-id', res.data);
          bus.emit('updata-review-list');
          bus.emit('refresh-attachment', res.data);
          emits('on-status-change', msgStatus.value);
          getOnlySurgery(res.data);
        }
        return true;
      }
    })
    .catch((err: { msg: any }) => {
      ElMessage.error(`添加失败！${err.msg}`);
    })
    .finally(() => {
      loading.value = false;
    });
};
let saveInfo = () => {
  const res = getSaveParams();
  if (!res) return;
  submit(res);
};

const ocrSaveInfo = (params: any) => {
  const res: any = getSaveParams();
  if (!res) return;
  const surgeryList = res?.surgeryList.map(v => ({
    ...v,
    accessory: params.accessory,
  }));
  const mergeData = { ...params, surgeryList };
  delete mergeData.accessory;
  return submit(mergeData);
};
const setFormData = (data: any) => {
  surgeryRecords.value = data;
};
defineExpose({
  submit: ocrSaveInfo,
  setFormData,
  getSaveData: getSaveParams,
  /** 刷新数据 */
  refresh,
  getData: () => surgeryRecords.value,
});

/**
 * 获取手术详情
 * @param source_id 病例🆔
 */
let getOnlySurgery = async (source_id: number) => {
  /** 额外的参数 */
  const extraParams = {
    ...(queryParams?.[FormCategory.OPERATION_RECORD] || {}),
    ...(queryParams?.all || {}),
  };
  /**
   * 业务逻辑判断
   * 如果是实习生端并且传入了sub_task_id，entry_task的值由接口返回的结果确定
   */
  if (
    extraParams?.['sub_task_id'] &&
    useGlobalInfo.currentRole === RoleEnum.INTERN
  ) {
    extraParams['entry_task'] =
      (await checkTaskContent(extraParams?.['sub_task_id'])) ??
      extraParams?.['entry_task'];
  }

  queryOperationDetailsApi({
    source_id,
    ...extraParams,
  })
    .then((res: any) => {
      if (res.data && res.data.length) {
        let arr = res.data.map(
          (item: {
            surgeryInfo: string;
            accessory: any;
            surgeryTime: string | number | Date;
            surgeryId: any;
            conclusion: any;
            surgeryText: string;
          }) => {
            return {
              surgeryInfo: item.surgeryInfo ? JSON.parse(item.surgeryInfo) : [],
              accessory: item.accessory,
              visible: false,
              surgeryTime: item.surgeryTime
                ? timestampToDate(item.surgeryTime)
                : '--',
              surgeryId: item.surgeryId,
              conclusion: item.conclusion,
              surgeryText: item.surgeryText,
            };
          }
        );
        // 上次编辑人和时间
        editInfo.value = {
          name: res.data[0].userName,
          time: res.data[0].modifyTime
            ? timestampToDateTime(res.data[0].modifyTime)
            : '--',
        };
        surgeryRecords.value = arr;
      } else {
        surgeryRecords.value = [];
      }
    })
    .catch(() => {});
};

// 取消保存
let cancelEdit = () => {
  if (patientHistoryId.value || sessionStorage.getItem('patientHistoryId')) {
    const id = patientHistoryId.value
      ? patientHistoryId.value
      : sessionStorage.getItem('patientHistoryId');
    getOnlySurgery(id as number);
  } else {
    surgeryRecords.value = [];
    addEmptySurgery();
  }
  msgStatus.value = 'view';
  emits('on-status-change', msgStatus.value);
};

/**
 * 刷新数据
 */
function refresh() {
  const sourceId =
    patientHistoryId.value || sessionStorage.getItem('patientHistoryId');
  if (patientHistoryId.value || sessionStorage.getItem('patientHistoryId')) {
    getOnlySurgery(Number(sourceId));
  }
}

//获取手术结论
let getSurgeryConclusion = (conclusion: any[]) => {
  surgeryRecords.value[currentSurgeryIndex.value].conclusion =
    conclusion.join(';');
};
//获取手术信息参数
let getParams = (params: any) => {
  surgeryRecords.value[currentSurgeryIndex.value].surgeryInfo = params;
};
</script>
<style scoped lang="less">
.outpatient-record-box {
  position: relative;
  .edit-box {
    position: absolute;
    right: 16px;
    top: 18px;
  }
}
.edit-content {
  .edit-item {
    .item-content {
      flex: 1;
      .sd-box {
        .content-box {
          .query-style {
            font-size: 14px;
            color: #3a4762;
          }
          .title {
            font-size: 16px;
            font-weight: 600;
            color: #15233f;
          }
          .box {
            background: #f7f8fa;
            border-radius: 4px;
            padding: 12px;
            box-sizing: border-box;
          }
          .total-label {
            font-size: 16px;
            font-weight: bold;
            color: #111111;
            .delete-btn {
              color: #de4747;
              font-size: 14px;
            }
          }
          .row-item {
            display: flex;
            margin-bottom: 16px;
            .date-box {
              position: relative;
              :deep(.datepicker) {
                .el-input__prefix {
                  display: none;
                }
              }
              .change-time-icon {
                position: absolute;
                top: 8px;
                right: 14px;
              }
            }
            .row-label {
              font-size: 14px;
              font-weight: 700;
              color: #111111;
              margin-right: 16px;
              width: 92px;
              text-align: right;
            }
            .choose-text {
              font-size: 14px;
              font-weight: 400;
              color: #0a73e4;
              cursor: pointer;
            }
            :deep(.input-box) {
              flex: 1;
              box-sizing: border-box;
              min-height: 96px;
              padding: 6px 16px;
              border: 1px solid #dcdfe6;
              font-size: 14px;
              font-family:
                PingFangSC-Regular,
                PingFang SC,
                'sans-serif';
              font-weight: 400;
              color: #203549;
              background-color: #efefef;
              border-radius: 2px;
              line-height: 18px;
              .con-item:not(:first-child) {
                margin-top: 8px;
              }
            }
          }
          .margin-b {
            margin-bottom: 16px;
          }
          .flex-v-center {
            align-items: center;
          }
        }
      }
    }
  }
}
.btn-group {
  margin-left: 116px;
  display: flex;
  margin-bottom: 20px;
  > button {
    width: 76px;
  }
}
.add-btn {
  font-size: 14px;
  color: #1c81dc;
}
.tag {
  margin-top: 8px;
  display: inline-block;
  padding: 2px 4px;
  font-size: 14px;
  font-weight: 400;
  color: #0a73e4;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #dcdee0;
}
.struct-btn {
  position: absolute;
  right: 12px;
  top: 60px;
  font-size: 12px;
  font-weight: 400;
  color: #fff;
  box-sizing: border-box;
  padding: 4px 12px;
  background-color: #0a73e4;
  border-radius: 2px;
  cursor: pointer;
}
</style>
