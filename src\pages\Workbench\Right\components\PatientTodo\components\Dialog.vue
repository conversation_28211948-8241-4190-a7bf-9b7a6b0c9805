<template>
  <div>
    <HrtDialog
      v-model="dialogVisibleTodo"
      :title="title"
      :width="props.width"
      class="todo-dialog"
      @close="close"
    >
      <span>
        <!-- 添加待办 -->
        <AddTodo v-if="useTodoInfo.status === 1" @close="close" />
        <!-- 延期待办 -->
        <PostponeTodo v-if="useTodoInfo.status === 2" @close="close" />
        <!-- 入组宣教、复查提醒、随访提醒 -->
        <GroupMission
          v-if="
            useTodoInfo.status === 3 ||
            useTodoInfo.status === 4 ||
            useTodoInfo.status === 9 ||
            useTodoInfo.status === 11 ||
            useTodoInfo.status === 999
          "
          @close="close"
        />
        <!-- 调药跟踪、门诊跟踪、住院跟踪 -->
        <TrackDown
          v-if="
            useTodoInfo.status === 6 ||
            useTodoInfo.status === 7 ||
            useTodoInfo.status === 8
          "
          @close="close"
        />
      </span>
    </HrtDialog>
  </div>
</template>
<script setup lang="ts">
import { todoTitle } from '../index';
import AddTodo from './AddTodo.vue';
import { HrtDialog } from '@hrt/components';
import PostponeTodo from './PostponeTodo.vue';
import GroupMission from './GroupMission.vue';
import TrackDown from './TrackDown.vue';
const dialogVisibleTodo = ref<boolean>(false);
const title = ref<string>('');

const props = defineProps({
  width: {
    default: '30%',
    type: String,
  },
  dialogVisible: {
    default: false,
    type: Boolean,
  },
});
import useTodo from '@/store/module/useTodo';
const useTodoInfo = useTodo();

watch(
  () => props.dialogVisible,
  newData => {
    todoTitle.value.forEach((item: any) => {
      if (item.type === useTodoInfo.status) title.value = item.title;
    });

    dialogVisibleTodo.value = newData;
  },
  { immediate: true, deep: true }
);

// 关闭弹窗
const emit = defineEmits(['closeDialog']);
const close = () => {
  emit('closeDialog');
};
</script>
<style scoped lang="less">
:deep(.todo-dialog) {
  .el-dialog__header {
    padding: 12px 24px;
    border-bottom: 2px solid #e9e8eb;
    margin-right: 0;
    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
  }
  .el-dialog__headerbtn {
    top: 0;
  }
  .el-dialog__body {
    padding: 16px;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    .sure-btn {
      background: #2e6be6;
      border-radius: 2px;
      border-color: #2e6be6;
    }
    .cancel-btn {
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
