<template>
  <div class="flex-1">
    <CustomTabs group="DigitalPortrait" />
  </div>
</template>

<script lang="ts" setup>
import CustomTabs from '@/pages/Workbench/Main/components/CustomTabs.vue';
import Graph from './Graph.vue';
import store from '@/store';
const tabs = store.useTabs();

watchEffect(() => {
  if (tabs.mainActiveTab !== 6) return;
  tabs.addTab({
    name: '数字画像',
    group: 'DigitalPortrait',
    component: Graph,
    mainTabCode: 6,
    changeMainTab: false,
    disableCache: true,
    closeable: false,
  });
});
</script>

<style scoped></style>
