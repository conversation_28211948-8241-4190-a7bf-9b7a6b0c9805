export enum messageType {
  /**
   * 患者消息
   */
  PATIENT_CHAT_RECORD = 'PATIENT_CHAT_RECORD',

  /**
   * 专家消息
   */
  EXPERT_CHAT_RECORD = 'EXPERT_CHAT_RECORD',

  /**
   * 团队消息
   */
  TEAM_CHAT_RECORD = 'TEAM_CHAT_RECORD',

  /**
   * 患者新入组
   */
  PATIENT_ENROLLMENT = 'PATIENT_ENROLLMENT',

  /**
   * 患者续费
   */
  PATIENT_RENEW = 'PATIENT_RENEW',

  /**
   * 血压风险异常
   */
  BLOOD_PRESSURE_RISK = 'BLOOD_PRESSURE_RISK',

  /**
   * 心率风险异常
   */
  HEART_RISK = 'HEART_RISK',

  /**
   * 血糖风险异常
   */
  BLOOD_SUGAR_RISK = 'BLOOD_SUGAR_RISK',

  /**
   * 体重异常
   */
  WEIGHT_RISK = 'WEIGHT_RISK',

  /**
   * 患者症状异常
   */
  PATIENT_ABNORMAL_SYMPTOMS = 'PATIENT_ABNORMAL_SYMPTOMS',

  /**
   * 入组资料上传
   */
  INCLUDED_DATA_UPLOADED = 'INCLUDED_DATA_UPLOADED',

  /**
   * 复查资料上传
   */
  REVIEW_REPORT_UPLOADED = 'REVIEW_REPORT_UPLOADED',

  /**
   * 入组资料转录完成
   */
  INCLUDED_DATA_TRANSCRIPTION = 'INCLUDED_DATA_TRANSCRIPTION',

  /**
   * 门诊资料转录完成
   */
  OUTPATIENT_RECORD_TRANSCRIPTION = 'OUTPATIENT_RECORD_TRANSCRIPTION',

  /**
   * 住院资料转录完成
   */
  HOSPITALIZATION_RECORD_TRANSCRIPTION = 'HOSPITALIZATION_RECORD_TRANSCRIPTION',

  /**
   * 复查报告转录完成
   */
  REVIEW_REPORT_TRANSCRIPTION = 'REVIEW_REPORT_TRANSCRIPTION',

  /**
   * 动态血压监测成功
   */
  AMBULATORY_BLOOD_PRESSURE_SUCCESS = 'AMBULATORY_BLOOD_PRESSURE_SUCCESS',
  /**
   * 动态血压监测失败
   */
  AMBULATORY_BLOOD_PRESSURE_FAILURE = 'AMBULATORY_BLOOD_PRESSURE_FAILURE',
  /**
   * 患者加入运动管理
   */
  PATIENT_JOIN_SPORT_MANAGE = 'PATIENT_JOIN_SPORT_MANAGE',
  /**
   * OCR识别成功
   */
  OCR_IDENTIFY_SUCCESS = 'OCR_IDENTIFY_SUCCESS',

  /**
   * OCR识别失败
   */
  OCR_IDENTIFY_FAILURE = 'OCR_IDENTIFY_FAILURE',
  /**
   * Ai 推荐对话
   */
  RECOMMEND_CONVERSATION = 'RECOMMEND_CONVERSATION',
  /**
   * Ai 推荐对话刷新消息
   */
  RECOMMEND_CONVERSATION_REFRESH = 'RECOMMEND_CONVERSATION_REFRESH',
  /**
   * 患者转入工作室
   */
  PATIENT_GROUP_IN = 'PATIENT_GROUP_IN',
  /**
   * 患者转出工作室
   */
  PATIENT_GROUP_OUT = 'PATIENT_GROUP_OUT',
  /**
   * 入院信息更新
   */
  PATIENT_ADMISSION_UPDATE = 'PATIENT_ADMISSION_UPDATE',
  /**
   * 科研患者加入
   */
  SCIENTIFIC_RESEARCH_PATIENT_ENROLLMENT = 'SCIENTIFIC_RESEARCH_PATIENT_ENROLLMENT',
  /**
   * 批量OCR识别成功
   */
  BATCH_OCR_IDENTIFY_SUCCESS = 'BATCH_OCR_IDENTIFY_SUCCESS',
}

type messageTypesMapType = {
  [key: number]: string[];
};

export const messageTypesMap: messageTypesMapType = {
  /*全部*/
  0: [],
  /*  聊天消息：患者消息、专家消息、团队消息*/
  1: [
    messageType.PATIENT_CHAT_RECORD,
    messageType.EXPERT_CHAT_RECORD,
    messageType.TEAM_CHAT_RECORD,
  ],
  /*患者动态：患者入组、患者续费、动态血压监测成功、患者转入、患者转出、患者加入运动管理、患者二期住院更新*/
  2: [
    messageType.PATIENT_ENROLLMENT,
    messageType.PATIENT_RENEW,
    messageType.AMBULATORY_BLOOD_PRESSURE_SUCCESS,
    messageType.PATIENT_GROUP_IN,
    messageType.PATIENT_GROUP_OUT,
    messageType.PATIENT_JOIN_SPORT_MANAGE,
    messageType.PATIENT_ADMISSION_UPDATE,
    messageType.SCIENTIFIC_RESEARCH_PATIENT_ENROLLMENT,
  ],
  /*风险提醒：风险事件、症状异常*/
  3: [
    messageType.BLOOD_PRESSURE_RISK,
    messageType.BLOOD_SUGAR_RISK,
    messageType.HEART_RISK,
    messageType.WEIGHT_RISK,
    messageType.PATIENT_ABNORMAL_SYMPTOMS,
  ],
  /* 进度通知：入组资料资料上传、复查报告资料上传、资料转录完成（入组、门诊、住院、复查）*/
  4: [
    messageType.INCLUDED_DATA_UPLOADED,
    messageType.REVIEW_REPORT_UPLOADED,
    messageType.INCLUDED_DATA_TRANSCRIPTION,
    messageType.OUTPATIENT_RECORD_TRANSCRIPTION,
    messageType.HOSPITALIZATION_RECORD_TRANSCRIPTION,
    messageType.REVIEW_REPORT_TRANSCRIPTION,
  ],
};
