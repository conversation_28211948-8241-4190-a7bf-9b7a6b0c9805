<template>
  <div class="forget-password" :class="{ 'change-height': next }">
    <div class="title flex-c">
      {{ next ? '输入新密码' : '找回密码' }}
    </div>
    <div class="main" :class="{ 'change-padding': next }">
      <div v-if="!next" class="go-back">
        已想起密码，<span class="back" @click="goBack">返回登录界面</span>
      </div>
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rulesCode"
        class="demo-ruleForm password-form"
      >
        <el-form-item v-if="!next" prop="account">
          <div class="inputBox">
            <img
              src="@/assets/imgs/login/phone-number.png"
              alt=""
              class="img"
            />
            <el-input
              v-model.number="ruleForm.account"
              placeholder="请输入手机号"
              clearable
              maxlength="11"
              oninput="value = value.replace(/\s+/g,'')"
            />
          </div>
        </el-form-item>
        <el-form-item v-if="!next" prop="password">
          <div class="inputBox">
            <img src="@/assets/imgs/login/password.png" alt="" class="img" />
            <el-input
              v-model="ruleForm.password"
              placeholder="请输入验证码"
              clearable
              oninput="value = value.replace(/\s+/g,'')"
              maxlength="6"
            />

            <el-button
              class="forgot-password"
              :disabled="getCodeDisabled"
              type="text"
              @click="handle"
              >{{ codeBtnWord }}</el-button
            >
          </div>
        </el-form-item>
        <el-form-item v-if="next" prop="code" class="change-password">
          <div class="inputBox">
            <img
              src="@/assets/imgs/login/verification-code.png"
              alt=""
              class="img"
            />
            <el-input
              v-model="ruleForm.code"
              placeholder="请输入新密码"
              clearable
              oninput="value = value.replace(/\s+/g,'')"
              show-password
              minlength="8"
            />
          </div>
        </el-form-item>
        <el-form-item :class="{ 'change-password': next }">
          <el-button
            v-if="!next"
            type="primary"
            class="login-button"
            @click="submitForm(ruleFormRef)"
          >
            下一步
          </el-button>
          <div v-if="next" class="btns-box flex-bc">
            <div class="cancel flex-c" @click="goBack">取消</div>
            <div class="sure flex-c ml-24" @click="sure()">确定</div>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { FormInstance } from 'element-plus';

interface RuleForm {
  account?: string;
  password?: string;
  code: string;
}

const ruleFormRef = ref();
const ruleForm = reactive<RuleForm>({
  account: '',
  password: '',
  code: '',
});

// 验证验证码登录
const rulesCode = reactive({
  account: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
});

// 验证使用手机号验证码登录的信息是否正确
let verifyMsg = () => {
  let regex = /^1[3-9]\d{9}$/;
  if (!ruleForm.account) {
    ElMessage({
      showClose: true,
      message: '请输入手机号！',
      type: 'error',
    });
    return false;
  }
  if (!Number(ruleForm.account) || !regex.test(ruleForm.account)) {
    ElMessage({
      showClose: true,
      message: '请输入正确的手机号！',
      type: 'error',
    });
    return false;
  }
  return true;
};

// 获取验证码
let getCodeDisabled = ref<boolean>(false);
let codeBtnWord = ref<string>('获取验证码');
import { getCode, verifyMsgCode, updatePassword } from '@/api/login';
onUnmounted(() => {
  document.onkeydown = false;
});
// 获取验证码按钮失效时间
let countDownTime = ref<number>(60);
let timer = ref();
// 发送验证码倒计时逻辑
let countDown = () => {
  let startTime = localStorage.getItem('startTimeLoginAssistant');
  let nowTime = new Date().getTime();
  if (startTime) {
    let surplus = 60 - parseInt((nowTime - startTime) / 1000, 10);
    countDownTime.value = surplus <= 0 ? 0 : surplus;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
  } else {
    countDownTime.value = 60;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
    localStorage.setItem('startTimeLoginAssistant', nowTime);
  }
  timer.value = setInterval(() => {
    countDownTime.value--;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
    getCodeDisabled.value = true;
    if (countDownTime.value <= 0) {
      localStorage.removeItem('startTimeLoginAssistant');
      clearInterval(timer.value);
      countDownTime.value = 60;
      codeBtnWord.value = '获取验证码';
      getCodeDisabled.value = false;
    }
  }, 1000);
};

let handle = () => {
  // 获取验证码
  if (verifyMsg()) {
    countDown();
    getCode({ loginAccount: ruleForm.account, type: 2 }).then(res => {
      if (res.code === 'E000000') {
        ElMessage({
          message: '验证码发送成功,请注意查收！',
          type: 'success',
        });
      } else {
        ElMessage.error(res.message);
      }
    });
  }
};

let next = ref<boolean>(false);
// 下一步
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any) => {
    if (valid) {
      if (!verifyMsg()) {
        return false;
      }
      // 如果是账号密码登录，需要验证手机号、验证码格式（只能为数字）是否正确
      if (!Number(ruleForm.password)) {
        ElMessage({
          showClose: true,
          message: '验证码格式错误，应为6位数字！',
          type: 'error',
        });
        return false;
      }
      // 校验短信验证码
      verifyMsgCode({
        loginAccount: ruleForm.account,
        verifyCode: ruleForm.password,
      }).then(res => {
        if (res.code === 'E000000') {
          next.value = true;
        } else {
          return ElMessage.error(res.message);
        }
      });
    } else {
      ElMessage.error('请填写完整！');
    }
  });
};

// 返回
let goBack = () => {
  emit('backLogin');
  ruleForm.account = '';
  ruleForm.password = '';
  ruleForm.code = '';
  next.value = false;
};

// 正则验证密码输入规则
let validatePassword = (password: string) => {
  const regex =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,15}$/;
  return regex.test(password);
};

// 确定
const emit = defineEmits(['backLogin']);
let sure = () => {
  if (!ruleForm.code) {
    ElMessage({
      showClose: true,
      message: '请输入新密码！',
      type: 'error',
    });
  } else if (!validatePassword(ruleForm.code)) {
    ElMessage({
      showClose: true,
      message:
        '密码格式错误，正确应为：8-15位数的大小写英文、数字及特殊符号任意三种！',
      type: 'error',
    });
  } else {
    console.log('确定事件');
    updatePassword({
      loginAccount: ruleForm.account,
      password: ruleForm.code,
    }).then(res => {
      if (res.code === 'E000000') {
        ElMessage({
          message: '密码修改成功,请重新登录！',
          type: 'success',
        });
        goBack();
      } else {
        ElMessage.error(res.message);
      }
    });
  }
};
</script>
<style scoped lang="less">
.forget-password {
  width: 72%;
  height: 450px;
  background: #ffffff;
  border-radius: 6px;
  .title {
    font-size: 32px;
    font-weight: bold;
    color: #ffffff;
    height: 76px;
    background: #2e6be6;
    border-radius: 6px 6px 0px 0px;
  }
  .main {
    padding: 32px 50px;
    box-sizing: border-box;
    .go-back {
      display: flex;
      justify-content: flex-end;
      font-size: 14px;
      color: #3a4762;
      margin-bottom: 24px;
      .back {
        color: #2e6be6;
        cursor: pointer;
      }
    }
    :deep(.demo-ruleForm) {
      width: 72%;
      .inputBox {
        width: 100%;
        display: flex;
        border-bottom: 2px solid #e9e8eb;
        padding: 8px 0;
        align-items: center;
        .img {
          width: 22px;
          height: 22px;
          margin-left: 14px;
          margin-right: 22px;
        }
        .el-input {
          flex: 1;
        }
        .forgot-password {
          font-size: 14px;
          color: #2e6be6;
          margin-right: 24px;
          cursor: pointer;
        }
      }

      .el-input__wrapper,
      .is-focus {
        box-shadow: none !important;
      }
      .change-login-type {
        margin-top: -10px;
        margin-bottom: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .is-checked {
          .el-checkbox__inner {
            background: #2e6be6;
            border-color: #2e6be6;
          }
          .el-checkbox__label {
            color: #203549;
          }
        }
        .code-login {
          font-size: 14px;
          color: #2e6be6;
          cursor: pointer;
        }
      }

      .login-button {
        width: 100%;
        height: 61px;
        background: #2e6be6;
        border-radius: 4px;
        font-size: 24px;
        color: #ffffff;
      }
    }
    :deep(.password-form) {
      width: 100% !important;
      .change-password {
        margin-top: 90px;
      }
      .el-form-item {
        margin-bottom: 36px;
      }
    }
    .btns-box {
      width: 100%;
      .cancel {
        width: 48%;
        height: 61px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dcdee0;
        box-sizing: border-box;
        font-size: 24px;
        color: #3a4762;
        cursor: pointer;
      }
      .sure {
        width: 48%;
        height: 61px;
        background: #2e6be6;
        border-radius: 4px;
        color: #ffffff;
        cursor: pointer;
        font-size: 24px;
      }
    }
  }
  .change-padding {
    padding: 0 50px;
  }
}
.change-height {
  height: 420px;
}
</style>
