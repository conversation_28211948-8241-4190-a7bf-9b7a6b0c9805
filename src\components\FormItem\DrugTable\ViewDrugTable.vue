<script setup lang="ts">
import { Drug } from '@/components/DrugInfo/components/types';
import { medicineTimeList } from '@/components/DrugInfo/components/constants';
import { keyBy } from 'lodash-es';
export type ViewDrugTableProps = {
  /** 药品数据 */
  paramsData: Drug[] | null;
};

defineOptions({
  name: 'ViewDrugTable',
});
const timeListMap = keyBy(medicineTimeList, 'value');
const { paramsData = [] } = defineProps<ViewDrugTableProps>();
</script>

<template>
  <el-table
    :data="paramsData || []"
    style="width: 100%"
    header-row-class-name="head-class"
  >
    <el-table-column type="index" label="序号" width="60" />
    <el-table-column
      prop="drug_name"
      label="药品名称"
      align="left"
      width="180"
    />
    <el-table-column
      prop="drugSpecStr"
      label="药品规格"
      align="left"
      width="110"
    >
      <template #default="{ row }">
        <div>
          {{ row.drug_spec.ingredients }}/{{ row.drug_spec.content_unit }}
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="drug_amount"
      align="left"
      width="110"
      label="单次用量"
    >
      <template #default="{ row }">
        <div>
          {{ row.drug_amount.ingredients }} /{{ row.drug_amount.content_unit }}
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="drug_usage" align="left" label="频率" width="120" />
    <el-table-column
      prop="medicine_time"
      align="left"
      label="服药时间"
      width="100"
    >
      <template #default="{ row }">
        <div>
          {{ timeListMap[row.medicine_time]?.label }}
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="drug_mode" align="left" label="用法" width="120" />
  </el-table>
</template>
