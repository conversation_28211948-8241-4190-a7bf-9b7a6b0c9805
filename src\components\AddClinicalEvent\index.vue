<template>
  <div class="content">
    <div class="main-content">
      <EventTab
        ref="eventAdd"
        v-model:active-tab="activeTab"
        :curr-choose-event="props.currChooseEvent"
        :user-id="userId"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import EventTab from './EventTab.vue';
interface IProps {
  visible?: boolean;
  userId: number | string;
  currChooseEvent?: object;
}
const props = withDefaults(defineProps<IProps>(), {
  visible: false,
  modal: true,
  currChooseEvent: {},
});
const activeTab = ref(props.currChooseEvent.clinicalType ?? 1);
const eventAdd = ref();
const onClose = () => {
  return { params: eventAdd.value.dealSubmitData() };
};

defineExpose({ onClose });

watch(
  () => props.currChooseEvent,
  () => {
    activeTab.value = props.currChooseEvent.clinicalType;
  }
);
</script>

<style lang="less" scoped>
.title {
  font-size: 16px;
  font-weight: bold;
  color: #101b25;
  border-bottom: 1px solid #e9e8eb;
  padding: 24px 24px 16px 24px;
}
.intelligentBtnBox {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9e8eb;
  .cancelBtn {
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #606266;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
  }
  .sureBtn {
    width: 76px;
    height: 32px;
    background: #0a73e4;
    border-radius: 2px;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 16px;
  }
}
.content {
  .head {
    box-sizing: border-box;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9e8eb;
    .label {
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
    }
    .close-icon {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .main-content {
    box-sizing: border-box;
    padding: 0 24px;
    min-height: 500px;
  }
}
</style>
