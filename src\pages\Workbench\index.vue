<template>
  <div class="h-full flex flex-col bg-[#F6F8FB]">
    <WorkBenchHeader @information="isShow = true" />
    <div v-if="isShow"><Information @go-index="isShow = false" /></div>
    <div v-if="!isShow" class="flex flex-1">
      <WorkBenchLeft />
      <WorkBenchMain :key="globalStore.rerenderKey" />
      <WorkBenchRight />
    </div>

    <!--  欢迎页面  -->
    <div v-if="isWelcome" class="welComePage">
      <img
        src="@/assets/imgs/welcome.png"
        alt=""
        class="welcomeImg"
        @click="closeWelcome"
      />
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="提示"
      width="30%"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <span>请完善互联网医院“医师备案”相关信息！</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBtn()">关 闭</el-button>
          <el-button type="primary" @click="sureBtn()">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <Doubt v-model="globalStore.isDoubting" />
  </div>
</template>

<script setup lang="ts">
import WorkBenchLeft from '@/pages/Workbench/Left/index.vue';
import WorkBenchMain from '@/pages/Workbench/Main/index.vue';
import WorkBenchRight from '@/pages/Workbench/Right/index.vue';
import Information from '@/pages/Information/index.vue';
import { onMounted } from 'vue';
import { queryPhysicianRegister } from '@/api/login';
import useGlobal from '@/store/module/useGlobal';
import { useComponentsTab } from './hooks/useComponentsTab';
import useUserStore from '@/store/module/useUserStore';
import { useLocalStorage } from '@vueuse/core';
import { useFormJSONSchema } from '@/hooks/useFormJSONSchema';
import Doubt from '@/pages/Workbench/Right/components/Doubt/index.vue';
const WorkBenchHeader = defineAsyncComponent(
  () => import('@/pages/Workbench/Header/index.vue')
);
useFormJSONSchema();
useComponentsTab();
const globalStore = useGlobal();
const userStore = useUserStore();
// 控制显示备案信息还是首页  true--备案  false--首页
let isShow = ref<boolean>(false);

// 医师备案
let dialogVisible = ref<boolean>(false);
const userRoles = userStore.userRoles;
let currentRole = userRoles?.[0];
if (userRoles?.length) {
  globalStore.setUserRoles(userRoles as any);
}

// 关闭
let closeBtn = () => {
  dialogVisible.value = false;
  useLocalStorage('isPopup', false).value = false;
};
// 确认
let sureBtn = () => {
  dialogVisible.value = false;
  isShow.value = true;
  useLocalStorage('isPopup', false).value = false;
};

// 查询医助是否完善互联网医师备案
let queryPhysician = () => {
  queryPhysicianRegister()
    .then(res => {
      if (res.code === 'E000000') {
        if (!res.data) dialogVisible.value = !res.data;
      }
    })
    .catch(() => {});
};

// 判断是否要显示欢迎页
let isWelcome = ref<boolean>(true);
let welcomeFlag = ref<string>('1');
let timer = ref<any>(null);

onMounted(() => {
  // 欢迎页逻辑处理   0--未显示  1--已显示
  welcomeFlag.value = sessionStorage.getItem('isWelcome') ? '1' : '0';

  if (welcomeFlag.value === '0') {
    isWelcome.value = true;
    timer.value = setTimeout(() => {
      isWelcome.value = false;
      sessionStorage.setItem('isWelcome', '1');
      let isPopup = userStore.isPopup;
      if (
        !location.href.includes('/archivalInformation') &&
        isPopup &&
        currentRole === 'ASSISTANT'
      ) {
        queryPhysician();
      }
    }, 3000);
  } else {
    isWelcome.value = false;
    let isPopup = userStore.isPopup;
    if (
      !location.href.includes('/archivalInformation') &&
      isPopup &&
      currentRole === 'ASSISTANT'
    ) {
      queryPhysician();
    }
  }
});

// 点击关闭欢迎页
let closeWelcome = () => {
  isWelcome.value = false;
  sessionStorage.setItem('isWelcome', '1');
  clearTimeout(timer.value);
  let isPopup = userStore.isPopup;
  if (
    !location.href.includes('/archivalInformation') &&
    isPopup &&
    currentRole === 'ASSISTANT'
  ) {
    queryPhysician();
  }
};
</script>
<style scoped lang="less">
.welComePage {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  .welcomeImg {
    width: 62.5%;
    cursor: pointer;
  }
}
</style>
