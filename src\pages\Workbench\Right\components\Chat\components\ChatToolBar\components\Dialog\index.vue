<template>
  <el-dialog
    v-model="dialogVisible"
    destroy-on-close
    :width="width ?? 900"
    class="chat-common-dialog"
    draggable
    :modal="false"
    @close="close"
  >
    <template #header="{ titleId, titleClass }">
      <div :id="titleId" :class="titleClass">
        <slot name="header"></slot>
      </div>
    </template>
    <div class="chat-dialog-content">
      <el-scrollbar max-height="500px">
        <slot></slot>
      </el-scrollbar>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
interface IProps {
  width?: number;
  visible: boolean;
}
defineOptions({
  name: 'ChatDialog',
});
const props = defineProps<IProps>();
const emits = defineEmits(['close']);
const dialogVisible = ref(false);

const close = () => {
  emits('close');
};
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);
</script>

<style lang="less">
.chat-common-dialog {
  padding: 0;
  .el-dialog__header {
    background: linear-gradient(
      180deg,
      #abc2ff 0%,
      rgba(255, 255, 255, 0.3) 100%
    );
    height: 54px;
    margin: 0;
    padding: 16px;
    .el-dialog__title {
      display: flex;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: bold;
      font-size: 16px;
      color: #203549;
      align-items: center;
      > img {
        width: 20px;
        height: 18px;
        margin-right: 8px;
      }
    }
  }
  .el-dialog__headerbtn {
    top: 0;
  }
  .el-dialog__body {
    padding: 0;
    .chat-dialog-content {
      max-height: 532px;
      padding: 16px 24px;
    }
  }
}
</style>
