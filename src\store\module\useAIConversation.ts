import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import {
  confirmRespond,
  getLatestConversation,
  getRecommendConversation,
  updateConversation,
} from '@/api/recommendConversation';
import useGlobal from './useGlobal';
interface IState {
  /** AI对话填入聊天窗口消息 */
  aiChatMsg?: string;
  /** 当前患者是否存在AI推荐信息 */
  hasAIMessage?: boolean;
  /** 当前患者聊天窗口是否展示横幅 */
  showAIBanner: boolean;
  /** 当前患者应用横幅的推荐对话 */
  activeAiConversation: {
    // 消息内容
    outputDialogue: string;
    // 消息id
    recommendId: number;
    patientId: number;
  };
  /** 获取消息总数（按患者分组） */
  chatCount?: number;
}

const useAIConversation = defineStore(STORE_NAMES.AI_CONVERSATION, {
  state: () =>
    ({
      chatCount: undefined,
      showAIBanner: false,
      activeAiConversation: {
        // 消息内容
        outputDialogue: '',
        // 消息id
        recommendId: 0,
        patientId: 0,
      },
    }) as IState,
  actions: {
    /** 设置当前患者应用横幅的推荐对话 */
    setActiveAiConversation(data?: IState['activeAiConversation']) {
      if (data && data?.patientId === useGlobal().userId) {
        this.hasAIMessage = true;
        this.activeAiConversation = data;
      } else {
        this.hasAIMessage = false;
        this.activeAiConversation = {
          outputDialogue: '',
          recommendId: 0,
          patientId: 0,
        };
      }
    },
    /** 清除当前患者所有AI推荐信息 */
    async removeConversationMsg(patientId?: number) {
      if (!this.hasAIMessage) return;
      await updateConversation({
        patientId: patientId ?? useGlobal().userId!,
        confirmType: 'IGNORED',
      });
      this.setActiveAiConversation();
      this.getRecommendConversationTotal();
      return true;
    },
    /** 更新消息总数 */
    async getRecommendConversationTotal() {
      const resp = await getRecommendConversation({
        pageSize: 1,
        pageNumber: 1,
      });
      this.chatCount = resp?.totalMsg || undefined;
    },
    /** 处理单调数据 */
    async handleAiRespond(data: {
      recommendId: number;
      confirmType: 'IGNORED' | 'CONFIRMED' | 'REPLIED';
    }) {
      const { recommendId, confirmType } = data;
      if (!recommendId) return;
      await confirmRespond({ id: recommendId, confirmType });
      if (recommendId === this.activeAiConversation.recommendId) {
        this.activeAiConversation = {
          outputDialogue: '',
          recommendId: 0,
          patientId: 0,
        };
      }
      this.getRecommendConversationTotal();
    },
    /** 获取最近一次推荐对话 */
    async getLatestConversationData() {
      const patientId = useGlobal().userId!;
      const res = await getLatestConversation({
        patientId,
      });
      this.setActiveAiConversation(
        res
          ? {
              recommendId: res.recommendId!,
              outputDialogue: res.outputDialogue || '',
              patientId,
            }
          : undefined
      );
    },
  },
});

export default useAIConversation;
