export interface CalenderWeekItem {
  //当前日期中文 X月X日 W
  listDate?: string;
  //是否为当日
  now?: boolean;
  //当前日期 YYYY-MM-DD
  sendDate?: string;
  //当前多少号
  showDate?: string;
  //当前日所在周几
  week?: string;
}

export interface CalenderWeekState {
  //当前日历渲染数据
  currentCalenderWeekList: CalenderWeekItem[][];
  //选中的日期
  checkedDate: string | null;
  //每周当前第一天
  currentFirstDate: Date | null;
  //头部日历时间
  headLabelDate: string;
  swiperRef: any;
}
