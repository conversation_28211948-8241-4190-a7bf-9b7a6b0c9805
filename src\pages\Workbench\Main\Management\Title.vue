<template>
  <div class="top flex items-center justify-between">
    <div class="base-msg flex items-center justify-center">
      <div class="avatar flex items-center justify-center">
        {{ baseInfo.patientName.slice(0, 1) }}
      </div>
      <div class="base-box ml-8">
        <span class="name">{{ baseInfo.patientName }}</span>
        <el-divider direction="vertical" />
        <span class="sex">{{
          baseInfo.gender === 1 ? '男' : baseInfo.gender === 2 ? '女' : '未知'
        }}</span>
        <el-divider direction="vertical" />
        <span class="sex">{{ baseInfo.age || '--' }}岁</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();

// 获取基本信息
let baseInfo = ref({
  patientName: '',
  gender: 1,
  age: 0,
  inGroup: 0,
});
let getBaseMsg = () => {
  baseInfo.value.inGroup = useGlobalInfo.userInfo.inGroup;
  baseInfo.value.patientName = useGlobalInfo.userInfo.patientName;
  baseInfo.value.gender = useGlobalInfo.userInfo.gender;
  baseInfo.value.age = useGlobalInfo.userInfo.age;
};

watch(
  () => useGlobalInfo.userInfo,
  () => {
    getBaseMsg();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style scoped lang="less">
.top {
  padding: 16px;
  background: #ffffff;
  .base-msg {
    .avatar {
      width: 30px;
      height: 30px;
      background: #ffffff;
      border: 1px solid #efefef;
      font-size: 16px;
      font-weight: 600;
      color: #2e6be6;
      border-radius: 50%;
    }
    .base-box {
      :deep(.el-divider--vertical) {
        height: 12px;
      }
      .name {
        font-size: 14px;
        font-weight: 600;
        color: #3a4762;
      }
      .sex {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
}
</style>
