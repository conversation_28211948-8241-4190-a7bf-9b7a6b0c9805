<template>
  <div ref="containerRef" class="smart-clamp-container">
    <!-- 主内容容器 -->
    <div
      ref="contentRef"
      class="content-box"
      :class="{ 'is-clamped': !isExpanded && showToggle }"
      :style="contentStyle"
    >
      <slot></slot>
    </div>

    <!-- 折叠按钮 -->
    <div
      v-if="showToggle"
      class="toggle-box"
      :class="{ 'is-expanded': isExpanded }"
    >
      <button class="toggle-btn" @click="toggleExpanded">
        {{ isExpanded ? '收起' : '展开' }}
        <svg class="toggle-icon" viewBox="0 0 24 24">
          <path :d="isExpanded ? 'M7 14l5-5 5 5z' : 'M7 10l5 5 5-5z'" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue';

const props = withDefaults(
  defineProps<{
    maxLines?: number; // 最大行数（默认2行）
    lineHeight?: number; // 明确行高（推荐对非文本内容设置）
    transitionDuration?: number; // 动画时长（ms）
  }>(),
  {
    maxLines: 2,
    lineHeight: undefined,
    transitionDuration: 300,
  }
);

const emit = defineEmits(['expand-change']);

const containerRef = ref<HTMLElement | null>(null);
const contentRef = ref<HTMLElement | null>(null);
const isExpanded = ref(false);
const showToggle = ref(false);
const calculatedMaxHeight = ref(0);
const calculatedContentHeight = ref(0);

// 动态计算内容样式
const contentStyle = computed(() => ({
  'max-height': isExpanded.value
    ? `${calculatedContentHeight.value}px`
    : `${calculatedMaxHeight.value}px`,
  '--transition-duration': `${props.transitionDuration}ms`,
  '--max-lines': props.maxLines,
}));

// 核心检测逻辑
const checkClampNeeded = async () => {
  await nextTick();
  if (!contentRef.value) return;

  // 1. 计算实际行高（优先使用props，否则自动检测）
  const lineHeight = props.lineHeight || getActualLineHeight();

  // 2. 设置理论最大高度
  calculatedMaxHeight.value = lineHeight * props.maxLines;

  // 3. 检测实际内容高度
  calculatedContentHeight.value = contentRef.value.scrollHeight;

  // 4. 判断是否需要折叠（保留1px容差防止舍入误差）
  showToggle.value =
    calculatedContentHeight.value > calculatedMaxHeight.value + 1;
};

// 获取实际行高（创建测量元素）
const getActualLineHeight = () => {
  if (!contentRef.value) return 20; // 默认值
  const temp = document.createElement('div');
  temp.style.font = getComputedStyle(contentRef.value).font;
  temp.innerHTML = 'M'; // 使用标准字符测量
  temp.style.visibility = 'hidden';
  temp.style.padding = '0';
  temp.style.margin = '0';
  document.body.appendChild(temp);
  const height = temp.offsetHeight;
  document.body.removeChild(temp);
  return height;
};

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
  emit('expand-change', isExpanded.value);
};

// 初始化
onMounted(() => {
  const observer = new ResizeObserver(checkClampNeeded);
  nextTick(() => {
    if (contentRef.value) {
      observer.observe(contentRef.value);
      checkClampNeeded();
    }
  });

  return () => observer.disconnect();
});

// 暴露方法
defineExpose({
  toggle: toggleExpanded,
  update: checkClampNeeded,
});
</script>

<style scoped>
.smart-clamp-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.content-box {
  overflow: hidden;
  transition: max-height var(--transition-duration) ease;
  word-break: break-word;
  flex: 1;
}

.content-box.is-clamped {
  display: -webkit-box;
  -webkit-line-clamp: var(--max-lines);
  -webkit-box-orient: vertical;
}

.toggle-box {
  position: relative;
  padding: 0 0 6px 6px;
  align-self: flex-end;
}

.toggle-box.is-expanded {
  position: static;
}

.toggle-btn {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--el-color-primary);
  font-size: 12px;
}

.toggle-btn:hover {
  opacity: 0.9;
}

.toggle-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}
</style>
