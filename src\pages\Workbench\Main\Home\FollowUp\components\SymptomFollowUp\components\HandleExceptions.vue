<template>
  <div>
    <HrtDialog
      v-model="dialogVisible"
      title="处理意见"
      :width="500"
      size="large"
    >
      <el-radio-group v-model="treatMethod" class="treat-method">
        <el-radio
          v-for="item in eventTreatList"
          :key="item.value"
          :label="item.value"
        >
          <div class="font-sm">
            {{ item.name }}
            <span>{{ item.tips }}</span>
          </div>
        </el-radio>
      </el-radio-group>
      <template #footer>
        <div class="p-sm pr-lg border-t">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            :loading="loadingDrugTableData || handleEventLoading"
            type="primary"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </HrtDialog>
    <EditDrug
      v-if="chooseDrugVisible"
      v-model:choose-drug-visible="chooseDrugVisible"
      :params-data="drugTableData"
      @confirm-drug="handleAdjustDrug"
      @cancel-edit="chooseDrugVisible = false"
    />
  </div>
</template>
<script setup lang="ts">
import { HrtDialog } from '@hrt/components';
import store from '@/store';
import { updateFollowDealSymptom } from '@/api/followup';
import { IApiDrugPatientCurrentDrugList } from '@/interface/type';
import EditDrug from '@/components/DrugInfo/components/EditDrug.vue';
import { getDrugPatientAdjust, getDrugPatientCurrent } from '@/api/drug';
import bus from '@/lib/bus';

interface IProps {
  followUpId: number;
  showDialogVisible: false;
}
const props = defineProps<IProps>();
const imStore = store.useIM();
const globalStore = store.useGlobal();
const dialogVisible = ref(true);
const treatMethod = ref(1);
const chooseDrugVisible = ref(false);
const drugTableData = ref<IApiDrugPatientCurrentDrugList[]>([]);

const eventTreatList = [
  {
    value: 1,
    name: '观察',
    tips: '（不生成后续待办）',
  },
  {
    value: 2,
    name: '调药',
    tips: '（调药操作后3日，系统提醒您跟踪患者调药）',
  },
  {
    value: 3,
    name: '门诊',
    tips: '（系统3日后自动提醒您跟踪患者门诊情况）',
  },
  {
    value: 4,
    name: '住院',
    tips: '（系统3日后自动提醒您跟踪患者住院情况）',
  },
];
const loadingDrugTableData = ref(false);
const handleEventLoading = ref(false);

const getDrugPatientCurrents = async () => {
  loadingDrugTableData.value = true;
  try {
    const res = await getDrugPatientCurrent({ patientId: globalStore.userId! });
    drugTableData.value =
      res.drugList?.map(v => {
        if (v.drugAmount) delete v.drugAmount['custom'];
        return v;
      }) || [];
    bus.emit('updete-todo-list');
  } finally {
    loadingDrugTableData.value = false;
  }
};
const handleAdjustDrug = async (obj: any) => {
  try {
    const res = await getDrugPatientAdjust({
      ...obj,
      patientId: globalStore.userId!,
    });
    if (obj.adjustDrugTrack) {
      // 跟踪用药需要发送药物调整消息卡片
      imStore.sendPatientCustomMsg({
        content: {
          name: '药物调整',
          id: res.drugInfoId,
          type: 7,
        },
      });
    }
    await treatmentMethodReq();
  } catch {
    ElMessage.error('调整用药失败!');
  }
};

const handleSubmit = async () => {
  if (treatMethod.value === 2) {
    await getDrugPatientCurrents();
    chooseDrugVisible.value = true;
  } else {
    await treatmentMethodReq();
  }
};
const treatmentMethodReq = async () => {
  const params = {
    followUpId: props.followUpId,
    dealType: treatMethod.value,
  };
  try {
    handleEventLoading.value = true;
    await updateFollowDealSymptom(params);
    ElMessage.success('处理成功');
    dialogVisible.value = false;
    bus.emit('updete-todo-list');
    emit('changeDeal');
  } finally {
    handleEventLoading.value = false;
  }
};
const emit = defineEmits(['update:showDialogVisible', 'changeDeal']);
onMounted(() => {});
watch(
  () => dialogVisible.value,
  () => {
    emit('update:showDialogVisible', false);
  }
);
</script>

<style scoped lang="less">
.treat-method {
  padding: 24px 56px;
  span {
    color: #7a8599;
  }
}
</style>
