<template>
  <div class="edit-box flex items-center">
    <div class="edit-msg">
      上次编辑人/时间：{{ props.editInfo.name }}/{{ props.editInfo.time }}
    </div>
    <div
      v-if="!props.readonly"
      class="edit-title flex items-center cursor-pointer ml-24"
      @click="edit"
    >
      <img :src="iconEdit" alt="" class="w-10 h-10 mr-4" />
      <span>编辑</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import iconEdit from '@/assets/imgs/overview/icon-edit.png';
const props = defineProps({
  editInfo: {
    default: () => {},
    type: Object,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['editMsg']);
let edit = () => {
  emit('editMsg');
};
</script>
<style scoped lang="less">
.edit-msg {
  font-size: 14px;
  color: #7a8599;
}
.edit-title {
  font-size: 14px;
  color: #2c89dc;
}
</style>
