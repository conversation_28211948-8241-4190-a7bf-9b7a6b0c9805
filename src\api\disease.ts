import { http } from '@/network';
import {
  IApiCaseHistoryCardiacConclusionItem,
  IApiCaseHistoryCtaDetail,
  IApiCaseHistoryCtaDetailParams,
  IApiCaseHistoryCtaPreserve,
  IApiCaseHistoryCtaPreserveParams,
  IApiCaseHistoryEcgConclusionItem,
  IApiCaseHistoryFieldItem,
  IApiCaseHistoryFieldParams,
  IApiCaseHistoryInHospitalCheck,
  IApiCaseHistoryInHospitalCheckParams,
  IApiCaseHistoryPatientHistoryDeleteParams,
  IApiCaseHistoryPreserveCardiacParams,
  IApiCaseHistoryPreserveEcgParams,
  IApiCaseHistoryPreservePacemakerParams,
  IApiCaseHistoryPreserveRecoveryParams,
  IApiCaseHistoryRecoveryDetail,
  IApiCaseHistorySearchDiagnosisReportItem,
  IApiCaseHistorySearchDiagnosisReportParams,
  IApiCaseHistorySearchEcgCardiacParams,
  IApiCaseHistorySearchEcgDynamicParams,
  IApiCaseHistorySearchEcgLeadParams,
  IApiCaseHistorySearchEcgPacemakerParams,
  IApiCaseHistoryTestSymptomItem,
  IApiCaseHistoryTitleTime,
  IApiCaseHistoryTitleTimeParams,
} from '@/interface/type';

/** 12导联心电图、动态心电图、心脏彩超页面构建数据 */
export const getIllnessInfo = (data: IApiCaseHistoryFieldParams) => {
  return http.post<Required<IApiCaseHistoryFieldItem>[]>({
    url: '/api/case/history/field',
    data,
  });
};

/** 12、动态心电图结论查询 */
export type IGetIllnessConclusion = Required<
  IApiCaseHistoryEcgConclusionItem & { pid: number }
>[];
export const getIllnessConclusion = () => {
  return http.post<IGetIllnessConclusion>({
    url: '/api/case/history/ecg/conclusion',
  });
};

/** 心脏彩超结论查询 */
export const getCardiacConclusion = () => {
  return http.post<Required<IApiCaseHistoryCardiacConclusionItem>[]>({
    url: '/api/case/history/cardiac/conclusion',
  });
};

// 删除 ecgId, ecgType: 1:12导联；2：动态心电图；3：心脏彩超；4：起搏器
export const ecgDelete = (data: any) => {
  return http.post({
    url: '/patient/IllnessInfo/ecg/delete',
    data,
  });
};

/** 保存12导联心电图、修改 */
export const hotelSubmit = (data: IApiCaseHistoryPreserveEcgParams) => {
  return http.post({
    url: '/api/case/history/preserve/ecg',
    data,
  });
};

/** 12导联心电图详情查询 */
export const getHotleDetails = (data: IApiCaseHistorySearchEcgLeadParams) => {
  return http.post({
    url: '/api/case/history/search/ecg/lead',
    data,
  });
};

/** 动态心电图保存、修改 */
export const dcgSubmit = (data: IApiCaseHistoryPreserveEcgParams) => {
  return http.post({
    url: '/api/case/history/preserve/ecg/dynamic',
    data,
  });
};

/** 动态心电图详情查询 */
export const getDcgDetails = (data: IApiCaseHistorySearchEcgDynamicParams) => {
  return http.post({
    url: '/api/case/history/search/ecg/dynamic',
    data,
  });
};

/** 心脏彩超保存、修改 */
export const heartUltrasoundSubmit = (
  data: IApiCaseHistoryPreserveCardiacParams
) => {
  return http.post({
    url: '/api/case/history/preserve/cardiac',
    data,
  });
};

/** 心脏彩超详情查询 */
export const getHeartUltrasoundDetails = (
  data: IApiCaseHistorySearchEcgCardiacParams
) => {
  return http.post({
    url: '/api/case/history/search/ecg/cardiac',
    data,
  });
};

/** 起搏器程控保存、修改 */
export const pacemakerSubmit = (
  data: IApiCaseHistoryPreservePacemakerParams
) => {
  return http.post({
    url: '/api/case/history/preserve/pacemaker',
    data,
  });
};

/** 起搏器程控详情 */
export const getPacemakerDetails = (
  data: IApiCaseHistorySearchEcgPacemakerParams
) => {
  return http.post({
    url: '/api/case/history/search/ecg/pacemaker',
    data,
  });
};

/** 6MWT症状查询 */
export function getTestSymptom() {
  return http.post<Required<IApiCaseHistoryTestSymptomItem>[]>({
    url: '/api/case/history/test/symptom',
  });
}

/** 6MWT保存、修改 */
export const preserveRecovery = (
  data: IApiCaseHistoryPreserveRecoveryParams
) => {
  return http.post({
    url: '/api/case/history/preserve/recovery',
    data,
  });
};

/** 6MWT详情查询 */
export const getRecoveryDetails = (data: { reportId: number }) => {
  return http.post<IApiCaseHistoryRecoveryDetail>({
    url: '/api/case/history/recovery/detail',
    data,
  });
};

/** 住院检查详情 */
export const getDiagnosisReportDetails = (
  data: IApiCaseHistorySearchDiagnosisReportParams
) => {
  return http.post<Required<IApiCaseHistorySearchDiagnosisReportItem>[]>({
    url: '/api/case/history/search/diagnosis/report',
    data,
  });
};

/** 保存住院检查 */
export const updateHospitalCheck = (
  data: IApiCaseHistoryInHospitalCheckParams
) => {
  return http.post<Required<IApiCaseHistoryInHospitalCheck>>({
    url: '/api/case/history/in/hospital/check',
    data,
  });
};

/** 删除病历 */
export const delPatientHistory = (
  data: IApiCaseHistoryPatientHistoryDeleteParams
) => {
  return http.post({
    url: '/api/case/history/patient/history/delete',
    data,
  });
};

/** 报告原文跳转复查、病例，title时间查询 */
export const getTitleTime = (data: IApiCaseHistoryTitleTimeParams) => {
  return http.post<IApiCaseHistoryTitleTime>({
    url: '/api/case/history/title/time',
    data,
  });
};

/** 获取心肺运动试验报告字段 */
export const getCardiopulmonaryFields = (data: any) => {
  return http.post({
    url: '/api/case/history/index/field',
    data,
  });
};
/** 保存心肺运动试验指标数据 */
export const saveCardiopulmonaryFields = (data: any) => {
  return http.post({
    url: '/api/case/history/save/index/data',
    data,
  });
};

/** 心肺运动试验详情 */
export const getCardiopulmonaryDetail = (data: any) => {
  return http.post({
    url: '/api/case/history/index/value/search',
    data,
  });
};

/** 查询结构化字段 */
export const getKeywords = (data: any) => {
  return http.post({
    url: '/api/ocr/query/keywords',
    data,
  });
};

/** 保存cta */
export const updateCtaPreserve = (data: IApiCaseHistoryCtaPreserveParams) => {
  return http.post<IApiCaseHistoryCtaPreserve>({
    url: '/api/case/history/cta/preserve',
    data,
  });
};

/** cta详情 */
export const getCtaPreserve = (data: IApiCaseHistoryCtaDetailParams) => {
  return http.post<IApiCaseHistoryCtaDetail>({
    url: '/api/case/history/cta/detail',
    data,
  });
};
