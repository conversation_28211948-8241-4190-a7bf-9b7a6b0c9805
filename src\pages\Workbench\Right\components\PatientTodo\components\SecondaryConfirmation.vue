<template>
  <div>
    <el-dialog
      v-model="props.customType"
      width="360px"
      class="sure-dialog"
      :show-close="false"
      @close="close"
    >
      <span>
        <div class="flex items-center mb-12">
          <img :src="warining" alt="" class="w-20 h-20 mr-8" /><span
            class="title"
            >是否{{ props.customTitle }}待办？</span
          >
        </div>
        <div class="middle ml-28 mb-12">
          {{ props.customTitle }}待办后不可撤回
        </div>
        <div class="btns flex items-center justify-end">
          <div
            class="cancel w-76 h-32 flex items-center justify-center cursor-pointer mr-8"
            @click="cancel(0)"
          >
            取消
          </div>
          <div
            class="sure w-76 h-32 flex items-center justify-center cursor-pointer"
            @click="sure"
          >
            确认
          </div>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import warining from '@/assets/imgs/todo/warning.png';
import { deteleTodoApi, handleTodoApi } from '@/api/todo';
import { dialogTip, getUserRoles } from '../index';

const props = defineProps({
  customType: {
    default: false,
    type: Boolean,
  },
  customTitle: {
    default: '',
    type: String,
  },
});

import useTodo from '@/store/module/useTodo';
import useUserStore from '@/store/module/useUserStore';
let useTodoInfo = useTodo();
const userStore = useUserStore();

let sure = () => {
  if (props.customTitle === '删除') {
    deteleTodoApi({
      backlogId: useTodoInfo.todoInfo.backlogId,
      type: 'CUSTOM_BACKLOG',
    }).then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('删除成功！', 'success');
        cancel(1);
      } else {
        dialogTip(message, '');
      }
    });
  } else {
    handleTodoApi({
      backlogId: useTodoInfo.todoInfo.backlogId,
      type: 'CUSTOM_BACKLOG',
      content: JSON.stringify({ customTodo: 1 }),
      headId: userStore.accountId,
      headRole: getUserRoles(),
    }).then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('处理成功！', 'success');
        cancel(1);
      } else {
        dialogTip(message);
      }
    });
  }
};

const emit = defineEmits(['close']);
/*value 1 成功 0 取消*/
let cancel = value => {
  emit('close', value);
};
let close = () => {
  cancel(0);
};
</script>
<style scoped lang="less">
:deep(.sure-dialog) {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 24px;
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #15233f;
    }
    .middle {
      font-size: 14px;
      color: #7a8599;
    }
    .btns {
      .sure {
        background: #2e6be6;
        border-radius: 2px;
        font-size: 14px;
        color: #ffffff;
      }
      .cancel {
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #dcdee0;
        font-size: 14px;
        color: #323233;
      }
    }
  }
}
</style>
