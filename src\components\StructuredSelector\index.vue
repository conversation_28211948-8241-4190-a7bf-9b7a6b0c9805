<template>
  <div class="container">
    <TagList
      :list="showNameList"
      @delete-item="deleteDiseaseItem"
      @sort="sortHandler"
    />
    <slot name="reference">
      <div
        class="nopx-disease-box"
        :style="{ width }"
        @click="checkListVisible = true"
      >
        <div class="select-box">
          <span class="empty">请选择</span>
          <el-icon :size="14">
            <i-ep-arrowDown />
          </el-icon>
        </div>
      </div>
    </slot>
    <DialogCheckList
      :visible="checkListVisible"
      :disease-list="diseaseList"
      :disease-data="diseaseSelectorData"
      :enable-filter="enableFilter"
      :width="dialogWidth"
      :data-map="dataMap"
      :title="props.title"
      :num="showNameList.length"
      @on-change="onChange"
      @close="onClose"
    />
  </div>
</template>

<script setup lang="ts">
import DialogCheckList from './components/DialogCheckList.vue';
import { isEmpty, isEqual } from 'lodash-es';
import { idsTransform, getDataMap, getSortedIds } from './util';
import TagList from './components/TagList.vue';
import {
  IDiseaseMap,
  IDiseaseData,
  IProps,
  IShowNameList,
  IPathItem,
} from './type';

defineOptions({
  name: 'StructuredSelector',
});
const emit = defineEmits(['onChange', 'onSpecialChange']);
const slots = useSlots();
const props = withDefaults(defineProps<IProps>(), {
  diseaseData: () => ({}),
  enableFilter: () => true,
});
const checkListVisible = ref(false);
const diseaseSelectorData = ref<IDiseaseData>({});
const localData = ref<Partial<IShowNameList>>({});
const showNameList = ref<IPathItem[]>([]);
const dataMap = ref<IDiseaseMap>({});
const disaseDataReady = ref(false);

const initData = () => {
  const { diseaseData } = props;
  const { diseaseIds } = diseaseData;
  const res = idsTransform(diseaseIds, dataMap.value);
  const { showNameList: snList, selectedPath, resultTreeData } = res;
  const params = {
    ids: diseaseIds,
    showNameList: snList,
    selectedPath,
    resultTreeData,
  };
  localData.value = params;
  showNameList.value = snList;

  emit('onChange', params);
};
watch(
  () => props.diseaseList,
  newVal => {
    if (newVal?.length) {
      dataMap.value = getDataMap(newVal);
      if (disaseDataReady.value) {
        initData();
      }
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.diseaseData,
  newVal => {
    if (newVal) {
      const { diseaseIds } = newVal;
      diseaseSelectorData.value = newVal;
      if (diseaseIds && !isEqual(diseaseIds, localData.value.ids)) {
        disaseDataReady.value = true;
        if (!isEmpty(dataMap.value)) {
          initData();
        }
      }
    }
  },
  { deep: true, immediate: true }
);
const onClose = () => {
  checkListVisible.value = false;
};
const onChange = (params: IShowNameList) => {
  showNameList.value = params.showNameList;
  localData.value = params;

  emit('onChange', params);
};
const changeParams = (newIds: number[]) => {
  const res = idsTransform(newIds, dataMap.value);
  const params = {
    ids: newIds,
    showNameList: res.showNameList,
    selectedPath: res.selectedPath,
    resultTreeData: res.resultTreeData,
  };
  localData.value = params;
  emit('onChange', params);
};
const deleteDiseaseItem = (item: IPathItem) => {
  const { selectedPath } = localData.value;
  showNameList.value = showNameList.value.filter(v => v.id !== item.id);
  const filterPath = selectedPath?.filter(v => v[v.length - 1].id !== item.id);
  const filterPathIds = filterPath?.map(v => v.map(v => v.id)).flat();
  const newIds = [...new Set(filterPathIds)];
  changeParams(newIds);
  diseaseSelectorData.value = {
    diseaseIds: newIds,
  };
};
const showCheckListVisible = () => {
  checkListVisible.value = true;
};
const sortHandler = () => {
  const newIds = getSortedIds(showNameList.value, localData.value.selectedPath);
  changeParams(newIds);
  diseaseSelectorData.value = {
    diseaseIds: newIds,
  };
};
onMounted(() => {
  const refrenceEleId = slots.reference?.()[0].props?.id;
  if (refrenceEleId) {
    const el = document.getElementById(refrenceEleId);
    el?.addEventListener('click', showCheckListVisible);
  }
});
onUnmounted(() => {
  const refrenceEleId = slots.reference?.()[0].props?.id;
  if (refrenceEleId) {
    const el = document.getElementById(refrenceEleId);
    el?.removeEventListener('click', showCheckListVisible);
  }
});
defineExpose({
  deleteItem: deleteDiseaseItem,
  idsTransform: ids => idsTransform(ids, dataMap.value),
});
</script>

<style scoped lang="less">
.nopx-disease-box {
  display: inline-block;
  .select-box {
    width: 240px;
    border-radius: 2px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    background-color: white;
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #bac8d4;
    cursor: pointer;
  }
}
</style>
