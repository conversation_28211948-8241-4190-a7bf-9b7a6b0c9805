<template>
  <div v-loading="loading" class="table-content">
    <el-table
      :key="renderKey"
      :data="tableData"
      row-key="uuid"
      width="100%"
      default-expand-all
      class="intern_table_list"
      :span-method="arraySpanMethod"
      :header-row-style="headerRowStyle"
      :header-cell-style="headerCellStyle"
      :row-style="rowStyleHandler"
      :row-class-name="rowClassHandler"
      :highlight-current-row="false"
    >
      <el-table-column prop="No" label="序号" width="120">
        <template #default="{ row }">
          <div v-if="row.children" class="detail">
            <div class="no">{{ row.No }}</div>
            <div class="item">
              姓名:
              <span>{{ row.patientName ?? '--' }}</span>
            </div>
            <div class="item">
              性别:
              <span>{{ GENDER_MAP[row.gender] ?? '未知' }}</span>
            </div>
            <div class="item">
              年龄:
              <span>{{ row.age ?? '--' }}岁</span>
            </div>
            <div class="item">
              工作室:
              <span>{{ row.workRoomName ?? '--' }}</span>
            </div>
            <div class="item">
              医生:
              <span>{{ row.doctorName ?? '--' }}</span>
            </div>
            <div class="item">
              任务类型:
              <span>{{ TASK_TYPE_MAP[row.taskType] ?? '--' }}</span>
            </div>
            <div class="item">
              最后提交时间:
              <span>{{ formatTime(row.endSubmitTime) || '--' }}</span>
            </div>
            <div class="item">
              任务状态:
              <span
                class="status"
                :style="{ backgroundColor: getStatusColor(row) }"
              ></span>
              <span>{{ TASK_STATUS_MAP[row.taskStatus] ?? '--' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="moduleList" label="子任务段落" width="360">
        <template #default="{ row }">
          <Text>{{ renderModuleList(row) }}</Text>
        </template>
      </el-table-column>
      <el-table-column prop="reason" to label="子任务说明" width="450">
        <template #default="{ row }">
          <Text>{{ renderReasonList(row) }}</Text>
        </template>
      </el-table-column>
      <el-table-column prop="taskStatus" label="状态" width="160">
        <template #default="{ row }">
          {{ SUB_TASK_MAP[row.taskStatus] ?? '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="generateName" label="子任务生成">
        <template #default="{ row }">
          {{ row.generateName + '： ' + formatTime(row.generateTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="submitName" label="提交完成">
        <template #default="{ row }">
          {{
            row.submitTime
              ? row.submitName + '： ' + formatTime(row.submitTime)
              : '--'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="operation" align="right" label="操作" width="240">
        <template #default="{ row }">
          <div v-if="row.children">
            <Popper
              v-if="row.taskStatus === 1"
              title="是否解除此条所有任务？解除后此将不算作你的资料录入数"
              placement="top-end"
              :confirm="() => releaseHandler(row)"
            >
              <template #reference>
                <el-button type="primary" link>释放任务</el-button>
              </template>
            </Popper>
            <Popper
              v-if="row.taskStatus === 0"
              placement="top-end"
              title="是否锁定此条任务关联单？"
              :confirm="() => lockHandler(row)"
            >
              <template #reference>
                <el-button type="primary" link>任务接单</el-button>
              </template>
              <div class="leading-[20px] pl-20">
                <p>1.锁定该任务，则同病历下所有任务都将关联</p>
                <p>2.最多只可锁定3个任务单</p>
                <p>
                  3.当天锁单新任务若在凌晨4点前未完成，则4点后系统自动将任务解单释放
                </p>
              </div>
            </Popper>
            <Popper
              v-if="
                row.taskStatus === 1 &&
                row.children.every(v => v.taskStatus === 2)
              "
              title="是否完成该转录任务？"
              placement="top-end"
              :confirm="() => completeHandler(row)"
            >
              <template #reference>
                <el-button type="primary" link>完成任务</el-button>
              </template>
              <div>
                <p>任务完成后将无法编辑已提交的内容。</p>
              </div>
            </Popper>
          </div>
          <div v-else>
            <el-button
              v-if="row.parent?.taskStatus === 1"
              type="primary"
              link
              @click="() => toView(row)"
            >
              查看
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="other" label=" " width="16" />
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <Record
      :title="recordData.title"
      :extend-info="recordData.extendInfo"
      @update="refreshTaskList"
    />
  </div>
</template>

<script setup lang="ts">
import { formatTime, getUuid } from '@/utils';
import dayjs from 'dayjs';
import Popper from '@/components/Popper/index.vue';
import Text from '@/components/Text/index.vue';
import Record from '../../Record.vue';
import {
  IApiInternTaskListContents,
  IApiInternTaskListContentsSubtaskList,
} from '@/interface/type';
import {
  getInterTaskList,
  lockTask,
  releaseTask,
  completeTask,
} from '@/api/intern';
import {
  GENDER_MAP,
  TASK_STATUS_MAP,
  TASK_TYPE_MAP,
  SUB_TASK_MAP,
  TASK_REASON_MAP,
  TASK_SECTION_MAP,
} from '@/constant/intern';
import { isNil } from 'lodash-es';
import useInternDrawer, { ModulesType } from '@/store/module/useInternDrawer';
import { IRecordSourceType } from '@/store/module/useComponentsTabAction';
import useGlobal from '@/store/module/useGlobal';
interface IProps {
  params: any;
}

type IChildren = IApiInternTaskListContentsSubtaskList & {
  parent: IApiInternTaskListContents & {
    taskType: number;
    sourceTime: number;
    gender: 1 | 2;
  };
  content: any;
};
interface IRow extends Omit<IApiInternTaskListContents, 'subtaskList'> {
  uuid: string;
  No: number;
  children: IChildren[];
}
const globalStore = useGlobal();
const props = defineProps<IProps>();
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const total = ref(0);
const tableData = ref<IApiInternTaskListContents[]>([]);
const renderKey = ref(0);
const recordData = ref({
  title: '',
  extendInfo: {} as any,
});

const getStatusColor = (row: any) => {
  const { taskStatus } = row;
  if (taskStatus === 0) return '#2E6BE6';
  if (taskStatus === 1) return '#2FB324';
  if (taskStatus === 2) return '#7A8599';
};
const renderModuleList = (row: any) => {
  const stringList = row.moduleList.map(v => {
    if (v === 6) {
      return TASK_SECTION_MAP[row.parent.taskType + '-' + v];
    }
    return TASK_SECTION_MAP[v];
  });
  return stringList.join('、') ?? '--';
};
const renderReasonList = (row: any) => {
  const stringList = row.reason
    .filter(v => v !== -1)
    .map(v => {
      return TASK_REASON_MAP[v];
    });
  if (row.remark) {
    stringList.push(row.remark);
  }
  return stringList.join('、') ?? '--';
};
const transformTask = (list: IApiInternTaskListContents[], p = null) => {
  const result = list.map((v, i) => {
    const obj: any = { ...v, uuid: getUuid() };
    if (p !== null) {
      obj.parent = p;
    }
    if (v.subtaskList) {
      obj.No = i + 1 + pageSize.value * (currentPage.value - 1);
      obj.children = transformTask(v.subtaskList, v as any);
      delete obj.subtaskList;
    }
    return obj;
  });
  return result;
};
const getTaskList = async () => {
  loading.value = true;
  const params = {
    ...props.params,
    endSubmitStartTime: props.params?.createTime?.[0] ?? null,
    endSubmitEndTime: props.params?.createTime?.[1]
      ? dayjs(props.params?.createTime?.[1]).add(1, 'day').valueOf() - 1
      : null,
    pageSize: pageSize.value,
    page: currentPage.value,
    taskType: !isNil(props.params?.taskType)
      ? props.params?.taskType === -999
        ? null
        : [props.params?.taskType]
      : null,
    taskStatus: !isNil(props.params?.taskStatus)
      ? props.params?.taskStatus === -999
        ? null
        : props.params?.taskStatus
      : null,
    patientIds: !isNil(props.params?.patientType)
      ? props.params?.patientType === -999
        ? null
        : JSON.parse(props.params?.patientType)
      : null,
  };
  delete params.createTime;
  const res = await getInterTaskList(params);
  tableData.value = transformTask(res.contents ?? []);
  total.value = res.total ?? 0;
  renderKey.value += 1;
  loading.value = false;
};
const loadingWrapper = async (fn: () => Promise<any>) => {
  const loadingInstance = ElLoading.service({ fullscreen: true });
  try {
    await fn();
  } finally {
    loadingInstance.close();
  }
};
const completeHandler = async (row: IRow) => {
  loadingWrapper(async () => {
    await completeTask({
      taskId: row.mainTaskId!,
      sourceTime: row.sourceTime,
      sourceId: row.sourceId,
    });
    refreshTaskList();
  });
};
const lockHandler = async (row: IRow) => {
  loadingWrapper(async () => {
    await lockTask({ taskId: row.mainTaskId! });
    refreshTaskList();
  });
};
const releaseHandler = async (row: IRow) => {
  loadingWrapper(async () => {
    await releaseTask({ mainTaskId: row.mainTaskId! });
    refreshTaskList();
  });
};
const { setVisible, setBaseInfo } = useInternDrawer();
const toView = ({ subtaskId, parent, moduleList }: IChildren) => {
  if (!parent || !subtaskId) return;
  const { patientId = 0, taskType = 0, sourceId = 0, sourceTime = 0 } = parent;
  setBaseInfo({
    patientId,
    taskId: subtaskId,
    caseId: sourceId,
    sourceType: taskType as IRecordSourceType,
    sourceId,
    sourceTime,
    modules: (moduleList || []) as ModulesType[],
    submitSourceId: subtaskId,
    internSubmitBatchId: subtaskId,
  });
  globalStore.setUserId(patientId);
  setVisible(true);
};
const handleSizeChange = () => {
  getTaskList();
};
const handleCurrentChange = () => {
  getTaskList();
};
const headerCellStyle = {
  backgroundColor: '#EBEDF0',
};
const headerRowStyle = {
  fontWeight: 500,
  fontSize: '14px',
  color: '#3A4762',
};
const rowClassHandler = ({ row }: any) => {
  if (row.children) return 'parent_level';
};
const rowStyleHandler = ({ row }: any) => {
  if (row.children) return 'background:#F7F8FA';
};
const arraySpanMethod = ({ row, columnIndex }: any) => {
  if (row.children) {
    if (columnIndex === 0) {
      return [1, 6];
    } else if ([6, 7].includes(columnIndex)) {
      return [1, 1];
    } else {
      return [0, 0];
    }
  }
};
const resetPage = () => {
  currentPage.value = 1;
  pageSize.value = 10;
};
const refreshTaskList = () => {
  resetPage();
  getTaskList();
};
defineExpose({
  refreshTaskList: refreshTaskList,
});
watch(
  () => props.params,
  () => {
    refreshTaskList();
  },
  {
    deep: true,
  }
);

const internDrawer = useInternDrawer();
watch(
  () => internDrawer.visible,
  value => {
    if (!value && internDrawer.taskId) refreshTaskList();
  },
  {
    deep: true,
    immediate: true,
  }
);
defineOptions({
  name: 'TableContent',
});
</script>

<style scoped lang="less">
.table-content {
  margin-top: 8px;
  padding: 16px 16px 0;
  background: #fff;
  height: calc(100vh - 132px);
  .intern_table_list {
    :deep(.el-table__row) {
      height: 44px;
      &:hover > td {
        background-color: unset !important;
      }
      .el-table__cell {
        &.hover-cell {
          background-color: unset !important;
        }
      }
      &.parent_level:hover > td {
        background-color: #ecf4fc !important;
      }
    }
    :deep(.el-table__cell) {
      height: 44px;
    }
    &.el-table {
      height: calc(100vh - 214px) !important;
    }
    :deep(.el-table__placeholder) {
      display: none;
    }
  }
}
.detail {
  display: inline-flex;
  .no {
    width: 90px;
  }
  .item {
    position: relative;
    color: #7a8599;
    margin-right: 50px;
    > span {
      color: #3a4762;
    }
    &:not(:last-child):after {
      position: absolute;
      content: '/';
      font-size: 0;
      height: 12px;
      top: 6px;
      right: -25px;
      border-left: 1px solid #bac8d4;
    }
    .status {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin: 0 8px;
      border-radius: 100%;
    }
  }
  .no-seperator {
    &:after {
      border-left-color: transparent !important;
    }
  }
}
.pagination {
  height: 64px;
  display: flex;
  justify-content: center;
}
</style>
