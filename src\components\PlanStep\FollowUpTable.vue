<template>
  <div v-loading="loading">
    <el-table
      class="follow-table"
      :data="tableData.length ? tableData : emptyTableData"
      max-height="300"
    >
      <el-table-column prop="date" label="名称" width="150">
        <template #default="scope">
          <div class="flex items-center">
            <span>{{ scope.row.name }}</span>
            <img
              v-if="!scope.row.draft"
              class="w-14 h-14 ml-4 cursor-pointer"
              :src="addImg"
              @click="() => addItem(scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="时间" width="160">
        <template #default="scope">
          <span v-if="!editItem.uuId || editItem.uuId !== scope.row.uuId">{{
            scope.row.date ? dayjs(scope.row.date).format('YYYY-MM-DD') : ''
          }}</span>
          <el-date-picker
            v-if="editItem.uuId && editItem.uuId === scope.row.uuId"
            v-model="editItem.date"
            type="date"
            placeholder="请选择日期"
            size="default"
            value-format="x"
            :disabled-date="disabledDateFn"
            :clearable="false"
            style="width: 135px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="address" label="随访表单">
        <template #default="scope">
          <template v-if="!editItem.uuId || editItem.uuId !== scope.row.uuId">
            <div
              v-for="(v, index) in scope.row.followQuestionnaireList"
              :key="index"
              class="item-card"
            >
              <img class="w-14 h-14" :src="followImg" />
              <span>{{ v.questionnaireName }}</span>
            </div>
          </template>
          <el-select
            v-if="editItem.uuId && editItem.uuId === scope.row.uuId"
            v-model="editItem.value"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.questionnaireId"
              :label="item.questionnaireName"
              :value="item.questionnaireId"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作" width="120">
        <template #default="scope">
          <span
            v-if="editItem?.uuId === scope.row.uuId"
            class="edit"
            @click="confirmHandler"
            >确定</span
          >
          <span
            v-if="tableData.length > 0 && editItem?.uuId !== scope.row.uuId"
            class="edit"
            @click="() => editRow(scope.row)"
            >编辑</span
          >
          <span
            v-if="editItem?.uuId === scope.row.uuId"
            class="delete cancel"
            @click="cancelEdit"
            >取消</span
          >
          <el-popconfirm
            v-if="tableData.length > 0 && editItem?.uuId !== scope.row.uuId"
            title="确认删除此数据？"
            width="180px"
            @confirm="() => deleteHandler(scope.row)"
          >
            <template #reference>
              <span class="delete">删除</span>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import addImg from '@/assets/imgs/callCenter/add.png';
import followImg from '@/assets/imgs/review/followup.png';
import { getUuid } from '@/utils';
import useGlobal from '@/store/module/useGlobal';
import useMeta from '@/store/module/useMeta';
import {
  getSymptomList,
  getLifeList,
  updateFollowInfo,
  deleteFollow,
  addFollow,
} from '@/api/followup';
import bus from '@/lib/bus';

interface IProps {
  type: 2 | 3; // 2 症状随访 3 生活方式随访
  stepChange: number;
}
interface IItem {
  uuId: string;
  name?: string;
  date?: number | string;
  draft?: boolean;
  followQuestionnaireList: {
    questionnaireId: number;
    questionnaireName: string;
  }[];
}

const globalStore = useGlobal();
const metaData = useMeta();
const props = defineProps<IProps>();
const tableData = ref<IItem[]>([]);
const emptyTableData = ref([
  {
    uuId: getUuid(),
    name: '随访',
    value: [],
  },
]);
const loading = ref(false);
const emptyRowData = {
  uuId: '',
  date: '',
  followId: '',
  beforeTime: '',
  value: [] as number[],
};
const editItem = ref({ ...emptyRowData });

const options = computed(() => {
  return metaData.followUpOptions[props.type - 2];
});
const getName = item => {
  const isCustom = item.times === 0;
  const pre = isCustom ? '自定义' : '';
  return item.followId ? `${pre}症状随访` : '生活方式随访';
};
const getList = async () => {
  loading.value = true;
  const req = props.type === 2 ? getSymptomList : getLifeList;
  const res = (await req({ patientId: globalStore.userId })) as any[];
  loading.value = false;
  tableData.value = res?.map(v => ({
    ...v,
    uuId: getUuid(),
    name: getName(v),
    beforeTime: v.date,
  }));
};

const disabledDateFn = (time: { getTime: () => number }) => {
  return time.getTime() < Date.now() - 8.64e7;
};
const successCallback = () => {
  ElMessage.success('操作成功！');
  bus.emit('refresh-followup-list');
  getList();
};
const confirmHandler = async () => {
  if (!editItem.value.date) {
    ElMessage.warning('请选择日期');
    return;
  }
  if (!editItem.value.value?.length) {
    ElMessage.warning('请选择随访表单');
    return;
  }
  const _followQuestionnaireList = options.value.filter(v =>
    editItem.value.value?.includes(v.questionnaireId)
  );
  loading.value = true;
  if (editItem.value.beforeTime) {
    const params = {
      patientId: globalStore.userId,
      beforeTime: editItem.value.beforeTime,
      afterTime: editItem.value.date,
      followId: editItem.value.followId,
      followType: props.type - 1,
      questionnaireList: _followQuestionnaireList,
    };
    await updateFollowInfo(params).finally(() => {
      loading.value = false;
    });
    successCallback();
  } else {
    const params = {
      patientId: globalStore.userId,
      date: editItem.value.date,
      type: props.type - 1,
      followQuestionnaireList: _followQuestionnaireList,
    };
    await addFollow(params).finally(() => {
      loading.value = false;
    });
    successCallback();
  }
};
const editRow = item => {
  const { uuId, date, followId, beforeTime, followQuestionnaireList } = item;
  editItem.value.uuId = uuId;
  editItem.value.date = date;
  editItem.value.followId = followId;
  editItem.value.beforeTime = beforeTime;
  editItem.value.value = followQuestionnaireList.map(v => v.questionnaireId);
};
const cancelEdit = () => {
  clearDraft();
  editItem.value = { ...emptyRowData };
};
const getEmptyItemData = () => {
  return {
    uuId: getUuid(),
    draft: true,
    name: `自定义${props.type === 2 ? '症状' : ''}随访`,
    followQuestionnaireList: [],
  };
};
const getIndex = id => {
  return tableData.value.findIndex(v => v.uuId === id);
};
const clearDraft = () => {
  tableData.value = tableData.value.filter(v => !v.draft);
};
const addItem = item => {
  const oldIndex = getIndex(item.uuId);
  clearDraft();
  const index = getIndex(item.uuId);
  const newItem = getEmptyItemData();
  let _index = index + 1;
  if (index === -1) {
    _index = oldIndex;
  }
  tableData.value.splice(_index, 0, newItem);
  editRow(newItem);
};
const deleteHandler = async item => {
  const { date, followId } = item;
  const params = {
    patientId: globalStore.userId,
    followId,
    date,
    type: props.type - 1,
  };
  loading.value = true;
  await deleteFollow(params).finally(() => {
    loading.value = false;
  });
  ElMessage.success('删除成功！');
  getList();
};
onMounted(() => {
  getList();
});
defineExpose({
  getTableData: () => tableData.value,
  getList: () => getList(),
});
watch(
  () => props.stepChange,
  () => {
    cancelEdit();
  }
);
defineOptions({
  name: 'FollowTable',
});
</script>

<style scoped lang="less">
.follow-table {
  width: 928px;
  :deep(.el-table__header-wrapper) {
    .el-table__cell {
      color: #15233f;
      background-color: #ebedf0;
      padding: 14px 0;
    }
  }
  :deep(.el-table__body-wrapper) {
    width: 928px;
    .el-table__cell {
      padding: 4px 0;
      height: 48px;
    }
  }
  :deep(.el-table__empty-block) {
    width: 928px !important;
  }
}
.edit {
  position: relative;
  cursor: pointer;
  color: #2e6be6;
  margin-right: 18px;
}
.delete {
  position: relative;
  cursor: pointer;
  color: #e63746;
  &:after {
    position: absolute;
    content: '/';
    font-size: 0;
    width: 1px;
    height: 12px;
    background: #e1e5ed;
    left: -9px;
    top: 4px;
  }
  &.cancel {
    color: #939cae;
  }
}
.item-card {
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
  margin-bottom: 4px;
  background-color: #e1e5ed;
  border-radius: 2px;
  padding: 2px 4px;
}
</style>
