import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults';
import { CSSProperties } from 'vue';
import {
  getRiskDeviceExData,
  getRiskDeviceData,
  getRiskDeviceView,
  getRiskHeartData,
} from '@/api/exceptions';
import store from '@/store';
import {
  getBloodPressureTotalConfig,
  getBloodPressureEchartsConfig,
  getEchartsOptions,
  formateTime,
  getTreatmentName,
  errorLevelTextMap,
} from './utils';

const getRiskDataMap = {
  blood: getRiskDeviceData,
  heart: getRiskHeartData,
};

interface ICellStyle<T> {
  row: T;
  rowIndex: number;
  column: TableColumnCtx<T>;
  columnIndex: number;
}

/** @description 血压异常 */
export default function useBloodPressure(props: any) {
  const globalStore = store.useGlobal();
  const bloodPressureOptions = ref({});
  const tableData = ref<any[]>([]);
  const switchType = ref(1);
  const totalData = ref<Record<string, any>>({});
  const loading = ref(true);
  const totalConfig = ref(getBloodPressureTotalConfig());
  const cellClassNameHandler = (data: ICellStyle<any>) => {
    const styleObj: CSSProperties = {};
    const { row, columnIndex } = data;
    const { treatmentMsg, heartLevel, errorLevel } = row;
    const { dataType } = props;
    if (
      ([1, 2].includes(columnIndex) &&
        errorLevel > 0 &&
        dataType === 'blood') ||
      (columnIndex === 1 && heartLevel > 0 && dataType === 'heart')
    ) {
      styleObj.color = 'var(--color-danger)';
    }

    if (treatmentMsg) {
      styleObj.borderTop = '1px solid #0A73E4';
    }
    return styleObj;
  };
  const getRiskDeviceExDataHandler = async () => {
    const params = { patientId: globalStore.userId!, type: switchType.value };
    const data = await getRiskDeviceExData(params);
    data.maxExceptionLevel = data.maxExceptionLevel
      ? errorLevelTextMap[data.maxExceptionLevel]
      : '--';
    totalData.value = data;
  };
  const getRiskDeviceDataHandler = async () => {
    const params = { patientId: globalStore.userId! };
    const data = await getRiskDataMap[props.dataType](params);
    if (data.deviceDataList) {
      tableData.value = data.deviceDataList.map(item => {
        const { treatmentMethod, processingTime, testTime } = item;
        const treatmentMsg = treatmentMethod
          ? `${getTreatmentName(treatmentMethod)} ${formateTime(
              processingTime
            )}`
          : null;
        return {
          ...item,
          treatmentMsg,
          testTime: formateTime(testTime),
        };
      });
    }
  };
  const getRiskDeviceViewHandler = async () => {
    const params = { patientId: globalStore.userId!, type: switchType.value };
    const data = await getRiskDeviceView(params);
    const config = getBloodPressureEchartsConfig(
      data,
      props.targetValue,
      props.dataType
    );
    bloodPressureOptions.value = getEchartsOptions(config);
  };
  onMounted(() => {
    if (props.dataType === 'blood') {
      getRiskDeviceExDataHandler();
    }
    getRiskDeviceViewHandler();
    getRiskDeviceDataHandler();
  });
  watch(switchType, () => {
    if (props.dataType === 'blood') {
      getRiskDeviceExDataHandler();
    }
    getRiskDeviceViewHandler();
  });

  return {
    bloodPressureOptions,
    tableData,
    switchType,
    totalData,
    loading,
    totalConfig,
    cellClassNameHandler,
  };
}
