<template>
  <div class="text-[14px]" :style="{ marginRight: config.marginRight + 'px' }">
    <div v-if="config.type == 'checkbox'">
      <el-checkbox
        v-for="(item, index) in config.options"
        :key="index"
        v-model="valueData[item.key]"
        size="default"
        @change="(val: boolean) => changeHandler(item.key, val)"
        >{{ item.label }}</el-checkbox
      >
    </div>
    <div
      v-if="config.type === 'checkbox-expand'"
      class="select-wrap flex pt-4 pl-8 pr-8"
      :style="{ borderColor: expand ? '#2e6be6' : '' }"
      @click.stop="expandHandler"
    >
      <div class="shrink-0 pr-8 h-24 leading-[24px] text-[#7A8599]">
        {{ config.titleName || '管理情况' }}
      </div>
      <div class="item-wrap" :style="{ width: config.width + 'px' }">
        <span v-for="(item, index) of checkedList" :key="index">
          {{ item.label }}
          <el-icon
            class="cursor-pointer pl-2"
            @click.stop="() => deleteItem(item)"
            ><i-ep-close class="text-[#BAC8D4]" />
          </el-icon>
        </span>
      </div>
      <div class="pl-4 h-24 flex items-center text-[12px]">
        <i-ep-caret-bottom v-if="expand" />
        <i-ep-caret-right v-else />
      </div>
    </div>
    <div v-if="expand" ref="target" class="expand-wrap">
      <div v-for="(item, index) in config.options" :key="index" class="item">
        <el-checkbox
          v-model="valueData[item.key]"
          size="default"
          @change="(val: boolean) => changeHandler(item.key, val)"
          >{{ item.label }}</el-checkbox
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core';
interface IProps {
  config: any;
  valueData: any;
}
const emits = defineEmits(['change']);
const props = defineProps<IProps>();
const expand = ref(false);
const target = shallowRef();
defineOptions({
  name: 'CheckGroup',
});

onMounted(() => {
  onClickOutside(target, () => {
    setTimeout(() => {
      expand.value = false;
    });
  });
});

const changeHandler = (key: string, val: boolean) => {
  const params = { ...props.valueData, [key]: val };
  emits('change', params);
};
const expandHandler = () => {
  expand.value = !expand.value;
};
const deleteItem = (item: any) => {
  props.valueData[item.key] = false;
  const params = { ...props.valueData };
  emits('change', params);
};
const checkedList = computed(() => {
  const { config, valueData } = props;
  const res = [];
  for (const option of config.options) {
    if (valueData[option.key]) {
      res.push({ ...option });
    }
  }
  return res;
});
</script>

<style scoped lang="less">
.select-wrap {
  min-height: 32px;
  border-radius: 2px;
  border: 1px solid #dcdee0;
  .item-wrap {
    display: flex;
    flex-wrap: wrap;
    width: 285px;
    > span {
      display: flex;
      align-items: center;
      min-width: 50px;
      height: 24px;
      background: #f7f8fa;
      border-radius: 2px;
      border: 1px solid #dcdee0;
      padding: 0 4px;
      margin-right: 4px;
      margin-bottom: 4px;
    }
  }
}
.expand-wrap {
  width: 360px;
  overflow: auto;
  position: absolute;
  background: #fff;
  z-index: 999;
  box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.5);
  border-radius: 2px;
  .item {
    height: 32px;
    padding: 0 12px;
    &:hover {
      background: #edf4ff;
    }
    :deep(.el-checkbox) {
      width: 100%;
    }
  }
}
</style>
