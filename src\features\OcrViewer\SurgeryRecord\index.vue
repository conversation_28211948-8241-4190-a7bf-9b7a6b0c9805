<template>
  <div>
    <SurgeryRecordHead
      v-if="!locatedPatientHistoryId"
      @get-record-params="getRecordParams"
    />
    <SurgicalInformation ref="surgeryRef" :from="'ocr'" />
  </div>
</template>

<script setup lang="ts">
import SurgeryRecordHead from './components/SurgeryRecordHead.vue';
import { TypeHeadParmas } from './components/type';
import globalBus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
import { useOcrScan } from '@/store/module/useOcrScan';
import SurgicalInformation from '@/features/PatientRecord/AddHospitalized/SurgicalInformation.vue';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';

const globalData = useGlobal();
const ocrStore = useOcrScan();
const saveOcrStore = useSaveOcrInfo();

const surgeryRef = shallowRef();

const headParams = ref<TypeHeadParmas>({
  saveType: 0,
});

const locatedPatientHistoryId = inject('locatedPatientHistoryId');

const getRecordParams = (data: any) => {
  headParams.value = data;
};

const saveData = async () => {
  if (!headParams.value.date && !locatedPatientHistoryId) {
    ElMessage.error('请选择入院时间!');
    return;
  }
  const extraParams: any = saveOcrStore.getExtraParams();
  const params = {
    inTime: headParams.value.date ?? null,
    patientHistoryId: locatedPatientHistoryId
      ? locatedPatientHistoryId
      : headParams.value.patientHistoryId,
    caseType: 0,
    patientId: globalData.userId as number,
    accessory: ocrStore.globalImgInfo.currentImgUrl.url
      ? [ocrStore.globalImgInfo.currentImgUrl.url]
      : [],
    entry_task: extraParams.entry_task,
    sub_task_id: extraParams.sub_task_id,
  };
  return surgeryRef.value?.submit(params)?.then(res => {
    if (res) {
      globalBus.emit('close-ocr-dialog');
      globalBus.emit('saved-ocr-info');
      if (locatedPatientHistoryId || headParams.value.patientHistoryId) {
        let id = locatedPatientHistoryId
          ? locatedPatientHistoryId
          : headParams.value.patientHistoryId;

        globalBus.emit('refresh-attachment', id);
        globalBus.emit('refresh-record-data', id);
      }
      return true;
    }
  });
};

const setFormData = (data: any) => {
  surgeryRef.value?.setFormData(data);
};
defineExpose({
  saveData,
  setFormData,
});
defineOptions({
  name: 'SurgeryRecord',
});
</script>

<style scoped lang="less">
.select-box {
  :deep(.el-select) {
    width: 240px;
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
  }
}
:deep(.el-textarea__inner) {
  border-radius: 2px;
  font-size: 14px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  color: #203549;
  line-height: 20px;
  padding-bottom: 20px;
}
.form-label {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  .required-icon {
    color: #ea1212;
  }
}
.surgery-title {
  box-sizing: border-box;
  min-width: 112px;
  height: 40px;
  background: #f7f8fa;
  border: 1px solid #dcdee0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: auto;
  user-select: none;
  padding: 0 20px;
}
.active {
  background-color: #ffffff;
  border-bottom: none;
}
.surgery-edit {
  background-color: #ffffff;
}
.add-btn {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #2e6be6;
  line-height: 20px;
  cursor: pointer;
}
</style>
