// pinia store name 常量枚举
export const enum STORE_NAMES {
  /** 患者信息 tab 管理 */
  PATIENT_TABS = 'patient_basic_tabs',
  /** 左侧列表*/
  USER_LIST = 'user_list',
  /** 全局公共状态 */
  GLOBAL = 'global_data',
  /** 登录用户信息 */
  APP_USER = 'app_user',
  /** 处理患者指标项信息 */
  Indicator_Info = 'indicator_info',
  /**ocr图片识别**/
  OCR_IMG_SCAN = 'ocr_img_scan',
  /**ocr保存图片识别结果 **/
  OCR_SAVE_INFO = 'ocr_save_info',
  /**外部打开门诊、住院、复查等组件tab行为信息 **/
  Component_Tabs_Action = 'component_tabs_action',
  /**系统元数据 **/
  META_DATA = 'meta_data',
  REMINDER_EVENT = 'reminder_event',
  /** AI推荐对话 */
  AI_CONVERSATION = 'ai_conversation',
  /** 实习生端抽屉基础数据 **/
  Intern_Drawer = 'intern_drawer',
  /** 质疑抽屉 **/
  DOUBT = 'doubt',
}

export default STORE_NAMES;
