import store from '@/store';
import bus from '@/lib/bus';
import dayjs from 'dayjs';
import { debounce, throttle } from 'lodash-es';
import { getEndTimeStamp } from '@/utils';
import useFilter from './useFilter';
import {
  listConfigMap,
  listDataMap,
  listRequestMap,
  listCountMap,
} from './config';
import { IType, ICard } from '@/store/module/useUserList';
const THROTTLE_TIME = 1000 * 3;
const PAGE_SIZE = 8;

const userList = (type: IType) => {
  const tabAction = store.useComponentsTabAction();
  const globalStore = store.useGlobal();
  const role = globalStore.currentRole;
  const { filterConfigs, filterValue, setFilterValues, resetFilterValues } =
    useFilter(type, role);
  const userListStore = store.useUserList();
  const listConfigs = ref(listConfigMap[role][type]);
  const listData = ref(listDataMap[role][type]);
  const pageNumbers = ref([1, 1]);
  const finish = ref(0);
  const loading = ref(false);

  /**
   *  获取当前列表请求需要传入的参数
   * @param i 0 上列表  1 下列表
   * @returns
   */
  const getRequestParams = (i: 0 | 1) => {
    const params: any = {
      ...filterValue.value,
      pageNumber: pageNumbers.value[i],
      pageSize: PAGE_SIZE,
      ...userListStore.getAdvancedFormatData(),
    };
    if (role === 3) {
      // 运动康复师, 请求参数特殊处理
      if (params.manageStatus) {
        params.isNeedRehabManageStatus = true;
      } else {
        delete params.manageStatus;
      }
      if (params.enrollmentStartDate || params.enrollmentEndDate) {
        params.enrollmentEndDate = getEndTimeStamp(params.enrollmentEndDate);
        params.isNeedEnrollmentDate = true;
      }
      if (params.riskLevel !== -1) {
        params.isNeedRehabRiskLevel = true;
      } else {
        delete params.riskLevel;
      }
      if (params.startTime || params.endTime) {
        params.endTime = getEndTimeStamp(params.endTime);
        params.isNeedAssessmentDate = true;
      }
    }
    return params;
  };
  /**
   * 当前角色请求的列表对应 api
   * @returns
   */
  const getRequestApi = () => {
    const currentReqMap = listRequestMap[role][type];
    return currentReqMap;
  };
  /**
   * 第一个列表分页加载更多
   */
  const getFirstMore = () => {
    pageNumbers.value[0] += 1;
    getFirstList(true);
  };
  const reduceListNum = () => {
    const sum = listData.value.reduce((pre, cur) => pre + cur.count, 0);
    userListStore.tabCount[type] = sum;
  };
  /**
   * 第二个列表分页加载更多
   */
  const getSecondMore = () => {
    pageNumbers.value[1] += 1;
    getSecondList(true);
  };
  /**
   * 列表请求
   * @param listIndex  列表索引
   * @param reqKey  api key
   * @param extra  是否是分页请求，加载更多
   */
  const getListHandler = async (
    listIndex: 0 | 1,
    reqKey: 'first' | 'second',
    extra: boolean
  ) => {
    if (userListStore.activeTab !== type) return;
    const reqs = getRequestApi();
    loading.value = true;
    const params = getRequestParams(listIndex);
    try {
      const data: any = await reqs[reqKey as keyof typeof reqs](params);
      listData.value[listIndex].count = data.totals;
      if (extra) {
        listData.value[listIndex].data.push(...data.data);
      } else {
        listData.value[listIndex].data = data.data;
      }
    } finally {
      finish.value += 1;
      if (!extra && finish.value === listData.value.length) {
        loading.value = false;
        const allData =
          listData.value.length === 1
            ? [...listData.value[0].data]
            : [...listData.value[0].data, ...listData.value[1].data];
        // const userId = globalStore.userId;
        // const current = allData.find(
        //   (v: ICard) => v.patientId && v.patientId === userId
        // );
        // if (!current && allData?.[0]?.patientId && !userListStore.keepUser) {
        //   globalStore.setUserId(allData?.[0]?.patientId);
        //   toTabHandler(allData?.[0]);
        //   if (role === 3) {
        //     globalStore.manageStatus = allData?.[0]?.manageStatus as any;
        //   }
        // }
        // if (!allData.length) {
        //   globalStore.setUserId(undefined);
        // }
        if (!userListStore.userChoosed) {
          globalStore.setUserId(allData?.[0]?.patientId);
          toTabHandler(allData?.[0]);
          if (role === 3) {
            globalStore.manageStatus = allData?.[0]?.manageStatus as any;
          }
          globalStore.diseaseType = allData?.[0]?.serviceDiseaseType;
          userListStore.userChoosed = true;
        } else {
          const curItem = allData.find(v => v.patientId === globalStore.userId);
          if (curItem) {
            toTabHandler(curItem);
          }
        }
        reduceListNum();
      }
      if (extra) {
        loading.value = false;
        reduceListNum();
      }
    }
  };
  /**
   * 分页加载更多入口函数
   * @param listIndex
   */
  const getListMore = (listIndex: 0 | 1) => {
    const curData = listData.value[listIndex];
    if (curData.count <= curData.data.length) {
      return;
    }
    if (listIndex === 0) {
      getFirstMore();
    } else {
      getSecondMore();
    }
  };
  /**
   * 获取对应tab下第一个列表
   * @param extra
   */
  const getFirstList = async (extra: boolean = false) => {
    getListHandler(0, 'first', extra);
  };
  /**
   * 获取对应tab下第二个列表
   * @param extra
   */
  const getSecondList = async (extra: boolean = false) => {
    getListHandler(1, 'second', extra);
  };
  /**
   * 刷新函数， 运动康复师第二个tab下只有一个列表
   */
  const refreshHandler = async () => {
    pageNumbers.value = [1, 1];
    finish.value = 0;
    getFirstList();
    if (!(role === 3 && type === 1)) {
      getSecondList();
    }
  };
  /**
   * 刷新函数入口，节流处理
   */
  const refreshList = throttle(() => {
    refreshHandler();
  }, THROTTLE_TIME);

  /**防抖调用刷新列表 */
  const debouceRefreshList = debounce(() => {
    refreshHandler();
  }, 100);
  /**
   * 设置过滤条件 & 刷新列表
   */
  const setFilter = (data: any) => {
    setFilterValues(data);
    debouceRefreshList();
  };
  /**
   * 重置过滤条件
   */
  const resetFilterHandler = debounce(() => {
    userListStore.resetAdvancedData();
    resetFilterValues();
    debouceRefreshList();
  }, 200);

  const toTabHandler = (item: ICard) => {
    if (!item) return;
    if ((role === 1 && type === 1) || (role === 2 && type === 0)) {
      if (!item?.patientReview?.reviewId) {
        ElMessage.warning('复查 ID 不存在！');
        return;
      }
      tabAction.setAction({
        componentType: 2,
        mode: 'reuse',
        name:
          dayjs(item?.patientReview?.reviewDate).format('YYYY-MM-DD') + ' 复查',
        data: {
          id: item.patientReview.reviewId,
          recordActionType: {
            actionType: 'view',
            sourceType: 2,
          },
        },
      });
      bus.emit('open-component-tab');
      // 跳转复查
    } else if (role === 2 && type === 1) {
      // 跳转随访
      const { followUpId, followUpDate, followUpType } =
        item?.patientFollowUp ?? {};
      const followUpData =
        followUpType === 1
          ? { followUpDate, followUpId }
          : { userQuestionnaireDate: followUpDate };
      const subTitle = followUpType === 1 ? '症状随访' : '生活方式随访';
      tabAction.setAction({
        componentType: followUpType === 1 ? 6 : 5,
        name: dayjs(followUpDate).format('YYYY-MM-DD') + subTitle,
        data: followUpData,
      });
      bus.emit('open-component-tab');
    }
  };
  const refreshCurTypeCount = async () => {
    if (userListStore.activeTab === type) return;

    const {
      url: req,
      params,
      paramsKey,
      resultKey,
    } = listCountMap[role]?.[type];
    const newParams = {
      [paramsKey]: { ...getRequestParams(0), ...params[paramsKey] },
    };
    if (req) {
      const res: any = await req(newParams);
      if (res) {
        userListStore.tabCount[type] = res[resultKey] ?? 0;
      }
    }
  };
  onMounted(() => {
    bus.on('refresh-patient-list', () => {
      refreshList();
    });
    bus.on('clear-patient-list-filter', () => {
      resetFilterHandler();
    });
    refreshList();
    userListStore.tabRendered[type] = true;
  });
  // 高级搜索变化时，刷新列表数据
  watch(
    () => userListStore.advancedData,
    (newVal, oldVal) => {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        debouceRefreshList();
        refreshCurTypeCount();
      }
    }
  );
  // 搜索关键词变化时清空其他搜索条件
  // watch(
  //   () => userListStore.keywords,
  //   () => {
  //     resetFilterHandler();
  //   }
  // );
  watch(
    () => userListStore.activeTab,
    val => {
      if (val === type) {
        debouceRefreshList();
      }
    }
  );
  // 搜索列表数据，更新总统计数
  // watch(listData.value, newVal => {
  //   const sum = newVal.reduce((pre, cur) => pre + cur.count, 0);
  //   userListStore.tabCount[type] = sum;
  // });
  return {
    filterConfigs,
    filterValue,
    setFilter,
    listConfigs,
    listData,
    refreshList,
    getListMore,
    loading,
    toTabHandler,
  };
};
export default userList;
