import { getCurrentInstance } from 'vue';
export { useDynamicSubForm } from './useDynamicSubForm';
export { useFormJSONSchema } from './useFormJSONSchema';
export { useHandleData } from './useHandleData';
export { useCache } from './useCache';
export { useExaminationList } from './useExaminationList';

export const useInstance = () => {
  const currentInstance = getCurrentInstance();
  const instance = currentInstance?.proxy;
  return { instance };
};
// export { default as useCharts } from './useCharts';
