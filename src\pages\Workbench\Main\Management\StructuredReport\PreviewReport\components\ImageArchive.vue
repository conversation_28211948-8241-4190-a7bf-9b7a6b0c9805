<template>
  <div class="common-sty">
    <div class="font-bold">图片档案</div>
    <div v-if="info.length" class="flex items-center pt-sm flex-wrap">
      <div v-for="(url, index) in info" :key="url" class="relative">
        <ImgPreview
          :key="index"
          class="mr-3xs"
          :width="60"
          :url="url"
          :type="imgPreviewType"
          :show-status-label="false"
          :show-status="false"
          fixed
        />
      </div>
    </div>
    <div v-else class="text-center py-3xs">暂无数据</div>
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import ImgPreview from '@/components/ImgPreview/index.vue';

defineProps({
  info: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

// 图片预览分组
const imgPreviewType = ref(Date.now() + '');
</script>
