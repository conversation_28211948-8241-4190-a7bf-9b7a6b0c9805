<template>
  <div ref="containerRef" :key="global.userId" class="flex-1">
    <el-tabs
      v-model="activeTab"
      type="border-card"
      class="basic-tabs"
      @tab-remove="handleDelete"
      @tab-change="handleChange"
    >
      <el-tab-pane
        v-for="item in currentTab"
        :key="item.renderKey"
        :label="item.name"
        :name="item.id"
        :lazy="true"
        :closable="item.closeable"
        class="inner-tab-content"
      >
        <el-scrollbar @scroll="scrollHandler">
          <component
            :is="item.component"
            :data="item.data"
            :common-sign="item.commonSign"
            :delete-tab-item="() => tabs.deleteTab(item.id, props.group)"
            :update-tab-item="(data: any) => tabs.updateTab(item.id, data)"
          />
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
    <div
      v-if="moveStatus === 2"
      class="marker"
      :style="{ left: markerLeft + 'px' }"
    ></div>
    <el-dialog
      v-model="tabs.remindVisible"
      title="提示"
      :show-close="false"
      :close-on-click-modal="false"
      width="450px"
      :before-close="handleClose"
    >
      <span>{{ tabs.remindText }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="confirmHanlder">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { pick, throttle } from 'lodash-es';
import { useEventListener } from '@vueuse/core';
import store from '@/store';

interface IProps {
  group: string;
  limit?: number;
}

const props = defineProps<IProps>();
const tabs = store.useTabs();
const global = store.useGlobal();

const emits = defineEmits(['tab-change']);
const activeTab = ref();
const currentTab = ref(tabs.getCurrentPatientTabs(props.group));
const moveStatus = ref(0);
const left = ref(0);
const markerLeft = ref(0);
const activeIndex = ref(0);
const targetIndex = ref(0);
const translateX = ref(0);
const changeKey = ref(1);
const tabWidth = ref<number[]>([]);
const containerRef = shallowRef();
watch(
  () => global.userId,
  () => {
    initHandler();
  }
);
watch(
  () => tabs.tabsMap,
  () => {
    tabs.tabsMap;
    currentTab.value = tabs.getCurrentPatientTabs(props.group);
    activeTab.value = tabs.patientActiveTabMap[props.group];
  },
  {
    deep: true,
  }
);
watch(
  () => tabs.patientActiveTabMap,
  val => {
    activeTab.value = val[props.group];
  },
  { deep: true }
);

const scrollHandler = throttle(() => {
  changeKey.value += 1;
}, 16);
provide('outer-position-change', changeKey);
const initHandler = () => {
  tabs.clearCache();
  currentTab.value = tabs.getCurrentPatientTabs(props.group);
  if (currentTab.value) {
    activeTab.value = currentTab.value?.[1]?.id;
    tabs.patientActiveTabMap[props.group] = currentTab.value?.[1]?.id ?? '';
  }
};
const confirmHanlder = () => {
  tabs.remindConfirm();
};
const handleClose = () => {
  tabs.remindVisible = false;
};
const handleChange = (id: string) => {
  tabs.patientActiveTabMap[props.group] = id;
  const curItem = currentTab.value.find(v => v.id === id);
  const data = pick(curItem, ['id', 'key', 'name', 'data', 'group']);
  emits('tab-change', data);
};
const handleDelete = (id: string) => {
  tabs.deleteTab(id, props.group);
};
const hasScroll = () => {
  return containerRef.value?.querySelector('.basic-tabs .el-tabs__nav-prev');
};
const getGap = () => (hasScroll() ? 17 : -3);
const mouseMoveHanlder = throttle((e: MouseEvent) => {
  if (!moveStatus.value) return;
  if (moveStatus.value === 1) {
    moveStatus.value = 2;
  }
  const pageX = e.pageX;
  const arr = [...tabWidth.value, 99999];
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] + left.value >= translateX.value + pageX) {
      if (i > 1) {
        markerLeft.value = arr[i - 1] - translateX.value + getGap();
        targetIndex.value = i;
      }
      break;
    }
  }
}, 16);
const mouseDownHandler = (e: MouseEvent) => {
  const pageX = e.pageX;
  const element = containerRef.value?.querySelector(
    '.basic-tabs .el-tabs__nav'
  );
  const style = element?.getAttribute('style');
  const tlx = Number(style?.match(/(\d+)px/)?.[1] ?? 0);
  const tabItems: HTMLElement[] = Array.from(
    element?.querySelectorAll('.el-tabs__item') ?? []
  );
  translateX.value = tlx;
  if (tabItems) {
    const res: number[] = [];
    let n = 0;
    for (let i = 0; i < tabItems.length; i++) {
      const width = tabItems[i].getBoundingClientRect().width;
      n += width;
      res.push(n);
    }
    tabWidth.value = res;
    const originLeft = tlx + pageX;
    for (let i = 0; i < res.length; i++) {
      if (res[i] + left.value >= originLeft) {
        activeIndex.value = i;
        if (i > 1) {
          markerLeft.value = res[i - 1] - tlx + getGap();
          moveStatus.value = 1;
        }
        break;
      }
    }
  }
};

const mouseUpHandler = () => {
  moveStatus.value = 0;
  if (targetIndex.value === 0 || targetIndex.value === 1) return;
  if (targetIndex.value === activeIndex.value) return;
  if (activeIndex.value === 0 || activeIndex.value === 1) return;
  tabs.changeTabPositions(activeIndex.value, targetIndex.value, props.group);
  targetIndex.value = 0;
  moveStatus.value = 0;
  tabs.refreshTab(currentTab.value[0].id);
};
onMounted(() => {
  initHandler();
  const element = containerRef.value?.querySelector(
    '.basic-tabs .el-tabs__nav'
  );
  const rect = element?.getBoundingClientRect();
  left.value = rect?.left ?? 0;
  useEventListener(element, 'mousedown', mouseDownHandler);
  useEventListener(document, 'mousemove', mouseMoveHanlder);
  useEventListener(document, 'mouseup', mouseUpHandler);
});
</script>
<script lang="ts">
export default {
  name: 'CustomTabs',
};
</script>
<style scoped lang="less">
.marker {
  position: absolute;
  width: 6px;
  height: 36px;
  border: 1px dashed #409eff;
  background: #a0cfff;
  top: 2px;
  z-index: 999;
}
</style>
<style lang="less">
.basic-tabs {
  border: none;
  & > .el-tabs__header {
    user-select: none;
    border-bottom: none;
    .el-tabs__item {
      &:first-child {
        width: 0 !important;
        min-width: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        overflow: hidden;
      }
      transition: none;
      position: relative;
      justify-content: center;
      padding-left: 12px !important;
      padding-right: 12px !important;
      min-width: 100px;
      margin-left: 0 !important;
      color: #203549 !important;
      font-weight: bold;
      border: none !important;
      &:not(.is-active):after {
        position: absolute;
        width: 1px;
        height: 12px;
        content: '/';
        font-size: 0;
        background-color: #bac8d4;
        right: 0;
        top: 14px;
      }
      &.is-active {
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        margin-left: -1px !important;
        box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
      }
    }
  }
  & > .el-tabs__content {
    padding: 0;
    background: #f6f8fb;
  }
}
.inner-tab-content {
  height: calc(100vh - 165px);
  overflow-y: auto;
}
</style>
