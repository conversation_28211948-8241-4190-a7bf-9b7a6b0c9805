<template>
  <HrtDialog
    v-model="props.visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :modal="false"
    width="650px"
    size="large"
    top="5vh"
    class="renew-dialog"
    @open="openDialog"
    @closed="closeDialog"
  >
    <div class="content">
      <div class="head">
        <div class="label">续费会员</div>
        <div class="close-icon" @click="choseDialog">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <div class="main-content">
        <div class="event-item">
          <div class="event-label">当前服务</div>
          <div class="content-box">
            <div class="current-pro">
              <div class="pro-name">{{ currentProduct.productName }}</div>
              <div class="pro-des">
                <span>体验有效期：1年</span>
                <span>¥ : {{ currentProduct.payPrice }}</span>
              </div>
              <div v-if="currentProduct.payType === 4" class="pro-tag">
                体验包
              </div>
            </div>
          </div>
        </div>
        <div class="expire-time">
          <span class="time-label">到期时间</span>
          <span class="time-des">
            {{
              currentProduct.expirationDate
                ? timestampToDateTime(currentProduct.expirationDate)
                : '--'
            }}
          </span>
        </div>
        <div class="expire-time">
          <div class="event-label">续费减免：</div>
          <div class="content-box mt-8">
            <div
              v-for="(item, index) in renewalFeeReductionList"
              :key="index"
              class="mb-8"
            >
              <span>{{ item.time }}</span>
              <span class="ml-48">-{{ item.money }}</span>
            </div>
          </div>
        </div>
        <div class="event-item">
          <div class="event-label">服务管理</div>
          <div class="content-box">
            <div class="all-pro">
              <div
                v-for="(pro, index) in productList"
                :key="index"
                :class="{
                  'pro-detail': true,
                  'active-border': activeIndex === index,
                }"
                @click="choosePackge(index, pro)"
              >
                <div class="name">{{ pro.productName }}</div>
                <div class="time">一年</div>
                <div class="price-box">
                  <span class="money-icon">¥:</span>
                  <span
                    :class="[
                      'origin-price',
                      pro.lowestPrice != pro.price ? 'discount' : '',
                    ]"
                  >
                    {{ pro.price }}
                  </span>
                  <span v-if="pro.lowestPrice != pro.price" class="real-price">
                    {{ pro.lowestPrice }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="event-item">
          <div class="event-label">支付方式</div>
          <div class="content-box">
            <div class="methods-box">
              <div class="radio-box">
                <el-radio v-model="paymentMethod" :label="1">
                  线上支付
                  <span class="des-text">
                    （患者线上自主完成续费，可生成订单支付二维码）
                  </span>
                </el-radio>
              </div>
              <div v-if="paymentMethod === 1" class="next-act">
                <div class="radio-box" style="margin-bottom: 8px">
                  <el-radio v-model="sendOrderType" :label="2">
                    发送订单二维码到患者聊天窗口
                  </el-radio>
                </div>
                <div class="radio-box">
                  <el-radio v-model="sendOrderType" :label="1">
                    推送订单到患者公众号
                  </el-radio>
                </div>
              </div>
              <div class="radio-box">
                <el-radio v-model="paymentMethod" :label="2">
                  现金/线下支付
                  <span class="des-text">
                    （患者线下完成续费，未通过系统完成续费）
                  </span>
                </el-radio>
              </div>
              <div v-if="paymentMethod === 2" class="next-act">
                <div class="input-box">
                  <div>备注</div>
                  <div style="margin-left: 8px">
                    <el-input v-model="marks" placeholder="请输入" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="intelligentBtnBox">
        <div class="cancelBtn" @click="choseDialog">取消</div>
        <el-button class="sureBtn" :disabled="disabled" @click="submit">
          确定
        </el-button>
      </div>
      <div id="qrcode" ref="qrcode" style="display: none"></div>
    </div>
  </HrtDialog>
</template>
<script setup lang="ts">
import { timestampToDateTime, dialogTip } from '../../hooks';
import { HrtDialog } from '@hrt/components';
import QRCode from 'qrcode';
import {
  queryRenewOrderApi,
  queryOrderProductApi,
  queryOfflineRenewOrderApi,
  queryConversationTeamListApi,
  queryOrderSelectReductionListApi,
} from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
const useGlobalInfo = useGlobal();
import store from '@/store';
const imStore = store.useIM();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentProduct: {
    type: Object,
    default: () => ({}),
  },
  modal: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '临床事件',
  },
  userId: {
    type: Number,
    default: 0,
  },
  orderId: {
    type: String,
    required: true,
  },
  vipType: {
    type: Number,
    required: true,
  },
});
const { userId, vipType } = props;

//1线上支付  2线下支付
const paymentMethod = ref<string | number>('');
//2发送二维码  1推送公众号
const sendOrderType = ref('');
const marks = ref('');
const activeIndex = ref(0);
/*续费参数*/
const renewObj = ref<any>({
  userId: '',
  orderId: '',
  productId: '',
  remark: '',
});
const disabled = ref(false);
const productList = ref<any>([]);
const teamNumber = ref('');

/*获取所有产品*/
const allProducts = async () => {
  await queryOrderProductApi({
    patientId: useGlobalInfo.userId,
  }).then((res: any) => {
    if (res.code === 'E000000') {
      if (vipType) {
        productList.value = res.data.filter(
          (v: any) => v.productId === props.currentProduct.productId
        );
      }
    }
  });
};

const openDialog = () => {
  allProducts();
  if (productList.value && productList.value.length) {
    const { productId } = productList.value[0];
    renewObj.value = {
      userId: userId,
      orderId: props.orderId,
      productId: productId,
      remark: '',
    };
  }
  getTeamId();
  queryOrderSelectReductionList();
};

//关闭弹窗重置选项
const closeDialog = () => {
  paymentMethod.value = '';
  sendOrderType.value = '';
  choseDialog();
};

//选择服务包
const choosePackge = (index: number, obj: { productId: any }) => {
  activeIndex.value = index;
  const { productId } = obj;
  renewObj.value = {
    userId: userId,
    orderId: props.orderId,
    productId: productId,
    remark: marks.value,
  };
};

// 关闭弹窗
const emit = defineEmits(['closeDialog', 'getOrderInfo']);
const choseDialog = () => {
  emit('closeDialog');
};

const getTeamId = () => {
  queryConversationTeamListApi({
    patientId: useGlobalInfo.userId,
  }).then((res: any) => {
    const obj = res.data.teamList.find(v => v.teamType == 2);
    if (obj && Object.keys(obj).length) {
      teamNumber.value = obj.teamNumber;
    }
  });
};

const submit = () => {
  if (!productList.value[activeIndex.value]) {
    dialogTip('请选择服务!');
  } else if (!paymentMethod.value) {
    dialogTip('请选择支付方式!');
  } else {
    if (paymentMethod.value === 1) {
      if (!sendOrderType.value) {
        dialogTip('请选择推送方式!');
      } else {
        payDifference(sendOrderType);
        emit('getOrderInfo');
      }
    }
    //线下付款
    if (paymentMethod.value === 2) {
      OfflinePayments();
    }
  }
};

/*线下支付*/
const OfflinePayments = () => {
  disabled.value = true;
  queryOfflineRenewOrderApi({
    remark: marks.value,
    orderId: props.orderId,
  }).then((res: any) => {
    if (res.code === 'E000000') {
      dialogTip('操作成功！', 'success');
      emit('getOrderInfo');
      choseDialog();
      disabled.value = false;
    }
  });
};

/*支付方式*/
const qrcode = ref<any>(null);
const payDifference = (payType: any) => {
  const { ...obj } = renewObj.value;

  let parameter;
  if (vipType) {
    parameter = {
      orderId: props.orderId,
      payType: payType.value,
    };
  } else {
    parameter = {
      ...obj,
      payType: payType.value,
    };
  }
  queryRenewOrderApi(parameter).then((res: any) => {
    if (res.code === 'E000000') {
      emit('getOrderInfo');
      choseDialog();

      /*发送二维码给患者*/
      if (payType.value === 2) {
        renewObj.value.orderId = res.data.orderId;
        // this.getOrderList();

        /*下面是生成二维码*/
        if (qrcode.value) {
          qrcode.value.innerHTML = '';
          QRCode.toDataURL(
            res.data.wxPayQrCode,
            { type: 'image/png' },
            function (err: any, url: any) {
              if (err) throw err;
              qrcode.value = url;
            }
          );

          let blob = dataURLtoBlob(qrcode.value);
          if (blob) {
            // 发送一个图片消息
            setTimeout(() => {
              imStore.sendBlobMsg({
                to: teamNumber.value,
                blob,
                type: 'image',
              });
            }, 600);
          }
        }
        dialogTip('成功', 'success');
      }
      /*微信推送*/
      if (payType.value == 1) {
        renewObj.value.orderId = res.data.orderId;
        dialogTip('操作成功', 'success');
      }
    }
  });
};

const dataURLtoBlob = dataURI => {
  if (dataURI) {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    let ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([ab], { type: mimeString });
    return blob;
  }
};

// 续费减免
interface IRenewalFeeReductionType {
  time: string;
  money: number;
}
const renewalFeeReductionList = ref<IRenewalFeeReductionType[]>([]);
const queryOrderSelectReductionList = () => {
  const params = {
    patientId: Number(useGlobalInfo.userId),
    queryEffectiveData: true,
  };
  queryOrderSelectReductionListApi(params).then((res: any) => {
    renewalFeeReductionList.value = res.map(item => {
      return {
        reductionId: item.reductionId,
        time: item.reductionDesc,
        money: item.reductionAmount,
      };
    });
  });
};
</script>
<style lang="less" scoped>
.renew-dialog {
  .el-dialog__body {
    text-align: left;
    .content {
      .head {
        box-sizing: border-box;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e9e8eb;
        .label {
          font-size: 16px;
          font-weight: bold;
          color: #101b25;
        }
        .close-icon {
          font-size: 18px;
          font-weight: bold;
        }
      }
      .main-content {
        box-sizing: border-box;
        padding: 24px 90px;
        font-size: 14px;
        font-weight: 400;
        .expire-time {
          color: #111111;
          margin-bottom: 16px;
        }
        .time-des {
          color: #dc0101;
          margin-left: 16px;
        }
        .event-item {
          margin-bottom: 24px;
          .event-label {
            font-size: 14px;
            font-weight: bold;
            color: #111111;
            margin-bottom: 12px;
          }
          .content-box {
            width: 100%;
            .current-pro {
              box-sizing: border-box;
              padding: 16px;
              background-color: #0a73e4;
              color: #ffffff;
              border-radius: 4px;
              position: relative;
              background-image: url('../../img/currentpro-bg.png');
              background-repeat: no-repeat;
              background-size: 40%;
              background-position: 80% 100%;
              .pro-name {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 12px;
              }
              .pro-des {
                font-size: 14px;
                font-weight: 400;
                color: #ffffff;
                display: flex;
                justify-content: space-between;
              }
              .pro-tag {
                box-sizing: border-box;
                padding: 4px 16px;
                background: #ffeadb;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                color: #ed6a0c;
                position: absolute;
                right: 0;
                top: 0;
              }
            }
            .all-pro {
              max-height: 360px;
              overflow-y: auto;
              .pro-detail {
                display: flex;
                align-items: center;
                box-sizing: border-box;
                padding: 16px;
                font-size: 14px;
                background-color: #f7f8fa;
                cursor: pointer;
                margin-bottom: 12px;
                background-image: url('../../img/otherpro-bg.png');
                background-repeat: no-repeat;
                background-size: 30%;
                background-position: 85% 50%;
                .name {
                  width: 202px;
                  font-weight: bold;
                  color: #111111;
                }
                .time {
                  margin-left: 10px;
                  font-weight: 400;
                  color: #111111;
                }
                .price-box {
                  margin-left: 42px;
                  .money-icon {
                    color: #111111;
                  }
                  .origin-price {
                    color: #dc0101;
                    font-size: 14px;
                    font-weight: bold;
                    margin: 0 8px;
                    &.discount {
                      text-decoration: line-through;
                      color: #708293;
                    }
                  }
                  .real-price {
                    font-size: 14px;
                    font-weight: bold;
                    color: #dc0101;
                  }
                }
              }
              .active-border {
                border-radius: 4px;
                border: 2px solid #0a73e4;
              }
            }
            .methods-box {
              width: 100% !important;
              .next-act {
                box-sizing: border-box;
                padding: 12px;
                background-color: #f7f8fa;
                margin: 8px;
                .input-box {
                  display: flex;
                  align-items: center;
                  .el-input__inner {
                    width: 240px;
                    height: 32px;
                    background: #ffffff;
                    border-radius: 2px;
                    border: 1px solid #dcdee0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
:deep(.radio-box) {
  .des-text {
    font-size: 14px;
    font-weight: 400;
    color: #708293;
  }
  .el-radio__label {
    font-size: 14px;
    font-weight: 400;
    color: #111111;
  }
  .el-radio__inner {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }

  .el-radio__input.is-checked {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
  }
}
.title {
  font-size: 16px;
  font-family:
    PingFangSC-Medium,
    PingFang SC;
  font-weight: 600;
  color: #101b25;
  border-bottom: 1px solid #e9e8eb;
  padding: 24px 24px 16px 24px;
}
.intelligentBtnBox {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9e8eb;
  .cancelBtn {
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #606266;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
  }
  .sureBtn {
    width: 76px;
    height: 32px;
    background: #0a73e4;
    border-radius: 2px;
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 16px;
  }
}
</style>
<style lang="less">
.renew-dialog {
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
  }
}
</style>
