import { http } from '@/network';
import {
  IApiUserQuestionnaireScoreParams,
  IApiUserQuestionnaireScoreItem,
  IApiUserQuestionnaireLifeQualityScoreParams,
  IApiUserQuestionnaireLifeQualityScore,
} from '@/interface/type';

/**查询患者的生活方式问卷分数折线图 */
export function getQuestionnaireScore(
  params: IApiUserQuestionnaireScoreParams
) {
  return http.post<IApiUserQuestionnaireScoreItem[]>({
    url: '/api/user/questionnaire/score',
    method: 'post',
    data: params,
  });
}

/**查询患者的生活质量问卷分数数据 */
export function getQuestionnaireLifeQualityScore(
  params: IApiUserQuestionnaireLifeQualityScoreParams
) {
  return http.post<IApiUserQuestionnaireLifeQualityScore>({
    url: '/api/user/questionnaire/life/quality/score',
    method: 'post',
    data: params,
  });
}
