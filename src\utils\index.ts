import dayjs from 'dayjs';
import { cloneDeep, get } from 'lodash-es';

interface IUrls {
  url: string;
  filename: string;
}
export const fetchImg = (url: string) => {
  return fetch(url).then(res => res.blob());
};
export const batchDownload = async (list: IUrls[]) => {
  for (const v of list) {
    const res = await fetchImg(v.url);
    createDownload(res, v.filename);
  }
};
export const createDownload = (blob: Blob, filename: string) => {
  const href = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = href;
  link.download = filename;
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

/**生成 uuid */
export const getUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
//获取当天最后时间戳
export const getEndTimeStamp = (val: number) =>
  dayjs(val).add(1, 'day').valueOf() - 1;
//将base64图片转为blob格式
export const convertBase64UrlToBlob = (dataurl: string) => {
  try {
    const arr = dataurl.split(',');
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {
      type: 'image/png',
    });
  } catch (err: any) {
    console.log(err.message);
  }
};
// 100内数字转大小
export const toChineseNumber = (num: number) => {
  const numList = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  if (num < 10) return numList[num];
  if (num === 10) return '十';
  if (num > 10 && num < 20) return '十' + numList[num % 10];
  const ten = Math.floor(num / 10);
  const unit = num % 10;
  if (unit === 0) return numList[ten] + '十';
  return numList[ten] + '十' + numList[unit];
};
export const escapeHtml = (str: string) => {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};
export const escapeHilightChatRecords = (str: string) => {
  const separator = '丨@@@@@丨';
  const reg = /<em style="color:#E63746">(.*?)<\/em>/;
  const matched = str.match(reg);
  const tempVals: string[] = [];
  let result = '';
  if (matched) {
    result = str.replaceAll(new RegExp(reg, 'g'), (_, a: string) => {
      tempVals.push(a);
      return separator;
    });
    result = escapeHtml(result);
    result = result.replaceAll(separator, () => {
      return `<em style="color:#E63746">${tempVals.shift()}<\/em>`;
    });
    return result;
  }
  return str;
};
export const formatDate = (
  time: number | string | undefined,
  options: { showToday?: boolean; showFutureTime?: boolean } = {}
) => {
  if (!time) return '';
  const now = dayjs();
  const { showToday = true, showFutureTime = true } = options;
  const format = ['HH', 'mm', 'ss'].slice(0, 2).join(':');
  const targetDate = dayjs(time);
  if (now.isSame(targetDate, 'day')) {
    return (showToday ? '今日 ' : '') + targetDate.format(format);
  } else if (now.subtract(1, 'day').isSame(targetDate, 'day')) {
    return '昨天 ' + targetDate.format(format);
  } else {
    return targetDate.format(
      'YYYY-MM-DD' + (showFutureTime ? ' ' + format : '')
    );
  }
};

/** 格式化时间 */
type ITime = number | string | Date;
export function formatTime(time: ITime, isReturnArr?: false): string;
export function formatTime(time: ITime, isReturnArr: true): [string, string];
export function formatTime(time: ITime, returnArr?: boolean): any {
  if (!time) {
    if (!returnArr) return '';
    return [];
  }

  const now = dayjs();
  const targetDate = dayjs(time);

  let result;

  if (targetDate.isSame(now, 'day')) {
    result = ['今天', targetDate.format('HH:mm:ss')];
  } else if (targetDate.isSame(now.subtract(1, 'day'), 'day')) {
    result = ['昨天', targetDate.format('HH:mm:ss')];
  } else if (targetDate.isSame(now.add(1, 'day'), 'day')) {
    result = ['明天', targetDate.format('HH:mm:ss')];
  } else {
    result = [targetDate.format('YYYY-MM-DD'), targetDate.format('HH:mm:ss')];
  }

  if (!returnArr) {
    return result.join(' ');
  }

  return result;
}

/** @description 计算 */
export function getBMI(height: number, weight: number) {
  if (!height || !weight) return 0;
  const heightInMeter = height / 100;
  return (weight / (heightInMeter * heightInMeter)).toFixed(1);
}

/** 浮点数计算精度丢失 */
export function formatFloat(f: number, digit = 2) {
  const m = Math.pow(10, digit);
  return Math.round(f * m) / m;
}

/** 扁平化对象 */
export function flattenObj(ob) {
  const result = {};
  for (const i in ob) {
    if (typeof ob[i] === 'object' && !Array.isArray(ob[i])) {
      const temp = flattenObj(ob[i]);
      for (const j in temp) {
        result[j] = temp[j];
      }
    } else {
      result[i] = ob[i];
    }
  }
  return result;
}

//用药时间枚举值
export const medicineTimeList = [
  {
    value: 1,
    label: '早上',
  },
  {
    value: 2,
    label: '中午',
  },
  {
    value: 8,
    label: '下午',
  },
  {
    value: 3,
    label: '晚上',
  },
  {
    value: 4,
    label: '早中晚',
  },
  {
    value: 5,
    label: '早晚',
  },
  {
    value: 6,
    label: '午晚',
  },
  {
    value: 7,
    label: '早午',
  },
];
/** 获取服药时间 */
export const DealDrugData = {
  //获取用药时间str
  getEatDrugTime: value => {
    if (value !== null) {
      const res = medicineTimeList.filter(item => item.value === value);
      return res[0].label;
    } else {
      return '';
    }
  },
  getDrugSpecStr: obj => {
    const ingredients = obj.ingredients + obj.contentUnit;
    const packageContent = obj.packageNum
      ? '*' + obj.packageNum + obj.unit
      : '';
    const packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
    return ingredients + packageContent + packageUnit;
  },
};

/** 格式化时间未指定格式字符串 */
export const formatTimeTemplate = (
  time: string | undefined | number,
  template = 'YYYY-MM-DD HH:mm:ss'
) => {
  if (!time) return undefined;
  return dayjs(time).format(template);
};

export const templateReplace = (
  template: string,
  data: Record<string, any>
) => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => data[key]);
};

export const toCamelCase = str => {
  return str.replace(/([_][a-z])/g, group =>
    group.toUpperCase().replace('_', '')
  );
};

export const getMedicationRequiredFields = () => {
  return [
    'drug_name',
    'drug_amount.ingredients',
    'drug_amount.content_unit',
    'drug_spec.ingredients',
    'drug_spec.content_unit',
    'drug_mode',
    'drug_usage',
    'medicine_time',
  ];
};
export const checkMedicationComplete = (drugList: any[]) => {
  if (!drugList) return false;
  const requiredFields = getMedicationRequiredFields();
  return drugList.every(drug => {
    return requiredFields.every(field => !!get(drug, field));
  });
};
export const checkMedicationEmpty = (drug: any) => {
  if (!drug) return false;
  const requiredFields = getMedicationRequiredFields();
  return requiredFields.every(field => !get(drug, field));
};
export const checkDuplicateMedication = list => {
  const nameList = list.map(v => v.drug_name);
  const filterList = [...new Set(nameList)];
  return nameList.length === filterList.length;
};

export const medicationValidate = (key, data) => {
  const validMedicationList = data[key].filter(v => !checkMedicationEmpty(v));
  if (data[key + '_start_time']) {
    if (validMedicationList?.length === 0) {
      ElMessage.warning('请选择用药');
      return false;
    }
    if (!checkMedicationComplete(validMedicationList)) {
      ElMessage.warning('请填写完整!');
      return false;
    }
    if (!checkDuplicateMedication(validMedicationList)) {
      ElMessage.warning('存在相同药品!请删除重复药品!');
      return false;
    }
  }
  return true;
};
/**动态表单校验 */
export const dynamicFormValidator = (
  formJson: any,
  data: any,
  excludeValidateKeys: string[] = []
) => {
  for (const v of formJson.items) {
    const { key, value, uiRules, uiOptions } = v;
    const uiRulesJson = uiRules ? JSON.parse(uiRules) : {};
    const uiOptionsJson = uiOptions ? JSON.parse(uiOptions) : {};
    if (
      uiRulesJson?.require &&
      !excludeValidateKeys.includes(key) &&
      !data[key]
    ) {
      ElMessage.warning('请选择' + value);
      return false;
    }
    if (uiOptionsJson?.type === 'drug') {
      const res = medicationValidate(key, data);
      if (!res) return false;
    }
  }
  return true;
};
/** 用药数据特殊过滤 */
export const dynamicFormDataFilter = (formJson: any, data: any) => {
  const result = cloneDeep(data);
  for (const v of formJson.items) {
    const { key, uiOptions } = v;
    const uiOptionsJson = uiOptions ? JSON.parse(uiOptions) : {};
    if (uiOptionsJson?.type === 'drug') {
      result[key] = result[key].filter(v => !checkMedicationEmpty(v));
    }
  }
  return result;
};

// 判断是否是url
export const isUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (err) {
    return false;
  }
};
// 获取文件名称
export const getFileName = (url: string): string => {
  let newUrl = url;

  if (!isUrl(url)) {
    newUrl = `${location.origin}${url.startsWith('/') ? url : `/${url}`}`;
  }

  return new URL(newUrl).pathname.split('/').pop() || '';
};

// 获取文件类型
export const getFileType = (url: string): string => {
  const fileName = getFileName(url);

  return fileName.split('.').pop()!.toLowerCase() || '';
};
