<template>
  <div v-for="item in items" :key="item.key">
    <TableForm
      v-if="item.uiMethod === 'table'"
      :mode="mode"
      :type="item.uiOptions?.type"
      :columns="item.columns"
      :items="item.items"
      :value="innerFormData[item.key]"
      :ui-options="item.uiOptions || {}"
      @change="val => formItemChange(item, val)"
    />
    <div v-else>
      <div v-if="mode === 'view'" class="break-all">
        <DiseaseTag :data="getViewTags(item)" />
      </div>
      <BaseSelector
        v-else
        :title="''"
        :list="transformList(item.options, 'value')"
        :selected-data="getFlatIdList(innerFormData[item.key])"
        hide-input
        @on-change="val => changeHandler(item, val)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import DiseaseTag from '@/components/HospitalForm/components/diseaseTag.vue';
import BaseSelector from '@/components/HospitalForm/BaseSelector.vue';
import TableForm from '@/components/FormItem/TableForm.vue';
import { getDataMap, idsTransform } from '@/components/StructuredSelector/util';
import { Base } from './type';
import { formatTreeData, getFlatIdList, transformList } from './utils';
import { cloneDeep, isEqual, keyBy } from 'lodash-es';
interface IProps extends Base {
  items: any[];
}
const props = defineProps<IProps>();
const innerFormData = ref({});
const emit = defineEmits(['change']);
const formItemChange = (item, val) => {
  if (isEqual(innerFormData.value[item.key], val)) return;
  innerFormData.value[item.key] = val;
  emit('change', cloneDeep(innerFormData.value));
};
const changeHandler = (item, val) => {
  const { resultTreeData } = val;
  const listMap = keyBy(item.options ?? [], 'id');
  const res = formatTreeData(resultTreeData, listMap);
  formItemChange(item, res);
};
const getViewTags = item => {
  const { mode } = props;
  if (mode !== 'view') return [];
  const list = transformList(item.options, 'value');
  const dataMap = getDataMap(list);
  const ids = innerFormData.value[item.key]?.map(v => v.id);
  const res = idsTransform(ids, dataMap);
  const { showNameList } = res;
  return showNameList.map(v => v.text);
};
watch(
  () => props.value,
  val => {
    innerFormData.value = cloneDeep(val || {});
  },
  { immediate: true, deep: true }
);
defineOptions({
  name: 'MixTableForm',
});
</script>

<style scoped lang="less">
// todo
</style>
