<template>
  <div
    v-if="useCallStore.isCallLogin === 0"
    class="flex items-center call mr-250"
    @click="loginTel"
  >
    <img class="w-16 h-16 cursor-pointer" :src="callCenter" alt="" />
    <span class="login-call-center cursor-pointer ml-8 text-sm font-bold">
      登录
    </span>
  </div>
  <div v-if="useCallStore.isCallLogin === 1" class="call-box flex items-center">
    <img
      class="w-16 h-16 cursor-pointer mr-12"
      :src="settings"
      alt=""
      @click="settingsCall()"
    />
    <div class="divider mr-12 w-1 h-12"></div>
    <div class="timer mr-12 text-sm">
      接通时常：{{ one < 10 ? '0' + one : one }}:{{
        two < 10 ? '0' + two : two
      }}:{{ three < 10 ? '0' + three : three }}
    </div>
    <div class="divider mr-12 w-1 h-12"></div>
    <div class="sitting-state mr-8 text-sm">坐席状态</div>
    <el-select
      v-model="useCallStore.callType"
      class="m-2 mr-16 select-box w-150"
      placeholder="请选择"
      @change="changeFreeType"
    >
      <el-option
        v-for="item in freeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-input
      v-if="!useCallStore.isCall"
      v-model="useCallStore.callPhone"
      placeholder="输入电话号码外呼"
      clearable
      class="mr-12 ipone-number"
    />
    <div
      v-if="useCallStore.isCall"
      class="call-out flex items-center mr-12 text-sm w-361 h-32"
    >
      <img class="w-16 h-16 mr-12" :src="exhalation" alt="" />
      <div>
        {{ useCallStore.callName }}：{{ useCallStore.callPhone }}（{{
          useCallStore.patientName
        }}{{ useCallStore.relation }}）
      </div>
    </div>
    <img
      v-if="!useCallStore.isCall || useCallStore.isCallInRinging === 1"
      class="w-24 h-24 cursor-pointer mr-10"
      :src="callCenter"
      alt=""
      @click="callNumber()"
    />
    <div class="divider mr-12 w-1 h-12"></div>
    <el-badge v-if="telRecordNum" :value="telRecordNum" class="telRecord">
      <img
        class="h-24 cursor-pointer"
        :src="historyRecord"
        alt=""
        @click="showTelRecord()"
      />
    </el-badge>
    <img
      v-else
      class="h-24 cursor-pointer"
      :src="historyRecord"
      alt=""
      @click="showTelRecord()"
    />
    <div
      :class="useCallStore.callType === '呼入响铃' ? 'mr-30' : 'mr-230'"
    ></div>
    <img
      v-if="useCallStore.isCall || useCallStore.isCallInRinging === 1"
      class="w-24 h-24 cursor-pointer"
      :src="hangup"
      alt=""
      @click="hangupNumber()"
    />
    <!-- 呼叫保持 -->
    <div
      v-if="useCallStore.isCall"
      class="cursor-pointer ml-20 silence-box"
      @click="holdCall"
    >
      <img class="w-16 h-16 mb-2" :src="isConnected ? takeBack : hold" alt="" />
      <div>{{ isConnected ? '接回' : '保持' }}</div>
    </div>

    <!-- 呼叫转移 -->
    <!-- <img
      v-if="useCallStore.isCall"
      class="w-16 h-16 cursor-pointer ml-12"
      :src="transfer"
      alt=""
    /> -->
    <!-- 静音 -->
    <div
      v-if="useCallStore.isCall"
      class="cursor-pointer ml-20 mr-95 silence-box"
      @click="silenceCall"
    >
      <img class="w-16 h-16 mb-2" :src="isSilence ? unmute : silence" alt="" />
      <div>{{ isSilence ? '取消' : '静音' }}</div>
    </div>

    <!-- 满意度 -->
    <!-- <img
      v-if="useCallStore.isCall"
      class="w-16 h-16 cursor-pointer ml-20"
      :src="satisfaction"
      alt=""
    /> -->
  </div>

  <!-- 呼叫中心 -->
  <el-dialog
    v-model="dialogVisibleCallCenter"
    title="呼叫中心"
    width="410px"
    :modal="false"
    class="dialogVisibleCallCenterBox"
  >
    <div class="phoneStyle flex items-center">
      <div class="phoneBox text-sm" style="margin-right: 8px">电话类型</div>
      <el-radio-group v-model="phoneRadio">
        <el-radio :label="1" class="phoneRadio text-sm">普通电话</el-radio>
        <el-radio :label="2" class="phoneRadio text-sm">IP话机</el-radio>
        <el-radio :label="3" class="phoneRadio text-sm">软电话</el-radio>
      </el-radio-group>
    </div>
    <div class="blndPhone mt-20">
      <span class="bindLeft text-sm mr-30">绑定电话</span>
      <span class="bindRight text-sm">
        {{
          phoneRadio === 1
            ? phoneObj.normalel
            : phoneRadio === 2
              ? phoneObj.ipTel
              : phoneObj.softTel
        }}
      </span>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn()">取消</el-button>
        <el-button
          style="background: #2e6be6; color: #ffffff"
          @click="confirmBtn()"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 呼叫列表 -->
  <TelRecord
    :visible="telRecordVisible"
    :record-type="1"
    :show-to-detail="true"
    @close="telRecordVisible = false"
    @clear="() => getRecordNum()"
  />
  <!-- 15s来电显示 -->
  <div
    v-if="useCallStore.dialogVisibleTelephoneCalls"
    class="caller-box flex items-center justify-between w-288 h-74"
  >
    <div class="left-msg-call">
      <div class="flex items-center">
        <img :src="initiative" alt="" class="w-16 h-16 mr-8" />
        <div class="text-base">
          {{
            useCallStore.callName
              ? useCallStore.callName + '(' + useCallStore.relation + ')'
              : useCallStore.callPhone
          }}
        </div>
      </div>
      <div class="text-base ml-24 mt-6">
        {{
          useCallStore.callName
            ? '患者：' + (useCallStore.patientName || useCallStore.callName)
            : '患者未知'
        }}
      </div>
    </div>
    <div class="right-call flex items-center">
      <img
        class="w-24 h-24 cursor-pointer mr-8"
        :src="callCenter"
        alt=""
        @click="callNumber()"
      />
      <img
        class="w-24 h-24 cursor-pointer"
        :src="hangup"
        alt=""
        @click="hangupNumber()"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import callCenter from '@/assets/imgs/callCenter/call-center.png';
import settings from '@/assets/imgs/callCenter/settings.png';
import hangup from '@/assets/imgs/callCenter/hangup.png';
import exhalation from '@/assets/imgs/callCenter/exhalation.png';
import hold from '@/assets/imgs/callCenter/hold.png';
import takeBack from '@/assets/imgs/callCenter/take-back.png';
// import transfer from '@/assets/imgs/callCenter/transfer.png';
import silence from '@/assets/imgs/callCenter/silence.png';
import unmute from '@/assets/imgs/callCenter/unmute.png';
import initiative from '@/assets/imgs/callCenter/initiative-incoming-call.png';
import historyRecord from '@/assets/imgs/callCenter/historyRecord.png';
// import satisfaction from '@/assets/imgs/callCenter/satisfaction.png';
import { queryPatientNameApi, addressMissCalls } from '@/api/addressBook';
import TelRecord from '@/pages/Workbench/Right/components/AddressList/components/TelRecord.vue';
import { onMounted, watch } from 'vue';

import useCall from '@/store/module/useCallCenter';
import useUserStore from '@/store/module/useUserStore';
let useCallStore = useCall();
const userStore = useUserStore();
watch(
  () => useCallStore.callType,
  () => {
    endHandler();
    startHandler();
  }
);
watch(
  () => useCallStore.isCallLogin,
  val => {
    if (val === 1) {
      getRecordNum();
    }
  }
);
const telRecordVisible = ref(false);
const telRecordNum = ref(0);
const getRecordNum = async () => {
  if (useCallStore.isCallLogin === 1) {
    const res: any = await addressMissCalls();
    telRecordNum.value = res;
  }
  return 0;
};
const startLoop = () => {
  getRecordNum().finally(() => {
    setTimeout(
      () => {
        startLoop();
      },
      1000 * 60 * 5
    );
  });
};
let bindingPhone = ref<null | string>('');
onMounted(() => {
  useCallStore.initCallingState();
  bindingPhone.value = sessionStorage.getItem('bindTel');

  let cno = userStore.cno;
  if (cno && cno !== 'null') {
    getPhoneListByCno();
  }
  startLoop();
});

let dialogVisibleCallCenter = ref<boolean>(false);
let phoneRadio = ref<any>(3);
let answeringStyle = localStorage.getItem('answeringStyle');
let phoneObj = ref({
  softTel: '',
  normalel: '',
  ipTel: '',
});
let phoneList = ref([
  { tel: '1013', telType: 3, isBind: 0 },
  { tel: '10133', telType: 4, isBind: 1 },
  { tel: '18161469472', telType: 2, isBind: 0 },
]);

// 计时器
let one = ref(0); // 时
let two = ref(0); // 分
let three = ref(0); // 秒
let abc = ref(0); // 秒的计数
let cde = ref(0); // 分的计数
let efg = ref(0); // 时的计数
let flag = ref();
// 开始计时
let startHandler = () => {
  flag.value = setInterval(() => {
    if (three.value === 60) {
      three.value = 0;
      abc.value = 0;
      if (two.value === 60) {
        two.value = 0;
        cde.value = 0;
        if (efg.value + 1 <= 9) {
          efg.value++;
          one.value = efg.value;
        } else {
          efg.value++;
          one.value = efg.value;
        }
      } else {
        if (cde.value + 1 <= 9) {
          cde.value++;
          two.value = cde.value;
        } else {
          cde.value++;
          two.value = cde.value;
        }
      }
    } else {
      if (abc.value + 1 <= 9) {
        abc.value++;
        three.value = abc.value;
      } else {
        abc.value++;
        three.value = abc.value;
      }
    }
  }, 1000);
};
// 重新开始计时
let endHandler = () => {
  clearInterval(flag.value);
  one.value = 0;
  two.value = 0;
  three.value = 0;
  abc.value = 0;
  cde.value = 0;
  efg.value = 0;
};

// 切换话机状态
let changeFreeType = () => {
  endHandler();
  startHandler();
  let params = {};
  if (useCallStore.callType === '2') {
    params = { cno: userStore.cno };
    (window as any).ClinkAgent.unpause_client(params);
  } else if (useCallStore.callType === '1') {
    params = { cno: userStore.cno };
    (window as any).ClinkAgent.pause_client(params);
  }
};

// 话机设置---修改接听电话方式
let settingsCall = () => {
  dialogVisibleCallCenter.value = true;
  phoneList.value.forEach((item: { telType: number; tel: string | null }) => {
    if (item.telType - 1 == phoneRadio.value) {
      bindingPhone.value = item.tel;
    }
  });
};

const showTelRecord = () => {
  telRecordVisible.value = true;
};
// 是否保持通话
let isConnected = ref(false);
let holdCall = () => {
  let params = ref({});
  // 接回通话
  if (isConnected.value) {
    (window as any).ClinkAgent.unhold(params);
  }
  // 保持通话
  if (!isConnected.value) {
    (window as any).ClinkAgent.hold(params);
  }
  isConnected.value = !isConnected.value;
};

// 是否静音
let isSilence = ref(false);
let silenceCall = () => {
  let params = ref({});
  // 取消静音
  if (isSilence.value) {
    params.value = { direction: 'in' };
    (window as any).ClinkAgent.unmute(params);
  }
  // 静音
  if (!isSilence.value) {
    params.value = { direction: 'in' };
    (window as any).ClinkAgent.mute(params);
  }
  isSilence.value = !isSilence.value;
};

// 拨打电话
let callNumber = () => {
  if (useCallStore.isCallInRinging === 1) {
    if (phoneRadio.value == '3') {
      (window as any).ClinkAgent.sipLink();
    } else {
      return getPrompt('该接听方式不支持软电话接听!', 'warning');
    }
  } else if (useCallStore.isCallInRinging === 0) {
    if (useCallStore.callPhone) {
      useCallStore.initCallingState();
      useCallStore.tempCallPhone = useCallStore.callPhone;
      (window as any).ClinkAgent.previewOutcall({
        tel: useCallStore.callPhone,
      });
    } else {
      return getPrompt('请填写电话!', 'warning');
    }
  }
};

// 挂断电话
let hangupNumber = () => {
  if (phoneRadio.value === '3') {
    (window as any).ClinkAgent.sipUnlink();
  } else {
    (window as any).ClinkAgent.previewOutcallCancel({});
  }
};

const freeOptions = [
  {
    value: '2',
    label: '空闲',
  },
  {
    value: '1',
    label: '忙碌',
  },
];

// 呼叫中心弹窗取消事件
let cancelBtn = () => {
  dialogVisibleCallCenter.value = false;
};

// 呼叫中心弹窗确认事件
let confirmBtn = () => {
  answeringStyle =
    phoneRadio.value === 1
      ? '普通电话'
      : phoneRadio.value === 2
        ? 'IP话机'
        : '软电话';

  localStorage.setItem('answeringStyle', answeringStyle);
  localStorage.setItem('phoneRadio', phoneRadio.value);
  loginTel();
  dialogVisibleCallCenter.value = false;
};

// 电话登录
let loginTel = () => {
  let isCallLogin = userStore.isLogin;
  if (!isCallLogin) {
    getPrompt('该账号不可用于登录呼叫中心！', 'warning');
  } else {
    const curCno = userStore.cno;
    if (curCno) {
      let cno = curCno;
      let password = userStore.seatsPassword;
      let identifier = userStore.enterpriseCode;
      let softTel = phoneList.value.filter(item => item.telType === 4);
      let normalel = phoneList.value.filter(item => item.telType === 2);
      let ipTel = phoneList.value.filter(item => item.telType === 3);
      const params = {
        identifier, //企业编码 当showEnterprise为false时必填，否则忽略

        cno, //座席工号 规则同identifier

        password, // 密码 规则同identifier

        bindTel:
          phoneRadio.value === 1
            ? normalel[0].tel
            : phoneRadio.value === 2
              ? ipTel[0].tel
              : softTel[0].tel, //绑定电话

        bindType: phoneRadio.value, // 绑定类型，1：普通电话、2：IP话机、3：软电话

        loginStatus: 1, //座席初始登录状态 当showAgentInitStatus为false时必填，否则忽略
      };

      (window as any).ClinkAgent.login(params);
    } else {
      getPrompt('您尚未绑定呼叫中心！', 'warning');
    }
  }
};

// 查询坐席电话列表
let getPhoneListByCno = async () => {
  await queryPatientNameApi({
    cno: userStore.cno ?? null,
  })
    .then((res: any) => {
      if (res.code == 'E000000') {
        phoneList.value = res.data.clientTelModelList;
        phoneObj.value.softTel = res.data.clientTelModelList.filter(
          (item: { telType: number }) => item.telType === 4
        )[0].tel;
        phoneObj.value.normalel = res.data.clientTelModelList.filter(
          (item: { telType: number }) => item.telType === 2
        )[0].tel;
        phoneObj.value.ipTel = res.data.clientTelModelList.filter(
          (item: { telType: number }) => item.telType === 3
        )[0].tel;
      }
    })
    .catch(() => {});
};

// 提醒消息
let getPrompt = (msg: string, style: string) => {
  let obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'error',
  };
  let newObj = obj as any;
  ElMessage(newObj);
};
</script>
<style scoped lang="less">
// 15s来电显示
.caller-box {
  background: #000000;
  box-shadow: 0px 4px 12px 0px rgba(8, 38, 99, 0.2);
  border-radius: 4px;
  opacity: 0.8;
  position: fixed;
  right: 16px;
  top: 78px;
  z-index: 99;
  padding: 12px;
  box-sizing: border-box;
}
.call {
  .login-call-center {
    color: #ffffff;
  }
}
.call-box {
  .silence-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
  }
  .call-out {
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #b8becc;
    padding-left: 12px;
    box-sizing: border-box;
    color: #3a4762;
  }
  .timer {
    white-space: nowrap;
    color: #ffffff;
  }
  .divider {
    border: 1px solid #ffffff;
  }
  .sitting-state {
    color: #ffffff;
    white-space: nowrap;
  }
  .ipone-number,
  .select-box {
    width: 150px;
  }
}
// 呼叫中心
:deep(.dialogVisibleCallCenterBox) {
  .el-dialog__header {
    padding: 0;
    height: 50px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 20px;
    .el-dialog__headerbtn {
      top: 0;
      right: 16px;
    }
  }
  .el-dialog__body {
    text-align: left;
    padding: 20px;
    .phoneStyle {
      .phoneBox {
        color: #303133;
      }
      .phoneRadio {
        margin-right: 0;
        margin-left: 30px;
        color: #303133;
      }
    }
    .blndPhone {
      span {
        color: #303133;
      }
      .bindLeft {
        font-weight: 400;
      }
      .bindRight {
        font-weight: 500;
      }
    }
    .contentList {
      margin-top: 13px;
      .el-collapse-item {
        .el-collapse-item__arrow {
          display: none;
        }
        .el-collapse-item__header {
          background: #f7f8fa;
        }
      }
      .el-collapse-item__content {
        padding-bottom: 0;
      }
    }
  }
  .el-dialog__footer {
    padding-top: 0;
  }
}
.telRecord {
  :deep(.el-badge__content) {
    border: none;
  }
}
</style>
