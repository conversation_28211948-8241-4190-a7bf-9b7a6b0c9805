/** 质疑类型 */
export enum DoubtType {
  SCHEME_INSPECT = 'SCHEME_INSPECT',
  DATA_INSPECT = 'DATA_INSPECT',
  AUDIT_INSPECT = 'AUDIT_INSPECT',
  OTHER = 'OTHER_INSPECT',
}
export const DoubtTypeMap = {
  [DoubtType.SCHEME_INSPECT]: '方案监查',
  [DoubtType.DATA_INSPECT]: '数据核查',
  [DoubtType.AUDIT_INSPECT]: '审计监察',
  [DoubtType.OTHER]: '其他',
};

/** 质疑类型 */
export enum DoubtPriority {
  HIGH = 'HIGH',
  MID = 'MID',
  LOW = 'LOW',
}
export const DoubtPriorityMap = {
  [DoubtPriority.HIGH]: '高',
  [DoubtPriority.MID]: '高',
  [DoubtPriority.LOW]: '高',
};

/** 质疑类型 */
export enum DoubtStatus {
  INIT = 'INIT',
  WAIT_RECEIVE = 'WAIT_RECEIVE',
  INSPECTING = 'INSPECTING',
  WAIT_AUDIT = 'WAIT_AUDIT',
  CLOSED = 'CLOSED',
  COMPLETED = 'COMPLETED',
}
export const DoubtStatusMap = {
  [DoubtStatus.INIT]: '初始化',
  [DoubtStatus.WAIT_RECEIVE]: '待接收',
  [DoubtStatus.INSPECTING]: '调查中',
  [DoubtStatus.WAIT_AUDIT]: '待审查',
  [DoubtStatus.CLOSED]: '已关闭',
  [DoubtStatus.COMPLETED]: '已归档',
};
