<template>
  <div class="record-list">
    <el-drawer
      v-model="drawerVisible"
      :direction="direction || 'rtl'"
      :close-delay="200"
      class=""
      @close="close"
    >
      <template #header>
        <div>通话记录</div>
      </template>
      <template #default>
        <div class="search-box mb-24">
          <div class="search-item flex items-center mb-18">
            <div class="title">记录列表</div>
          </div>
          <div
            v-if="recordType === 1"
            class="search-item flex items-center mb-18"
          >
            <div class="title mr-8">患者姓名</div>
            <div class="item-content flex items-center">
              <el-select
                v-model="form.keyword"
                filterable
                remote
                reserve-keyword
                placeholder="患者姓名/手机号"
                :remote-method="remoteMethod"
                :loading="loading"
                style="width: 240px"
                class="search-input"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="flex items-center mb-18 flex-wrap">
            <div class="search-item flex items-center mr-70">
              <div class="title mr-8">呼叫类型</div>
              <div class="item-content flex items-center">
                <el-select
                  v-model="form.type"
                  class="m-2 mr-16"
                  placeholder="请选择"
                  style="width: 120px"
                  @change="changeCallOut"
                >
                  <el-option
                    v-for="item in callOutOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
            <div class="search-item flex items-center">
              <div class="title mr-8">接听状态</div>
              <div class="item-content flex items-center">
                <el-select
                  v-model="form.status"
                  class="m-2 mr-16"
                  placeholder="请选择"
                  style="width: 120px"
                  @change="changeAnswer"
                >
                  <el-option
                    v-for="item in answerStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </div>
          <div class="search-item flex items-center mb-24">
            <div class="title mr-8">呼叫时间</div>
            <div class="item-content flex items-center change-time-box">
              <el-date-picker
                v-model="form.callTime"
                :disabled-date="disabledDateFn"
                type="daterange"
                clearable
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                class="date-picker-box"
                value-format="YYYY-MM-DD"
                @change="changeTime"
              />
              <!-- <img
                :src="changeTimeImg"
                alt=""
                class="w-18 h-18 change-time-img"
              /> -->
            </div>
          </div>
          <div class="btns flex items-center justify-center">
            <div
              class="search flex items-center justify-center mr-8 cursor-pointer"
              @click="search"
            >
              搜索
            </div>
            <div
              class="reset flex items-center justify-center cursor-pointer"
              @click="reset"
            >
              重置
            </div>
          </div>
        </div>
        <ul
          v-infinite-scroll="load"
          class="list-box"
          :infinite-scroll-immediate="false"
        >
          <li
            v-for="item in recordList"
            :key="item.uniqueId"
            class="item mb-12"
          >
            <div class="item-header-box">
              <div class="module-one flex items-center justify-between mb-8">
                <div class="one-left flex items-center">
                  {{ item.name
                  }}<span class="relation">{{
                    item.relation ? '(' + item.relation + ')' : ''
                  }}</span>
                  <div
                    v-if="showToDetail"
                    class="detail"
                    @click="() => toDetail(item)"
                  >
                    患者资料 >
                  </div>
                </div>
                <div class="one-right flex items-center">
                  <img
                    v-if="
                      item.callType === '预览外呼' ||
                      item.callType === '直接外呼' ||
                      item.callType === '主叫外呼'
                    "
                    :src="exhalationImg"
                    alt=""
                    class="w-18 h-18 mr-8"
                  />
                  <img
                    v-if="item.callType === '呼入'"
                    :src="incomingCall"
                    alt=""
                    class="w-18 h-18 mr-8"
                  />
                  <span
                    v-if="
                      item.callType === '预览外呼' ||
                      item.callType === '直接外呼' ||
                      item.callType === '主叫外呼'
                    "
                    >呼出</span
                  >
                  <span v-if="item.callType === '呼入'">呼入</span>
                  <span class="ml-8">{{ formatTime(item.callTime) }}</span>
                </div>
              </div>
              <div class="module-one flex items-center justify-between">
                <div class="one-left flex items-center">
                  <span class="relation">{{ item.customerNumber }}</span
                  ><span class="relation"
                    >（{{ item.customerProvince }}/{{
                      item.customerCity
                    }}）</span
                  >
                </div>
                <div class="one-right flex items-center">
                  <div
                    v-if="
                      item.callType === '呼入' && item.status === '座席未接听'
                    "
                    style="color: #e63746"
                  >
                    座席未接听
                  </div>
                  <div
                    v-else-if="
                      (item.callType === '预览外呼' ||
                        item.callType === '直接外呼') &&
                      item.status === '客户未接听'
                    "
                    style="color: #e63746"
                  >
                    用户未接听
                  </div>
                  <div
                    v-else-if="
                      (item.callType === '预览外呼' ||
                        item.callType === '直接外呼') &&
                      item.status === '座席未接听'
                    "
                    style="color: #e63746"
                  >
                    座席未接听
                  </div>
                  <div
                    v-else-if="
                      item.status === '双方接听' || item.status === '座席接听'
                    "
                    style="color: #28c445"
                  >
                    双方接听
                  </div>
                  <div v-else style="color: #e63746">系统应答</div>
                  <span
                    >(去电{{
                      calculateBridgeDuration(item.bridgeDuration)
                    }})</span
                  >
                </div>
              </div>
            </div>
            <div class="query-details flex items-center justify-center mt-12">
              <span class="mr-8 cursor-pointer" @click="queryDetails(item)">{{
                item.flag ? '收起详情' : '展开详情'
              }}</span>
              <el-icon
                v-if="item.flag"
                size="12px"
                class="cursor-pointer"
                @click="item.flag = !item.flag"
              >
                <i-ep-arrow-up-bold color="#2E6BE6" />
              </el-icon>
              <el-icon
                v-else
                size="12px"
                class="cursor-pointer"
                @click="item.flag = !item.flag"
              >
                <i-ep-arrow-down-bold color="#2E6BE6" />
              </el-icon>
            </div>
            <div v-if="item.flag" class="mt-16">
              <Details
                :details-info="detailsInfo"
                :banquet-info="banquetInfo"
              />
            </div>
          </li>
        </ul>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import changeTimeImg from '@/assets/imgs/callCenter/change-time.png';
import exhalationImg from '@/assets/imgs/callCenter/exhalation-img.png';
import incomingCall from '@/assets/imgs/callCenter/incoming-call.png';
import Details from './Details.vue';
import { addressCallListApi } from '@/api/addressBook';
import { formatTime } from '@/utils/index';
import {
  queryCallDetailsApi,
  queryPatientRelevanceMsgApi,
  cleanAddressSign,
} from '@/api/addressBook';
import useGlobal from '@/store/module/useGlobal';
import { calculateBridgeDuration, convertTime } from '../index';
import { formInfo, info, ListItem } from '../type';
import { debounce } from 'lodash-es';
import dayjs from 'dayjs';
import useUserStore from '@/store/module/useUserStore';

interface IProps {
  visible: boolean;
  direction?: string;
  showToDetail?: boolean;
  //1--所有记录  2--当前患者
  recordType: 1 | 2;
  immediate?: boolean;
}

const props = defineProps<IProps>();
const emits = defineEmits(['change', 'close', 'clear']);
const useGlobalInfo = useGlobal();
const userStore = useUserStore();
const defaultForm = () => ({
  keyword: '',
  type: 1,
  callTime: '',
  startTime: '',
  endTime: '',
  status: 0,
  pageSize: 10,
  page: 1,
  cno: userStore.cno,
  patientId: useGlobalInfo.userId,
});
const drawerVisible = ref(false);
// 通话记录列表
const recordList = ref<any>([]);
// 数据总页数
const totalNumber = ref<number>(0);
const form = ref<formInfo>({ ...defaultForm() });
// 呼叫状态
const callOutOptions = ref([
  {
    value: 1,
    label: '呼入',
  },
  {
    value: 2,
    label: '呼出',
  },
]);

const detailsInfo = ref<info>({
  customerNumber: '',
  hotline: '',
  callType: '',
  status: '',
  totalDuration: '',
  startTime: '',
  bridgeTime: '',
  endTime: '',
  qno: '',
  cno: '',
  clientNumber: '',
});

const clearUnread = async (uniqueId: string) => {
  if (form.value.type === 2) return;
  await cleanAddressSign({ uniqueId });
  emits('clear');
};
const toDetail = (item: any) => {
  clearUnread(item.uniqueId);
  if (item.patientId) {
    useGlobalInfo.userId = item.patientId;
    close();
  }
};
const banquetInfo = ref<any>({
  clientName: '',
  cno: '',
  clientNumber: '',
  callType: '',
  sipCause: '',
  status: '',
  remember: '',
  startTime: '',
  answerTime: '',
  totalDuration: '',
});
const loading = ref(false);
const options = ref<ListItem[]>([]);
// 重置
const reset = () => {
  resetForm();
  recordList.value = [];
  getList();
};
const getReqParams = () => {
  const res: any = { ...form.value };
  if (props.recordType === 1) {
    res.patientId = '';
  }
  if (res.status === 0) {
    res.status = null;
  }
  delete res.callTime;
  return res;
};
// 获取通话记录列表
const getList = () => {
  const params = getReqParams();
  addressCallListApi(params).then((res: any) => {
    let { code, data } = res;
    if (code === 'E000000' && data && data.contents && data.contents.length) {
      // 首页展示的一条数据
      const item = data.contents[0];
      emits('change', {
        ipone: item.customerNumber,
        time: formatTime(item.callTime),
        name: item.name,
        relation: item.relation,
        num: calculateBridgeDuration(item.bridgeDuration),
      });
      data.contents.forEach((item: { flag: boolean }) => (item.flag = false));
      recordList.value = [...recordList.value, ...data.contents];

      totalNumber.value = Math.ceil(data.total / 10);
    } else {
      recordList.value = [];
      emits('change', {
        time: '--',
        num: '--',
        relation: '--',
        ipone: '--',
        name: '--',
      });
    }
  });
};
const remoteMethod = (query: string) => {
  options.value = [];
  remoteMethodQuery(query);
};
const remoteMethodQuery = debounce((query: string) => {
  if (query) {
    loading.value = true;
    queryPatientRelevanceMsgApi({ keyword: query })
      .then((res: any) => {
        if (res.code === 'E000000' && res.data && res.data.length) {
          options.value = res.data.map((item: { phone: any; name: any }) => {
            return {
              value: item.phone,
              label: item.name,
            };
          });
        } else {
          options.value = [];
        }
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    options.value = [];
  }
}, 500);

// 选择呼叫状态
let changeCallOut = () => {
  form.value.status = 0;
};

// 接听状态
const answerStatusList = computed(() => {
  if (form.value.type === 2) {
    return [
      {
        value: 0,
        label: '全部',
      },
      {
        value: 1,
        label: '客户未接听',
      },
      {
        value: 2,
        label: '座席未接听',
      },
      {
        value: 3,
        label: '双方接听',
      },
    ];
  } else {
    return [
      {
        value: 0,
        label: '全部',
      },
      {
        value: 1,
        label: '座席接听',
      },
      {
        value: 2,
        label: '来电未接听',
      },
      {
        value: 3,
        label: '系统接听',
      },
    ];
  }
});
// 选择接听状态
const changeAnswer = () => {};

// 选择时间
const changeTime = () => {
  form.value.startTime = form.value.callTime[0];
  form.value.endTime = form.value.callTime[1];
};
const resetForm = () => {
  form.value = { ...defaultForm() };
};
// 抽屉关闭
const close = () => {
  resetForm();
  emits('close');
  // getList();
};
// 限制日期选择时间
const disabledDateFn = (time: { getTime: () => number }) => {
  let year = new Date().getFullYear();
  let month = new Date().getMonth();
  let days = new Date(year, month, 1).getTime();
  return days > time.getTime() || time.getTime() > Date.now() - 8.64e6;
};
// 加载到底部分页加载数据
const load = () => {
  if (props.visible && form.value.page < totalNumber.value) {
    form.value.page++;
    getList();
  }
};
// 搜索
const search = () => {
  const { startTime, endTime } = form.value;
  form.value.page = 1;
  form.value.startTime = startTime ? dayjs(startTime).valueOf() : '';
  form.value.endTime = endTime
    ? dayjs(endTime).add(1, 'day').valueOf() - 1
    : '';
  recordList.value = [];
  getList();
};
// 查看详情
const queryDetails = (item: { flag: boolean; uniqueId: string }) => {
  if (!item.flag) {
    if (props.recordType === 1) {
      clearUnread(item.uniqueId);
    }
    queryCallDetailsApi({
      mainUniqueId: item.uniqueId,
      type: form.value.type,
    }).then((res: any) => {
      let { data, code } = res;
      if (code === 'E000000') {
        // 呼叫坐席
        banquetInfo.value = data.callAgentList[0];
        if (data.callAgentList.length) {
          banquetInfo.value.startTime = formatTime(
            banquetInfo.value.startTime * 1000
          );
          banquetInfo.value.answerTime = formatTime(
            banquetInfo.value.answerTime * 1000
          );
          banquetInfo.value.totalDuration = convertTime(
            banquetInfo.value.totalDuration
          );
        }

        // 通话详情
        data.callBackInfo.totalDuration = convertTime(
          data.callBackInfo.totalDuration
        );
        data.callBackInfo.startTime = formatTime(
          data.callBackInfo.startTime * 1000
        );
        data.callBackInfo.endTime = formatTime(
          data.callBackInfo.endTime * 1000
        );
        data.callBackInfo.bridgeTime = formatTime(
          data.callBackInfo.bridgeTime * 1000
        );
        detailsInfo.value = data.callBackInfo;
        item.flag = !item.flag;
      }
    });
  } else {
    item.flag = !item.flag;
  }
};
onMounted(() => {});
watch(
  () => useGlobalInfo.userId,
  () => {
    recordList.value = [];
    if (useGlobalInfo.userId && props.immediate) {
      form.value.patientId = useGlobalInfo.userId;
      getList();
    }
  },
  {
    deep: true,
  }
);
watch(
  () => props.visible,
  val => {
    drawerVisible.value = val;
    if (val) {
      recordList.value = [];
      getList();
    }
  }
);
defineOptions({
  name: 'TelRecord',
});
</script>

<style scoped lang="less">
.record-list {
  :deep(.el-drawer) {
    height: calc(100% - 60px);
    top: 60px;

    .el-drawer__header {
      margin-bottom: 0;
      padding: 12px 16px;
      border-bottom: 1px solid #e9e8eb;
      font-size: 16px;
      font-weight: 600;
      color: #101b25;
    }
    .el-drawer__body {
      display: flex;
      flex-direction: column;
      padding: 0;
      .search-box {
        padding: 16px;

        .search-item {
          .title {
            font-size: 14px;
            color: #111111;
          }
          .item-content {
            .date-picker-box {
              width: 50%;
              .el-range-separator {
                width: 50px;
              }
              .el-range-input {
                width: 45%;
              }
              .el-range__icon {
                display: none;
              }
            }
            .all-box {
              .change-box {
                width: 14px;
                height: 14px;
                background: #ffffff;
                border-radius: 8px;
                border: 1px solid #dcdee0;
              }
              .change-box-checked {
                width: 14px;
                height: 14px;
                background: #ffffff;
                border-radius: 8px;
                border: 1px solid #2e6be6;
                .interior-check {
                  width: 10px;
                  height: 10px;
                  background: #0a73e4;
                  border-radius: 8px;
                }
              }
              .change-title {
                font-size: 14px;
                color: #3a4762;
              }
            }
            .search-input {
              width: 240px;
            }
          }
          .change-time-box {
            position: relative;
            .change-time-img {
              position: absolute;
              right: 8px;
            }
          }
        }
        .btns {
          .search {
            width: 76px;
            height: 32px;
            background: #2e6be6;
            border-radius: 2px;
            font-size: 14px;
            color: #ffffff;
          }
          .reset {
            width: 76px;
            height: 32px;
            border-radius: 2px;
            border: 1px solid #dcdfe6;
            box-sizing: border-box;
            font-size: 14px;
            color: #606266;
          }
        }
      }
      .list-box {
        flex: 1;
        background: #f7f8fa;
        overflow-y: scroll;
        box-sizing: border-box;
        padding: 16px;

        .item {
          background: #ffffff;
          box-shadow: 0px 0px 4px 0px rgba(10, 42, 97, 0.1);
          border-radius: 4px;
          box-sizing: border-box;
          padding: 16px;

          .item-header-box {
            border-bottom: 1px solid #e9e8eb;
            padding-bottom: 16px;
            box-sizing: border-box;
            .detail {
              cursor: pointer;
              color: #2e6be6;
              font-weight: 400;
              margin-left: 8px;
            }
            .module-one {
              .one-left {
                font-size: 14px;
                font-weight: 600;
                color: #101b25;
                .relation {
                  color: #708293;
                  font-weight: 500;
                }
              }
              .one-right {
                font-size: 14px;
                color: #708293;
              }
            }
          }
          .query-details {
            font-size: 14px;
            color: #2e6be6;
          }
        }
      }

      /*修改滚动条样式*/
      .list-box::-webkit-scrollbar {
        width: 0;
        height: 0;
        /**/
      }

      .list-box::-webkit-scrollbar-track {
        background: rgb(239, 239, 239);
        border-radius: 2px;
      }

      .list-box::-webkit-scrollbar-thumb {
        background: #bfbfbf;
        border-radius: 0;
      }
    }
  }
}
</style>
