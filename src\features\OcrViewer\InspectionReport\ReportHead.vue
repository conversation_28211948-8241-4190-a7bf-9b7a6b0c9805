<template>
  <div class="double-block">
    <div v-if="!locatedPatientHistoryId" class="form-block">
      <div class="form-label">
        <span class="required-icon">*</span>
        类型
      </div>
      <div class="form-content">
        <div class="select-box large-width">
          <el-select
            v-model="indexParams.type"
            placeholder="请选择保存类型"
            @change="changeSaveType"
          >
            <el-option
              v-for="item in saveOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div
      v-if="indexParams.type !== 2 && !locatedPatientHistoryId"
      class="form-block"
    >
      <div class="form-label">
        <span class="required-icon">*</span>
        记录日期
      </div>
      <div class="form-content">
        <div class="select-box large-width">
          <el-select
            v-model="indexParams.patientHistoryId"
            placeholder="请选择记录"
          >
            <el-option
              v-for="(item, index) in timeOptions"
              :key="index"
              :label="item.name"
              :value="item.patientHistoryId"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div v-if="indexParams.type === 2" class="form-block">
      <div class="form-label">
        <span class="required-icon">*</span>
        复查日期
      </div>
      <div class="form-content">
        <div class="select-box large-width">
          <el-select
            v-model="indexParams.patientHistoryId"
            placeholder="请选择记录"
          >
            <el-option
              v-for="(item, index) in timeOptions"
              :key="index"
              :label="item.name"
              :value="item.patientHistoryId"
            />
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getInHospitalRecords,
  getOutPatientHospitalRecords,
  getReviewList,
} from '@/api/ocr';

import { TypeCheckHeadParams, TypeRecordReqMap, TypeTimeOptions } from './type';

import { useOcrScan } from '@/store/module/useOcrScan';

import useGlobal from '@/store/module/useGlobal';

import dayjs from 'dayjs';

const globalData = useGlobal();

const ocrStore = useOcrScan();

const emit = defineEmits(['getReportHeadParams']);

const locatedPatientHistoryId = computed(
  () => inject('locatedPatientHistoryId') ?? null
);

const indexParams = ref<TypeCheckHeadParams>({
  type: 0,
  patientHistoryId: null,
});

const saveOptions = ref([
  {
    label: '住院',
    value: 0,
  },
  {
    label: '门诊',
    value: 1,
  },
  {
    label: '复查',
    value: 2,
  },
]);

const timeOptions = ref<TypeTimeOptions[]>([]);

const recordRequestMap: TypeRecordReqMap = {
  0: getInHospitalRecords,
  1: getOutPatientHospitalRecords,
  2: getReviewList,
};

const changeSaveType = () => {
  indexParams.value.patientHistoryId = null;
  timeOptions.value = [];
  getRecordsList();
};

const stateMap: { [key: string]: { type: string; label: string } } = {
  1: { type: 'info', label: '未开始' },
  32: { type: 'danger', label: '未上传' },
  64: { type: 'success', label: '已上传' },
  128: { type: 'info', label: '已完成' },
  '-1': { type: 'info', label: '已失效' },
};

const getRecordsList = () => {
  recordRequestMap[indexParams.value.type]({
    userId: globalData.userId,
  }).then(data => {
    if (indexParams.value.type !== 2) {
      if ((data as any).historyInfo) {
        if (indexParams.value.type === 0) {
          (data as any).historyInfo.forEach((item: any) => {
            const { patientHistoryId, isGroup } = item;
            timeOptions.value.push({
              patientHistoryId: patientHistoryId,
              name:
                dayjs(item.inTime).format('YYYY-MM-DD') +
                '' +
                (isGroup ? ' 入组' : ' 入院'),
            });
          });
        }
        if (indexParams.value.type === 1) {
          (data as any).historyInfo.forEach((item: any) => {
            const { patientHistoryId } = item;
            timeOptions.value.push({
              patientHistoryId: patientHistoryId,
              name: dayjs(item.inTime).format('YYYY-MM-DD') + '' + ' 门诊',
            });
          });
        }
      }
    } else {
      if ((data as any).reviewInfo) {
        (data as any).reviewInfo.forEach((item: any) => {
          const { reviewId } = item;
          timeOptions.value.push({
            patientHistoryId: reviewId,
            name:
              dayjs(item.date).format('YYYY-MM-DD') +
              ' ' +
              (item.times === 0 ? '自定义复查' : `第${item.times}次复查`) +
              ' ' +
              stateMap[item.status || '-1'].label,
          });
        });
      }
    }
  });
};

watch(
  () => indexParams,
  newVal => {
    emit('getReportHeadParams', newVal.value);
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  getRecordsList();
});
</script>
<style scoped lang="less">
.form-block {
  margin-bottom: 16px;
  width: 100%;
  .form-label {
    font-size: 14px;
    font-weight: bold;
    color: #111111;
    margin-bottom: 8px;
    .required-icon {
      font-weight: bold;
      color: #ea1212;
    }
  }
}
.double-block {
  display: flex;
  flex-wrap: wrap;
  width: 70%;
  .form-block {
    flex: 1;
  }
}

.select-box {
  :deep(.el-select) {
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
    padding: 0px 11px;
  }
}
.large-width {
  :deep(.el-select) {
    width: 240px;
  }
}
</style>
