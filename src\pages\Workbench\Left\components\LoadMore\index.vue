<template>
  <el-scrollbar
    ref="scrollRef"
    class="scroll-list"
    :max-height="height"
    @scroll="handleScroll"
  >
    <div v-infinite-scroll="loadHandler" :infinite-scroll-immediate="false">
      <slot></slot>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
interface IProps {
  height: number;
  immediate?: boolean;
}
withDefaults(defineProps<IProps>(), {
  immediate: false,
});
const scrollRef = shallowRef();
const emits = defineEmits(['load', 'scroll']);
const loadHandler = () => {
  emits('load');
};
const handleScroll = e => {
  emits('scroll', e);
};
defineOptions({
  name: 'LoadMore',
});
defineExpose({
  setScrollTop: (top: number) => scrollRef.value.setScrollTop(top),
});
</script>

<style scoped lang="less">
.scroll-list {
  padding-right: 8px;
}
</style>
