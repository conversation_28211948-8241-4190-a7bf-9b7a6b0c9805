import { http } from '@/network';

// 登录
interface LoginResultModel {
  cno: string;
  enterpriseCode: string;
  imAccid: string;
  imToken: string;
  isLogin: number;
  userName: string;
  doctorRole: string;
  seatsPassword: string;
  token: string;
  type: number;
  userId: string;
  userRoles: string[];
}
export interface LoginParams {
  password: string;
  loginAccount: string;
  type: number;
  verifyCode?: string;
}

export function login(params: LoginParams) {
  return http.post<LoginResultModel>({
    url: '/api/health/user/login',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}
// 获取系统登录类型
export function getLoginType() {
  return http.post({
    url: '/api/health/user/login/type',
    method: 'post',
    data: { dictCode: 'login_type' },
  });
}

// 获取短信验证码
export function getCode(params: any) {
  return http.post({
    url: '/api/health/user/send',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取短信验证码
export function verifyMsgCode(params: any) {
  return http.post({
    url: '/api/health/verify/code',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 忘记密码
export function updatePassword(params: any) {
  return http.post({
    url: '/api/health/update/password',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 修改密码
export function editPassword(params: any) {
  return http.post({
    url: '/api/health/update/pwd',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询医生是否备案
export function queryPhysicianRegister() {
  return http.post({
    url: '/api/physician/whether/register',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 添加或修改医师备案信息
export function addPhysician(params: any) {
  return http.post({
    url: '/api/physician/modify',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询互联网医院
export function queryInternetHospital() {
  return http.post({
    url: '/api/physician/query/internet/hospital',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 查询医生备案详情
export function queryPhysicianDetails() {
  return http.post({
    url: '/api/physician/query/info',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 查询是否绑定公众号
export function queryCheckBindApi() {
  return http.post({
    url: '/api/health/check/bind',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 解绑微信
export function unbindWechatApi() {
  return http.post({
    url: '/api/health/unbind/wechat',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 获取医生注册二维码
export function getQrCodeApi() {
  return http.post({
    url: '/api/health/qrCode/register',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者工作室变更信息
export function getPatientGroupChangeApi(data: { businessId: any }) {
  return http.post({
    url: '/api/patient/group/change/record',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}
