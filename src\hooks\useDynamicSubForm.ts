import { ref, watch } from 'vue';
import { FormCategory } from '@/constant';
import { useFormJSONSchema } from '@/hooks/useFormJSONSchema';

/**
 * 根据Key获取动态表单Schema
 * @param key 表单Key
 */
export function useDynamicSubForm(key: string) {
  const jsonSchema = ref({});
  const schema = ref({});
  const { data, isSuccess } = useFormJSONSchema();
  watch(
    data,
    (newVal, oldVal) => {
      if (isSuccess.value && !oldVal) {
        const finalData: Record<string, any> = {};
        (newVal || [])?.forEach(item => {
          // 暂时根据key对组合的情况做特殊判断
          if (item.key !== FormCategory.DIAGNOSE_REPORT) {
            finalData[item.key] = item;
          } else {
            item.items.forEach((item: any) => {
              finalData[item.key] = item;
            });
          }
        });
        jsonSchema.value = finalData;
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );

  watch(
    [() => key, jsonSchema],
    ([newKey, newJsonSchema]) => {
      schema.value = newJsonSchema?.[newKey] || {};
    },
    { immediate: true }
  );

  return { schema, allSchema: jsonSchema };
}
