<template>
  <div class="wrapper">
    <div
      v-for="item in data"
      :key="item.date"
      class="item"
      :class="{ active: current === item.date }"
      :style="{ height }"
      @click="() => clickItem(item)"
    >
      <div class="content">
        <slot name="content" :data="item.allData">
          <div
            v-for="val in getCurTypeList(item.allData)"
            :key="val.type"
            class="count-card"
          >
            <img :src="typesMap[val.type ?? 4]?.icon" />
            <span>{{ val.count }}</span>
          </div>
        </slot>
      </div>
      <div class="text">
        {{ item.label }}
      </div>
      <div v-if="current === item.date" class="triangle"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import hospital from '@/assets/imgs/review/hospital.png';
import outpatient from '@/assets/imgs/review/outpatient.png';
import review from '@/assets/imgs/review/review.png';
import enroment from '@/assets/imgs/todo/patient-enrollment-img.png';
import followup from '@/assets/imgs/review/followup.png';
import { cloneDeep, groupBy, isEmpty } from 'lodash-es';
interface IProps {
  data: {
    label: string;
    date: string;
    allData: any[];
  }[];
  types: number[];
  current: string;
  height: string;
}

const typesMap = {
  0: { icon: hospital },
  1: { icon: outpatient },
  2: { icon: enroment },
  3: { icon: review },
  undefined: { icon: followup },
};

defineProps<IProps>();
const emits = defineEmits(['clickItem']);
const clickItem = val => {
  emits('clickItem', val);
};

const getCurTypeList = (data: any) => {
  const validData = data.filter(v => !isEmpty(v.data)).map(v => v.data);
  const cloneData = cloneDeep(validData);
  cloneData.forEach(v => {
    if (v.type === 2) {
      // 特殊处理，入组归类到住院
      v.type = 0;
    }
  });
  const groupObj = groupBy(cloneData, 'type');
  const keys = Object.keys(groupObj).sort((a, b) => Number(b) - Number(a));
  const result = keys.map(v => ({ type: v, count: groupObj[v].length }));
  return result;
};

defineOptions({
  name: 'Toolbar',
});
</script>

<style scoped lang="less">
.wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  .item {
    display: flex;
    position: relative;
    flex-direction: column;
    height: 100px;
    width: 42px;
    background: #f7f8fa;
    border: 1px solid #f7f8fa;
    cursor: pointer;
    border-radius: 2px;
    &.active {
      border: 1px solid #2e6be6;
      .text {
        background: #2e6be6;
        color: #fff;
      }
    }
  }
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 0 0 6px;
  }
  .count-card {
    display: flex;
    height: 14px;
    font-size: 14px;
    margin-top: 10px;
    align-items: center;
    color: #3a4762;
    > img {
      width: 12px;
      height: 12px;
      margin-right: 3px;
    }
  }
  .text {
    height: 24px;
    line-height: 24px;
    border-radius: 1px;
    color: #3a4762;
    background: #e8eaed;
    text-align: center;
    font-size: 14px;
  }
}
.triangle {
  position: absolute;
  z-index: 10;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
  bottom: -21px;
  left: 14px;
  border: 1px solid #dcdfe6;
  background: #f7f8fa;
  border-right-color: transparent;
  border-bottom-color: transparent;
}
</style>
