<template>
  <div>
    <FileHead
      v-if="!locatedPatientHistoryId"
      @get-head-params="getHeadParams"
    />
    <SingleDynamicForm
      ref="DynamicFormRef"
      v-model="dynamicFormData"
      class="mt-lg"
      mode="create"
      :category="category"
      hidden-file-upload
      :extra-data="dynamicFormExtraData"
    />
  </div>
</template>
<script setup lang="ts">
import FileHead from './FileHead.vue';
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';
import { useOcrScan } from '@/store/module/useOcrScan';
import globalBus from '@/lib/bus';
import { isArray } from 'lodash-es';
import { handleFormatDiagnoseReportData } from '@/features/OcrViewer/utils';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';

defineOptions({ name: 'Electrocardiogram' });

const { category = '' } = defineProps<{
  /** 动态心电图: ecg_dynamic； 心脏彩超: echocardiogram； 12导联心电图: ecg_12 */
  category: 'ecg_dynamic' | 'echocardiogram' | 'ecg_12';
}>();

const ocrStore = useOcrScan();
const saveOcrStore = useSaveOcrInfo();

const DynamicFormRef = shallowRef();
const sourceParams = ref<any>({});

const locatedPatientHistoryId = inject('locatedPatientHistoryId');

const componentLocation = inject('componentLocation');

const locatedCaseType = computed(() => {
  if (componentLocation === 'REVIEW_RECORDS') {
    return 2;
  } else if (componentLocation === 'OUTPATIENT_RECORDS') {
    return 1;
  } else if (componentLocation === 'HOSPITALIZATION_RECORDS') {
    return 0;
  } else {
    return null;
  }
});

const getHeadParams = (data: any) => {
  sourceParams.value = data;
};

const baseInfoMap = {
  ecg_dynamic: {
    reportName: '动态心电图',
    indexTermId: 61,
  },
  echocardiogram: {
    reportName: '心脏彩超',
    indexTermId: 67,
  },
  ecg_12: {
    reportName: '12导联心电图',
    indexTermId: 60,
  },
};
const dynamicFormData = ref<any>();
const dynamicFormExtraData = computed(() => {
  return {
    ...baseInfoMap[category],
    ...saveOcrStore.getExtraParams(),
    source_type: locatedPatientHistoryId
      ? locatedCaseType.value
      : sourceParams.value.sourceType,
    source_id: locatedPatientHistoryId || sourceParams.value.sourceId,
    accessory: [ocrStore.globalImgInfo.currentImgUrl.url],
  };
});

const saveData = async () => {
  if (!sourceParams.value.sourceId && !locatedPatientHistoryId) {
    ElMessage.error('请选择记录！');
    return;
  }
  try {
    const reqHttp = await DynamicFormRef.value?.submit();
    if (reqHttp === undefined) return;
    globalBus.emit('close-ocr-dialog');
    globalBus.emit('saved-ocr-info');
    if (locatedPatientHistoryId || sourceParams.value.sourceId) {
      const id = locatedPatientHistoryId
        ? locatedPatientHistoryId
        : sourceParams.value.sourceId;

      globalBus.emit('refresh-attachment', id);
      globalBus.emit('refresh-record-data', id);
    }
  } catch {}
};

const setFormData = (outData = null) => {
  const data =
    isArray(ocrStore.aiResultData) &&
    handleFormatDiagnoseReportData(ocrStore.aiResultData);
  dynamicFormData.value = outData ? outData : data?.[0];
};
watch(
  () => ocrStore.aiResultData,
  () => {
    setFormData();
  },
  { deep: true }
);
onMounted(() => {
  setFormData();
});
defineExpose({
  saveData,
  setFormData,
});
</script>
