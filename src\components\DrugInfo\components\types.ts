export interface DrugAmount {
  /** 单次用量含量 */
  ingredients: string;
  /** 单次用量单位 */
  contentUnit: string;
}

export interface DrugSpec {
  /** 成分含量 */
  ingredients: string;
  /** 含量单位 */
  contentUnit: string;
  /** 制剂数量 */
  packageNum: string;
  /** 制剂单位 */
  unit: string;
  /** 包装单位 */
  packageUnit: string;
}

export interface Drug {
  /** 药品类型 */
  type: number;
  /** 药品通用名称 */
  commonName: string;
  /** 药品名称 */
  drugName: string;
  /** 药品用量信息 */
  drugAmount: DrugAmount;
  /** 药品规格信息 */
  drugSpec: DrugSpec;
  /** 服药方法 */
  drugMode: string;
  /** 服药频率 */
  drugUsage: string;
  /** 服药时间（值来自medicineTimeList的value字段） */
  medicineTime: string;
}

export type PickPartial<T, K extends keyof T> = Partial<Pick<T, K>> & Omit<T, K>;