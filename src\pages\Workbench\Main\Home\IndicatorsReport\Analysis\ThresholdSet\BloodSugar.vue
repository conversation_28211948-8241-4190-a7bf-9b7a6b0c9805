<template>
  <div class="blood-sugar">
    <div class="pb-lg">
      <div class="pb-xs">
        <i class="text-danger">*</i>
        <span class="font-medium">系统监测并提醒患者测量7天血糖</span>
        <span>（近7天至少完成1天血糖测量）</span>
      </div>

      <div class="pl-2xl">
        <el-radio-group v-model="configData.status">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="blood-sugar-forewarning">
      <div class="py-2xs text-danger">1级预警</div>
      <div class="notes-text">患者血糖低于超低值</div>
      <div class="flex items-center pt-3xs pl-2xl pb-md text-sm">
        <div class="w-72">超低值</div>
        <div class="flex-c">
          <div class="name name-layout">空腹/随机/餐后2小时血糖（mmol/L）</div>
          <InputNumber
            v-model="configData.lev1Min.value"
            :min="0.1"
            :max="configData.lev1Min.defaultVal"
            :placeholder="`默认：${configData.lev1Min.defaultVal}`"
          />
        </div>
      </div>

      <div class="flex items-baseline pt-3xs pl-2xl pb-md text-sm">
        <div class="w-72">偏高值</div>
        <div>
          <div
            v-for="item in configData.lev2List"
            :key="item.type"
            class="flex-c mt-xs"
          >
            <div class="name name-layout">{{ item.name }}</div>
            <InputNumber
              v-model="item.value"
              :min="0.1"
              :max="
                configData.lev1Max.value
                  ? formatFloat(configData.lev1Max.value - 0.1)
                  : 999
              "
              :placeholder="`默认：${item.defaultVal}`"
            />
          </div>
        </div>
      </div>

      <div class="notes-text">患者血糖高于超高值</div>
      <div class="flex items-center pt-3xs pl-2xl pb-md text-sm">
        <div class="w-72">超高值</div>
        <div class="flex-c">
          <div class="name name-layout">空腹/随机/餐后2小时血糖（mmol/L）</div>
          <InputNumber
            v-model="configData.lev1Max.value"
            :min="configData.lev1Min.defaultVal"
            :placeholder="`默认：${configData.lev1Max.defaultVal}`"
            @blur="onInputBlur"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InputNumber from '@/components/InputNumber/index.vue';
import { PropType } from 'vue';
import { formatFloat } from '@/utils';
import { IBloodSugarData } from '../hooks/useThresholdSet';
import { isNumber } from 'lodash-es';

const props = defineProps({
  configData: {
    type: Object as PropType<IBloodSugarData>,
    default: () => ({
      lev1Min: {
        value: undefined,
        defaultVal: 3.9,
      },
      lev1Max: {
        value: undefined,
        defaultVal: 33.3,
      },
    }),
  },
});

function onInputBlur(event: FocusEvent) {
  const val = +(event.target as HTMLInputElement).value;
  const lev2List = props.configData?.lev2List;
  lev2List.forEach(item => {
    if (isNumber(item.value) && item.value >= val) {
      item.value = undefined;
    }
  });
}
</script>
<style scoped lang="less">
.blood-sugar {
  padding: 24px 100px 0;

  .notes-text {
    color: #8193a3;
  }

  &-forewarning {
    .flex {
      padding-bottom: var(--spacing-2xs);

      .name {
        text-align: right;
        color: #203549;
      }

      .name-layout {
        width: 244px;
      }
    }
  }
}
</style>
