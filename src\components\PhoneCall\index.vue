<template>
  <div
    v-if="useCallStore.isCallLogin !== 1 || disabledConfirm"
    class="call-container"
    @click.stop="clickHandler"
  >
    <slot>
      <img :src="callCenter" />
    </slot>
  </div>
  <div v-else @click.stop>
    <Popper
      title="确认拨号"
      placement="top-end"
      :width="420"
      :confirm="() => callback()"
    >
      <template #reference>
        <div class="call-container">
          <slot>
            <img :src="callCenter" />
          </slot>
        </div>
      </template>
      <div>是否确认拨打以下号码, {{ name }}:{{ tel ?? '--' }}?</div>
    </Popper>
  </div>
</template>

<script setup lang="ts">
import callCenter from '@/assets/imgs/callCenter/call-center.png';
import useCall from '@/store/module/useCallCenter';
import Popper from '@/components/Popper/index.vue';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();
const useCallStore = useCall();
import { handleRecordApi } from '@/api/addressBook';
import useUserStore from '@/store/module/useUserStore';

interface IProps {
  disabledConfirm?: boolean;
  name?: string;
  tel?: string | number;
  needClear?: boolean;
}
const props = defineProps<IProps>();

defineOptions({
  name: 'PhoneCall',
});

const userStore = useUserStore();

const clickHandler = () => {
  if (useCallStore.isCallLogin === 1) {
    callback();
  } else {
    let obj = {
      showClose: true,
      message: '请先登录呼叫中心',
      type: 'warning',
    };
    let newObj = obj as any;
    ElMessage(newObj);
  }
};
const callback = () => {
  if (!props.tel) {
    ElMessage.warning(`${props.name}电话号码为空`);
    return;
  }
  (window as any).ClinkAgent.previewOutcall({ tel: props.tel });
  if (props.needClear) {
    useCallStore.tempCallPhone = props.tel as string;
  }
  handleRecord();
};

let handleRecord = () => {
  // 用户角色  医生：ASSISTANT, 健康管理师：CUSTOMER_SERVER, 运动康复师：REHAB, 实习生：INTERN,
  const userRoles = userStore.userRoles;
  let currentRole = userRoles?.[0];
  if (currentRole !== 'INTERN') {
    let userType =
      currentRole === 'ASSISTANT'
        ? 5
        : currentRole === 'CUSTOMER_SERVER'
          ? 3
          : 9;
    let params = {
      patientId: useGlobalInfo.userId,
      userId: userStore.accountId,
      userType,
      recordTime: timestampToDate(new Date()),
    };
    handleRecordApi(params);
  }
};

// 将时间戳转为年月日
let timestampToDate = (timestamp: string | number | Date) => {
  if (!timestamp) return '--';

  const date = new Date(timestamp); // JavaScript Date对象需要的毫秒级时间戳
  const year = date.getFullYear();
  let month: any = date.getMonth() + 1; // getMonth()返回的月份从0开始，所以要+1
  let day: any = date.getDate();

  // 如果月份或日期小于10，前面补0
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;

  const dateStr = year + month + day;
  return dateStr;
};
</script>

<style scoped lang="less">
.call-container {
  display: inline-block;
  cursor: pointer;
  > img {
    width: 16px;
    height: 16px;
  }
}
</style>
