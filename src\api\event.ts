import { http } from '@/network';

/** 临床事件列表 */
export function getEventList(params: any) {
  return http.post({
    url: 'api/patient/info/list/event',
    method: 'post',
    data: params,
  });
}
/** 删除临床事件 */
export function deleteEvent(params: any) {
  return http.post({
    url: '/api/patient/info/delete/event',
    method: 'post',
    data: params,
  });
}

export function saveEvent(params: any) {
  return http.post({
    url: '/api/patient/info/preserve/event',
    method: 'post',
    data: params,
  });
}
