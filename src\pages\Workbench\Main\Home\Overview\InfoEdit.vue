<template>
  <div class="info-edit">
    <div class="base-info">
      <div class="til">基本信息</div>
      <div class="info-box">
        <div class="info-item">
          <div class="label">加入日期：</div>
          <div class="value">
            {{
              patientData.joinDate
                ? dayjs(patientData.joinDate).format('YYYY-MM-DD')
                : '--'
            }}
          </div>
        </div>
        <div class="info-item">
          <div class="label">民族：</div>
          <div class="value">{{ patientData.nation || '--' }}</div>
        </div>
        <div class="info-item line">
          <div class="label">身份证号：</div>
          <div class="value">{{ patientData.cardNo || '--' }}</div>
        </div>
        <div class="info-item line">
          <div class="label">家庭地址：</div>
          <div class="value flex">
            <SurgeryToolTip>
              <div class="tip-content text-ellipsis overflow-hidden">
                {{ regionValue.join('') }}{{ detailsAddress }}
              </div>
            </SurgeryToolTip>
            <el-button class="w-50" link @click="visibleAddress = true">
              <img
                class="w-11 h-13 ml-10 mr-5"
                src="@/assets/imgs/overview/icon-edit.png"
                alt=""
              />
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="base-info">
      <div class="til">亲情账号</div>
      <div class="info-box">
        <div v-for="(item, i) in familyList" :key="i" class="info-item line">
          <div class="label">账号一：</div>
          <div class="value">
            {{ item.name }}（{{ item.relation }}）{{ item.phone }}
          </div>
        </div>
        <div
          v-if="!familyList || familyList.length === 0"
          class="info-item line"
        >
          <div class="label">账号一：</div>
          <div class="value">--</div>
        </div>
      </div>
    </div>
    <div class="base-info">
      <div class="til flex justify-between">
        <span>绑定硬件</span>
        <span style="color: #2e6be6" class="cursor-pointer" @click="bindRecord">
          绑定记录
        </span>
      </div>
      <div>
        <div
          class="equipment-type flex-bc"
          :class="{ 'no-bind': !equipmentBlood }"
        >
          <div class="type flex-ac">
            <img
              class="w-16 h-16 mr-8"
              src="@/assets/imgs/overview/icon-blood.png"
              alt=""
            />
            血压计
            <span>（{{ equipmentBlood ? '已绑定' : '未绑定' }}）</span>
          </div>
        </div>
        <div class="info-box">
          <div class="info-item">
            <div class="label">
              设备编码({{
                `${equipmentBlood?.soNo}`.startsWith('86978406')
                  ? 'IMEI'
                  : 'SN'
              }})：
            </div>
            <div class="value">{{ equipmentBlood?.soNo || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="label">设备型号：</div>
            <div class="value">
              {{
                equipmentBlood
                  ? equipmentInfoShow(equipmentBlood.type, unitTypeList)
                  : '--'
              }}
            </div>
          </div>
          <div class="info-item info-item-time">
            <div class="label">绑定日期：</div>
            <div class="value">
              {{
                equipmentBlood?.bindTime
                  ? dayjs(equipmentBlood.bindTime).format('YYYY-MM-DD')
                  : '--'
              }}
            </div>
          </div>
          <div class="about-equipment flex items-center justify-between">
            <div class="left-msg flex items-center">
              <div
                v-if="equipmentBlood"
                class="equipment-recall cursor-pointer mr-4"
                @click="recallEquipment(1)"
              >
                设备召回
              </div>
              <div
                v-if="equipmentBlood && equipmentBlood?.purchase"
                class="push-buy-link cursor-pointer ml-12"
                @click="
                  purchaseEquipment(
                    equipmentBlood.type === 5 ? 'HP' : 'BPG',
                    600,
                    equipmentBlood
                  )
                "
              >
                推送购买链接
              </div>
              <div
                v-if="equipmentBlood && !equipmentBlood?.purchase"
                class="push-buy-link ml-12"
              >
                已购买
              </div>
              <el-tooltip v-if="equipmentBlood" placement="top">
                <template #content>
                  患者操作路径：
                  <br />
                  公众号收到链接->点击进入->确认设备信息->立即购买
                </template>
                <img
                  src="@/assets/imgs/overview/question-circle-outlined.png"
                  alt=""
                  class="w-16 h-16 ml-4 cursor-pointer mr-16"
                />
              </el-tooltip>
            </div>
            <div class="right-btn">
              <div v-if="equipmentBlood" class="operation">
                <el-button
                  link
                  @click="
                    ((deleteDeviceInfo = equipmentBlood),
                    (untieDialogVisible = true))
                  "
                >
                  解绑
                </el-button>
                <el-button link @click="editDevice(equipmentBlood)">
                  更换
                </el-button>
              </div>
              <div v-else class="operation">
                <el-button link @click="editDevice(null, 1)">绑定</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          class="equipment-type flex-bc"
          :class="{ 'no-bind': !equipmentWatch }"
        >
          <div class="type flex-ac">
            <img
              class="w-16 h-16 mr-8"
              src="@/assets/imgs/overview/icon-watch.png"
              alt=""
            />
            手表
            <span>（{{ equipmentWatch ? '已绑定' : '未绑定' }}）</span>
          </div>
        </div>
        <div class="info-box">
          <div class="info-item">
            <div class="label">设备编号(IMEI)：</div>
            <div class="value">
              <!--              {{ equipmentWatch?.soNo || '&#45;&#45;' }}-->
              <SurgeryToolTip>
                <div class="tip-content text-ellipsis overflow-hidden">
                  {{ equipmentWatch?.soNo || '--' }}
                </div>
              </SurgeryToolTip>
            </div>
          </div>
          <div class="info-item">
            <div class="label">设备型号：</div>
            <div class="value">
              {{
                equipmentWatch
                  ? equipmentInfoShow(equipmentWatch.type, unitTypeList)
                  : '--'
              }}
            </div>
          </div>
          <div class="info-item info-item-time">
            <div class="label">绑定日期：</div>
            <div class="value">
              {{
                equipmentWatch?.bindTime
                  ? dayjs(equipmentWatch.bindTime).format('YYYY-MM-DD')
                  : '--'
              }}
            </div>
          </div>
          <div class="about-equipment flex items-center justify-between">
            <div class="left-msg flex items-center">
              <div
                v-if="equipmentWatch"
                class="equipment-recall cursor-pointer mr-4"
                @click="recallEquipment(2)"
              >
                设备召回
              </div>
              <div
                v-if="equipmentWatch && equipmentWatch?.purchase"
                class="push-buy-link cursor-pointer ml-12"
                @click="purchaseEquipment('WATCH', 1200, equipmentWatch)"
              >
                推送购买链接
              </div>
              <div
                v-if="equipmentWatch && !equipmentWatch?.purchase"
                class="push-buy-link ml-12"
              >
                已购买
              </div>
              <el-tooltip v-if="equipmentWatch" placement="top">
                <template #content>
                  患者操作路径：
                  <br />
                  公众号收到链接->点击进入->确认设备信息->立即购买
                </template>
                <img
                  src="@/assets/imgs/overview/question-circle-outlined.png"
                  alt=""
                  class="w-16 h-16 ml-4 cursor-pointer mr-16"
                />
              </el-tooltip>
            </div>
            <div class="right-btn">
              <div v-if="equipmentWatch" class="operation">
                <el-button
                  link
                  @click="
                    ((deleteDeviceInfo = equipmentWatch),
                    (untieDialogVisible = true))
                  "
                >
                  解绑
                </el-button>
                <el-button link @click="editDevice(equipmentWatch)">
                  更换
                </el-button>
              </div>
              <div v-else class="operation">
                <el-button link @click="editDevice(null, 3)">绑定</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          class="equipment-type flex-bc"
          :class="{ 'no-bind': !equipmentWS }"
        >
          <div class="type flex-ac">
            <img
              class="w-16 h-16 mr-8"
              src="@/assets/imgs/overview/icon-ws.png"
              alt=""
            />
            体重秤
            <span>（{{ equipmentWS ? '已绑定' : '未绑定' }}）</span>
          </div>
        </div>
        <div class="info-box">
          <div class="info-item">
            <div class="label">设备编号(MAC)：</div>
            <div class="value">
              <SurgeryToolTip>
                <div class="tip-content text-ellipsis overflow-hidden">
                  {{ equipmentWS?.soNo || '--' }}
                </div>
              </SurgeryToolTip>
            </div>
          </div>
          <div class="info-item">
            <div class="label">设备型号：</div>
            <div class="value">
              {{
                equipmentWS
                  ? equipmentInfoShow(equipmentWS.type, unitTypeList)
                  : '--'
              }}
            </div>
          </div>
          <div class="info-item info-item-time">
            <div class="label">绑定日期：</div>
            <div class="value">
              {{
                equipmentWS?.bindTime
                  ? dayjs(equipmentWS.bindTime).format('YYYY-MM-DD')
                  : '--'
              }}
            </div>
          </div>
          <div class="about-equipment flex items-center justify-between">
            <div class="left-msg flex items-center">
              <div
                v-if="equipmentWS"
                class="equipment-recall cursor-pointer mr-4"
                @click="recallEquipment(3)"
              >
                设备召回
              </div>
              <div
                v-if="equipmentWS && equipmentWS?.purchase"
                class="push-buy-link cursor-pointer ml-12"
                @click="purchaseEquipment('WS', 100, equipmentWS)"
              >
                推送购买链接
              </div>
              <div
                v-if="equipmentWS && !equipmentWS?.purchase"
                class="push-buy-link ml-12"
              >
                已购买
              </div>
              <el-tooltip v-if="equipmentWS" placement="top">
                <template #content>
                  患者操作路径：
                  <br />
                  公众号收到链接->点击进入->确认设备信息->立即购买
                </template>
                <img
                  src="@/assets/imgs/overview/question-circle-outlined.png"
                  alt=""
                  class="w-16 h-16 ml-4 cursor-pointer mr-16"
                />
              </el-tooltip>
            </div>
            <div class="right-btn">
              <div v-if="equipmentWS" class="operation">
                <el-button
                  link
                  @click="
                    ((deleteDeviceInfo = equipmentWS),
                    (untieDialogVisible = true))
                  "
                >
                  解绑
                </el-button>
                <el-button link @click="editDevice(equipmentWS)">
                  更换
                </el-button>
              </div>
              <div v-else class="operation">
                <el-button link @click="editDevice(null, 4)">绑定</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <HrtDialog
    v-model="visible"
    :width="600"
    :title="equipmentInfo.operation == 1 ? '添加智能设备' : '更换智能设备'"
  >
    <SmartDevices
      ref="refSmartDevices"
      v-model:equipment-info="equipmentInfo"
    />
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="visible = false">取消</div>
        <div class="sure-btn" @click="sureBtnSmartDevices">确定</div>
      </div>
    </template>
  </HrtDialog>
  <HrtDialog
    v-model="visibleAddress"
    :width="600"
    title="家庭地址"
    class="address-box"
  >
    <AddressEdit
      ref="refAddressEdit"
      v-model:region-data="regionValue"
      v-model:details="detailsAddress"
    />
    <template #footer>
      <!-- 这里是末尾的元素插槽，比如提交取消按钮-->
      <div class="btn-box">
        <div class="cancel-btn" @click="visibleAddress = false">取消</div>
        <div class="sure-btn" @click="sureBtnVisible">确定</div>
      </div>
    </template>
  </HrtDialog>
  <!--  血压计解绑弹窗  -->
  <HrtDialog
    v-model="untieDialogVisible"
    title=""
    :width="360"
    class="untie-dialog"
  >
    <div class="tips">
      <img src="@/assets/imgs/overview/icon-del.png" alt="" class="untieImg" />
      确定解除该智能设备的绑定？
    </div>
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="untieDialogVisible = false">取消</div>
        <div class="sure-btn" @click="deleteDevice">确定</div>
      </div>
    </template>
  </HrtDialog>
  <!-- 设备召回 -->
  <HrtDialog v-model="recallVisible" width="320" class="recall-visible">
    <template #header>
      <div class="my-header flex items-center">
        <img
          src="@/assets/imgs/overview/recall-icon.png"
          alt=""
          class="w-20 w-20 cursor-pointer mr-8"
        />
        <div class="title">设备召回</div>
      </div>
    </template>
    <span class="content ml-28">确定给患者推送设备邮寄地址吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="recallVisible = false">取消</el-button>
        <el-button type="primary" @click="recallsure">确定</el-button>
      </div>
    </template>
  </HrtDialog>

  <!-- 绑定记录 -->
  <BindRecord :drawer="drawer" @close-drawer="drawer = false" />

  <!-- 购买设备 -->
  <HrtDialog
    v-model="visiblePurchaseEquipment"
    :width="428"
    title="设备购买"
    class="address-box"
  >
    <div class="ml-24 mt-20">
      设备类型：
      <span>
        {{
          purchaseEquipmentType === 'HP'
            ? '掌护血压计'
            : purchaseEquipmentType == 'BPG'
              ? '台式血压计'
              : purchaseEquipmentType == 'WATCH'
                ? '智能手表'
                : '体重秤'
        }}
      </span>
    </div>
    <div class="ml-24 mt-12">价格：{{ purchaseEquipmentPrice }}元</div>
    <div class="ml-24 mt-12 text-xs">患者操作路径：</div>
    <div class="mx-24 mt-12 text-xs w-fit">
      购买在用设备：公众号收到链接->点击进入->确认设备信息->立即购买
    </div>
    <template #footer>
      <!-- 这里是末尾的元素插槽，比如提交取消按钮-->
      <div class="btn-box purchase-btn">
        <div class="cancel-btn" @click="visiblePurchaseEquipment = false">
          取消
        </div>
        <div class="sure-btn" @click="pushLink">推送</div>
      </div>
    </template>
  </HrtDialog>
</template>

<script setup lang="ts">
import SmartDevices from './SmartDevices.vue';
import AddressEdit from './AddressEdit.vue';
import BindRecord from './BindRecord.vue';
import bus from './hooks/bus';
import dayjs from 'dayjs';
import { HrtDialog } from '@hrt/components';

import {
  updatePatientAddress,
  getPatientDeviceList,
  updatePatientDevice,
  deletePatientDevice,
  pushAddressApi,
  createOrderApi,
} from '@/api/overview';
import SurgeryToolTip from './SurgeryToolTip.vue';
import { debounce } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';

// 购买设备
let visiblePurchaseEquipment = ref(false);
let purchaseEquipmentType = ref();
let purchaseEquipmentPrice = ref();
let priceInfo = ref();
let purchaseEquipment = (type: any, price: any, item: { purchase: any }) => {
  if (!item.purchase) {
    ElMessage.warning('当前设备不可购买！');
  } else {
    priceInfo.value = { ...item, type };
    purchaseEquipmentType.value = type;
    purchaseEquipmentPrice.value = price;
    visiblePurchaseEquipment.value = true;
  }
};
let pushLink = () => {
  createOrder();
};
let createOrder = async () => {
  let obj = {
    userId: global.userId,
    patientId: global.userId,
    wxPayType: 'WX_JSAPI',
    orderType: 'HARDWARE',
    productId: priceInfo.value.productId,
    creatorType: 'DOCTOR',
    orderDeviceRemark: {
      deviceType: priceInfo.value.type,
      deviceSoNo: priceInfo.value.soNo,
    },
    creatorId: localStorage.getItem('accountId'),
  };
  createOrderApi(obj).then((res: any) => {
    let { code } = res;
    if (code == 'E000000') {
      ElMessage.success('推送成功！');
      visiblePurchaseEquipment.value = false;
    } else if (code == 'E080305') {
      ElMessage.error('产品状态异常！');
    } else if (code == 'E080306') {
      ElMessage.error('患者未绑定工作室！');
    } else if (code == 'E080307') {
      ElMessage.error('重复发起订单');
    } else if (code == 'E080309') {
      ElMessage.error('患者未关注公众号！');
    } else if (code == 'E080312' || code == 'E080313') {
      ElMessage.error('设备状态异常！');
    } else {
      ElMessage.error('订单创建失败！');
    }
  });
};

// 绑定记录
let drawer = ref(false);
const bindRecord = () => {
  drawer.value = true;
};

// 设备召回
let recallVisible = ref(false);
let equipmentType = ref(0);
const recallEquipment = (type: number) => {
  equipmentType.value = type;
  recallVisible.value = true;
};
let recallsure = () => {
  pushAddressApi({ businessId: global.userId })
    .then(() => {
      ElMessage.success('推送成功!');
      recallVisible.value = false;
    })
    .catch(() => {
      ElMessage.error('推送失败!');
    });
};

interface IProps {
  patientData?: any;
}
interface IEquipmentInfo {
  type: number | string; //设备类型(血压计=1，手表=3)，默认为血压计
  model: number | string; //设备型号（爱奥乐=1，脉搏波=2，手表=3，掌护=5）
  equipmentSN: number | string; //设备编号
  operation: number | string; // 设备是更换还是新增，默认新增
}
const props = defineProps<IProps>();

const regionValue = ref([
  props.patientData.province || '',
  props.patientData.city || '',
  props.patientData.county || '',
]);
const detailsAddress = ref(props.patientData.detailAddress);
const visible = ref(false);
const visibleAddress = ref(false);
const untieDialogVisible = ref(false);
const refAddressEdit = ref();
const refSmartDevices = ref();
const deleteDeviceInfo = ref<any>({});
const equipmentInfo = ref<IEquipmentInfo>({
  //当前设备编辑弹窗的数据（添加/更换时赋值）
  type: 1, //设备类型(血压计=1，手表=3)，默认为血压计
  model: '', //设备型号（爱奥乐=1，脉搏波=2，ZK204(手表)=3）
  equipmentSN: '', //设备编号
  operation: 1, //1添加，2更改
});
const unitTypeList = [
  //设备型号
  {
    value: 1,
    label: '爱奥乐',
    type: 1, //此type用于区分血压计/手表，与智能设备类型选项value对应，方便根据当前选择的设备类型过滤设备型号的选项数据
  },
  {
    value: 2,
    label: '脉搏波',
    type: 1,
  },
  {
    value: 3,
    label: 'ZK204',
    type: 3,
  },
  {
    value: 4,
    label: 'CF516BLE',
    type: 4,
  },
  {
    value: 5,
    label: '掌护',
    type: 1,
  },
];
const equipmentList = ref<any>([]);
const familyList = ref(props.patientData.familyList);
const global = useGlobal();
const sureBtnVisible = () => {
  let regionData = refAddressEdit.value.onClose().regionData;
  let details = refAddressEdit.value.onClose().details;
  if (!regionData.length) {
    ElMessage.error('请选择地区!');
    return;
  } else if (!details) {
    ElMessage.error('请输入详细地址!');
    return;
  }

  let params = {
    patientId: global.userId,
    province: regionData[0],
    city: regionData[1],
    county: regionData[2],
    address: details,
  };
  updatePatientAddress(params)
    .then(() => {
      ElMessage.success('保存成功!');
      visibleAddress.value = false;
      //修改地址成功后， 刷新患者信息
      bus.emit('refresh-patient-infos');
    })
    .catch(() => {
      ElMessage.error('保存失败!');
    });
};
const sureBtnSmartDevices = debounce(() => {
  let data = refSmartDevices.value.onClose();
  let params = {
    patientId: global.userId,
    soNo: data.equipmentSN,
    type: data.model,
  };
  if (!params.type) return ElMessage.warning('设备类型不能为空！');
  if (!params.soNo) return ElMessage.warning('设备编号不能为空！');
  if (params.type == 5 && !params.soNo.startsWith('86978406'))
    return ElMessage.warning('该设备编号错误或类型错误');

  updatePatientDevice(params)
    .then(res => {
      if (res.code === 'E000000') {
        ElMessage.success('保存成功!');
        getPatientDeviceLists();
        visible.value = false;
      }
    })
    .catch(() => {
      // ElMessage.error(`保存失败：${err.message}`);
    });
}, 800);

const deleteDevice = () => {
  let params = {
    patientId: global.userId,
    soNo: deleteDeviceInfo.value.soNo,
    simNo: deleteDeviceInfo.value.simNo,
    type: deleteDeviceInfo.value.type,
  };
  deletePatientDevice(params)
    .then(() => {
      ElMessage.success('解绑成功!');
      getPatientDeviceLists();
      untieDialogVisible.value = false;
    })
    .catch(() => {
      ElMessage.error('解绑失败!');
    });
};
const editDevice = (info: { type: number; soNo: any } | null, type?: any) => {
  // type: '1', //设备类型(血压计=1，手表=3)，默认为血压计
  // model: '', //设备型号（爱奥乐=1，脉搏波=2，手表=3）
  // equipmentSN: '', //设备编号
  // operation: '1', // 设备是更换还是新增，默认新增
  if (info) {
    equipmentInfo.value = {
      type: info.type == 3 ? 3 : info.type == 4 ? 4 : 1,
      model: info.type,
      equipmentSN: info.soNo,
      operation: 2,
    };
  } else {
    equipmentInfo.value = {
      type: type,
      model: '',
      equipmentSN: '',
      operation: 1,
    };
  }
  visible.value = true;
};

// 绑定设备数据展示
const equipmentInfoShow = (value: any, arr: any[]) => {
  let info = arr.find((re: { value: any }) => re.value == value);
  return info ? info.label : '--';
};
const equipmentBlood = computed(() => {
  return equipmentList.value.find(
    (re: { type: number }) => re.type != 3 && re.type != 4
  );
});
const equipmentWatch = computed(() => {
  return equipmentList.value.find((re: { type: number }) => re.type == 3);
});
const equipmentWS = computed(() => {
  return equipmentList.value.find((re: { type: number }) => re.type == 4);
});
const getPatientDeviceLists = () => {
  getPatientDeviceList({ patientId: global.userId }).then((res: any) => {
    equipmentList.value = res.deviceList;
  });
};
watch(
  () => props.patientData,
  () => {
    regionValue.value = [
      props.patientData.province || '',
      props.patientData.city || '',
      props.patientData.county || '',
    ];
    detailsAddress.value = props.patientData.detailAddress;
    familyList.value = props.patientData.familyList;
  }
);
watch(
  () => props.patientData,
  () => {
    regionValue.value = [
      props.patientData.province || '',
      props.patientData.city || '',
      props.patientData.county || '',
    ];
    detailsAddress.value = props.patientData.detailAddress;
  }
);
watch(
  () => global.userId,
  () => {
    getPatientDeviceLists();
  }
);

onMounted(() => {
  getPatientDeviceLists();
});
</script>
<style scoped lang="less">
.info-edit {
  padding: 0 16px;
  .base-info {
    .til {
      font-size: 14px;
      font-weight: bold;
      color: #101b25;
      padding: 16px 0 8px 0;
    }
    .info-box {
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #d2dce6;
      border-right: none;
      border-bottom: none;

      .info-item {
        width: 50%;
        height: 44px;
        line-height: 44px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #d2dce6;
        .label {
          width: 120px;
          height: 100%;
          background: #f7f8fa;
          border-right: 1px solid #d2dce6;
          text-indent: 12px;
          font-size: 14px;
          color: #7a8599;
          white-space: nowrap;
        }
        .value {
          flex: 1;
          height: 100%;
          border-right: 1px solid #d2dce6;
          text-indent: 12px;
          font-size: 14px;
          color: #3a4762;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .info-item-time {
        width: 100%;
      }
      .info-item.line {
        width: 100%;
        .value {
        }
      }
      .about-equipment {
        width: 100%;
        height: 44px;
        border-right: 1px solid #d2dce6;
        border-bottom: 1px solid #d2dce6;
        padding: 0 12px;
        box-sizing: border-box;
        .left-msg {
          font-size: 14px;
          .equipment-recall {
            color: #e63746;
          }
          .push-buy-link {
            color: #2e6be6;
          }
        }
      }
    }
  }
  .equipment-type {
    width: 100%;
    height: 44px;
    align-items: center;
    background: #f7f8fa;
    font-size: 14px;
    color: #3a4762;
    font-weight: bold;
    padding: 0 12px;
    border: 1px solid #d2dce6;
    border-top: none;
    border-bottom: none;
    .el-button {
      margin-left: 16px;
    }
    .type {
      > span {
        font-weight: normal;
      }
    }
    &:nth-of-type(2) {
      border-top: 1px solid #d2dce6;
    }
  }
  .equipment-type.no-bind {
    .type > span {
      color: #7a8599;
    }
  }
}
.item {
  .titleBox {
    font-size: 14px;
    color: #101b25;
    margin-right: 16px;
    width: 170px;
    text-align: right;
  }
}
</style>
<style lang="less">
.btn-box {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9e8eb;
  .cancel-btn {
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    color: #606266;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
  }
  .sure-btn {
    width: 76px;
    height: 32px;
    background: #0a73e4;
    border-radius: 2px;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 16px;
  }
}
.operation {
  .el-button {
    height: 20px;
    padding: 0;
    margin: 0;
    margin-left: 16px;
    font-size: 14px;
    &:first-of-type {
      color: #e63746;
    }
    &:last-of-type {
      color: #2e6be6;
    }
  }
}
.untie-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .btn-box {
    border: none;
  }
}
.recall-visible {
  .el-dialog__header {
    padding-bottom: 12px;
  }
  :deep(.my-header) {
    .title {
      font-weight: bold;
      font-size: 16px;
      color: #15233f;
    }
  }
}
.purchase-btn {
  border-top: 0;
}
</style>
