<template>
  <div class="h-60 mb-8 flex bg-[#fff]">
    <div
      v-for="(item, index) in imStore.patientTeamList"
      :key="item.teamNumber"
      class="item"
      :class="[
        item.teamNumber === teamId ? 'active' : '',
        index === 0 ? 'patient' : '',
        item.teamType === 4 ? 'group' : '',
      ]"
      @click="() => clickHandler(item)"
    >
      <div v-if="index === 0" class="avator">
        {{ getAvatorName(item?.teamName) }}
      </div>
      <div v-if="curTeamUnreadNum(item)" class="unread">
        {{ curTeamUnreadNum(item) }}
      </div>
      <div class="content">
        <div class="name">
          <Text>{{ item.teamName }}</Text>
        </div>
        <div class="desc_list">
          <div v-for="(v, i) in item.userList" :key="i" class="desc">
            <div class="text">
              <Text v-if="item.teamType === 4">{{ v.userName }}</Text>
              <Text v-else>{{ v.roleName }}</Text>
            </div>
            <PhoneCall
              v-if="index !== 0"
              :name="v.userName"
              :tel="v.userPhone"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Text from '@/components/Text/index.vue';
import PhoneCall from '@/components/PhoneCall/index.vue';
import store from '@/store';
import { IApiPatientConversationTeamListTeamList } from '@/interface/type';
import { getAvatorName } from '@/pages/Workbench/Left/utils';
const imStore = store.useIM();
interface IProps {
  teamId: string | number;
}
const emits = defineEmits(['change']);

const props = defineProps<IProps>();
defineOptions({
  name: 'ChatHeader',
});

const clickHandler = (item: IApiPatientConversationTeamListTeamList) => {
  if (item.teamNumber === props.teamId) return;
  emits('change', item);
};
const curTeamUnreadNum = computed(() => item => {
  const res = imStore.teamUnreadMap[item.teamNumber ?? ''] ?? 0;
  return res > 99 ? '99+' : res;
});
</script>

<style scoped lang="less">
.item {
  position: relative;
  width: 116px;
  height: 60px;
  background: #f6f8fb;
  border-radius: 4px;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  border: 1px solid transparent;
  &:not(:last-child) {
    margin-right: 12px;
  }
  &:first-child {
    .desc .text {
      max-width: 85px;
    }
  }
  .unread {
    position: absolute;
    min-width: 16px;
    height: 16px;
    border-radius: 8px;
    right: -8px;
    top: -8px;
    font-size: 12px;
    padding: 2px;
    text-align: center;
    line-height: 12px;
    background: #e63746;
    color: #fff;
  }
  .avator {
    width: 30px;
    height: 30px;
    flex-shrink: 0;
    background: #fff;
    font-size: 16px;
    font-weight: bold;
    color: #2e6be6;
    border-radius: 100%;
    text-align: center;
    line-height: 30px;
    margin-right: 8px;
  }
  .content {
    flex: 1;
  }
  .name {
    max-width: 85px;
    overflow: hidden;
    font-size: 14px;
    height: 20px;
    font-weight: bold;
    color: #101b25;
    line-height: 20px;
  }
  .desc {
    width: 100%;
    font-size: 14px;
    height: 20px;
    color: #7a8599;
    line-height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .text {
      max-width: 60px;
    }
  }
}
.patient {
  width: 138px;
}
.group {
  width: 200px;
  .desc {
    width: 90px;
    justify-content: start;
    .text {
      margin-right: 8px;
    }
  }
}
.active {
  border: 1px solid #2e6be6;
  background: rgb(230, 241, 252);
  position: relative;
  &:after {
    position: absolute;
    content: '/';
    font-size: 0;
    width: 0;
    height: 0;
    border: 4px solid #2e6be6;
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-right-color: transparent;
    bottom: -8px;
    left: calc(50% - 2px);
  }
  &:before {
    position: absolute;
    content: '/';
    font-size: 0;
    width: 0;
    height: 0;
    border: 4px solid rgb(230, 241, 252);
    border-bottom-color: transparent;
    border-left-color: transparent;
    border-right-color: transparent;
    bottom: -7px;
    left: calc(50% - 2px);
    z-index: 1;
  }
}
.desc_list {
  display: flex;
}
</style>
