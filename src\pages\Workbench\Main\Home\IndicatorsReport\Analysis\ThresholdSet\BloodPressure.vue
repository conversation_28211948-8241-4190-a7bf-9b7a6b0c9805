<template>
  <div class="threshold">
    <template v-for="item in configData" :key="item.level">
      <div class="title">
        <span class="text-danger mr-2xs">{{ item.warningInfo.title }}</span>
        {{ item.warningInfo.tips }}
      </div>
      <div class="flex-bc pt-3xs pb-md text-sm">
        <div class="w-72">{{ item.label }}</div>
        <div class="flex-c">
          <div class="pr-2xs">收缩压mmHg</div>
          <InputNumber
            v-model="item.systolic.value"
            type="number"
            step-strictly
            :min="2"
            :max="999"
            :placeholder="`默认：${item.systolic.defaultVal}`"
            @blur="e => onInputBlur(e, item, 'systolic')"
          />
        </div>
        <div class="flex-c">
          <div class="pr-2xs">舒张压mmHg</div>
          <InputNumber
            v-model="item.diastolic.value"
            type="number"
            step-strictly
            :min="1"
            :max="998"
            :placeholder="`默认：${item.diastolic.defaultVal}`"
            @blur="e => onInputBlur(e, item, 'diastolic')"
          />
        </div>
      </div>
    </template>
    <div class="title">
      <span class="text-danger mr-2xs">降值风险</span>
      一旦某次收缩压相比前一个月平均值，降幅超过20%
    </div>
  </div>
</template>

<script setup lang="ts">
import InputNumber from '@/components/InputNumber/index.vue';
import { IBloodPressure } from '@/pages/Workbench/Main/Home/IndicatorsReport/Analysis/Content/hooks/useThresholdSet';
interface IProps {
  configData: IBloodPressure[];
}
const props = withDefaults(defineProps<IProps>(), {
  configData: () => [],
});

const emits = defineEmits<{
  (e: 'deal-error'): void;
}>();

function onInputBlur(
  event: FocusEvent,
  currentItem: IBloodPressure,
  type: string
) {
  const val = +(event.target as HTMLInputElement).value;
  if (!val) return;
  const { level } = currentItem;
  const prevLevelItem = props.configData.find(item => item.level === level - 1);
  const nextLevelItem = props.configData.find(item => item.level === level + 1);
  let errMsg = '';
  // 当前值
  let tempVal: number | undefined = val;

  // 收缩压值
  const systolicVal = currentItem.systolic.value;
  const nextLevelSystolicVal = nextLevelItem?.systolic.value;
  const prevLevelSystolicVal = prevLevelItem?.systolic.value;

  // 舒张压值
  const diastolicVal = currentItem.diastolic.value;
  const nextLevelDiastolicVal = nextLevelItem?.diastolic.value;
  const prevLevelDiastolicVal = prevLevelItem?.diastolic.value;

  switch (type) {
    case 'systolic':
      {
        if (diastolicVal && val <= diastolicVal) {
          tempVal = diastolicVal + 1;
          errMsg = '收缩压应大于舒张压,当前最小值为：';
        } else if (nextLevelSystolicVal && val >= nextLevelSystolicVal) {
          tempVal = nextLevelSystolicVal - 1;
          errMsg = `当前值应小于${nextLevelItem.label},当前最大值为：`;
        } else if (prevLevelSystolicVal && val <= prevLevelSystolicVal) {
          tempVal = prevLevelSystolicVal + 1;
          errMsg = `当前值应大于${prevLevelItem.label},当前最小值为：`;
        }

        if (tempVal < 2 || tempVal > 999) tempVal = undefined;
        currentItem.systolic.value = tempVal;
      }

      break;
    case 'diastolic':
      {
        if (systolicVal && val >= systolicVal) {
          tempVal = systolicVal - 1;
          errMsg = '舒张压应小于收缩压,当前最大值为：';
        } else if (nextLevelDiastolicVal && val >= nextLevelDiastolicVal) {
          tempVal = nextLevelDiastolicVal - 1;
          errMsg = `当前值应小于${nextLevelItem.label},当前最大值为：`;
        } else if (prevLevelDiastolicVal && val <= prevLevelDiastolicVal) {
          tempVal = prevLevelDiastolicVal + 1;
          errMsg = `当前值应大于${prevLevelItem.label},当前最小值为：`;
        }

        if (tempVal < 1 || tempVal > 999) tempVal = undefined;
        currentItem.diastolic.value = tempVal;
      }
      break;
  }

  if (errMsg) {
    let msg = errMsg + tempVal;
    if (!tempVal)
      msg = '当前值应为小于等于999的正整数(收缩压大于1)且满足阈值等级范围限制';
    emits('deal-error');
    ElMessage.error(msg);
  }
}
</script>
<style scoped lang="less">
.threshold {
  padding: 24px 60px 0;

  .title {
    color: #708293;
  }

  div.flex-bc:last-child {
    padding-bottom: 4px;
  }
}
</style>
