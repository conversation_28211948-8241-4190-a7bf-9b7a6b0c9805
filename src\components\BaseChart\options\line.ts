import { LineECOption } from '../type';
import { LineSeriesOption } from 'echarts/charts';
import { ZRColor } from 'echarts/types/dist/shared';
import { dataZoomData } from './index';
export const getLineEchartsOptions = (config: any): LineECOption => {
  const {
    xAxisData,
    seriesConfig,
    grid = {},
    xAxis = {},
    yAxis = {},
    title = {},
    tooltipConfig = {},
    legend = { enable: true, config: {} },
    dataZoom = { enable: false, config: undefined },
    extra = {},
  } = config;

  return {
    title,
    grid: {
      left: 40,
      bottom: dataZoom.enable ? 80 : 60,
      ...grid,
    },
    tooltip: { trigger: 'axis', ...tooltipConfig },
    dataZoom: dataZoom.enable
      ? dataZoom.config
        ? dataZoom.config
        : dataZoomData
      : null,
    yAxis: {
      axisLabel: { formatter: (value: any) => (value === 0 ? '' : value) },
      splitLine: { lineStyle: { type: 'dashed' } },
      ...yAxis,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLine: { show: false },
      axisTick: { show: false },
      data: xAxisData,
      ...xAxis,
    },
    legend: legend.enable
      ? {
          bottom: 0,
          icon: 'stack',
          itemWidth: 10,
          itemHeight: 10,
          ...legend.config,
        }
      : null,
    series: generateLineSeries(seriesConfig),
    ...extra,
  };
};

interface ILineSeriesOption extends LineSeriesOption {
  markLineData: {
    yAxis: string | number;
    name?: string | number;
    color?: ZRColor;
    labelPosition?: string;
  }[];
}
export const generateLineSeries = (
  series: Array<ILineSeriesOption>
): LineECOption['series'] => {
  if (!series?.length) return [];
  return series.map(item => {
    const {
      color = '#0A73E4',
      name,
      data = [],
      markArea,
      markLine,
      markLineData,
      ...otherObj
    } = item;
    return {
      name,
      data,
      type: 'line',
      connectNulls: true,
      lineStyle: { color },
      itemStyle: { color },
      markLine:
        markLine ??
        (markLineData && {
          emphasis: { disabled: true },
          symbol: 'none',
          data: markLineData?.map(mark => ({
            name: mark.name ?? name,
            yAxis: mark.yAxis,
            symbol: 'none',
            lineStyle: { color: mark.color ?? color },
            label: {
              padding: 12,
              fontSize: 12,
              color: mark.color ?? color,
              position: mark.labelPosition ?? 'end',
              formatter: params => params.name,
            },
          })),
        }),
      markArea,
      ...otherObj,
    };
  }) as LineECOption['series'];
};
