import { IrefundReasonInfo } from './type';

/* 服务包退款和硬件退款 */
export const refundReasonList: IrefundReasonInfo[] = [
  {
    label: '患者去世',
    value: 1,
  },
  {
    label: '患者不配合',
    value: 2,
  },
  {
    label: '家属不同意',
    value: 3,
  },
  {
    label: '患者觉得没必要',
    value: 4,
  },
  {
    label: '管理问题',
    value: 19,
  },
  {
    label: '系统问题（系统复杂；不好操作）',
    value: 5,
  },
  {
    label: '设备问题（上传问题、设备不准）',
    value: 6,
  },
  {
    label: '费用问题（患者经济困难、费用太高）',
    value: 7,
  },
  {
    label: '患者转院（患者回住地就医、患者前往其他城市或医院就医）',
    value: 8,
  },
  {
    label: '二次入院',
    value: 9,
  },
  {
    label: '三方平台',
    value: 10,
  },
  {
    label: '医生影响（医生告知不需要入组、医生推荐其他平台、有医护亲属或朋友）',
    value: 11,
  },
  { label: '医闹风险', value: 12 },
  { label: '医院离家近', value: 14 },
  { label: '当地医院就近复查', value: 15 },
  { label: '服务包更换', value: 16 },
  { label: '其他', value: 13 },
];
/* 押金退款 */
export const depositRefundReasonList: IrefundReasonInfo[] = [
  {
    label: '科研到期',
    value: 17,
  },
  {
    label: '退出科研',
    value: 18,
  },
];

/* 处理创建订单请求接口返回 code */
export const createOrderCode = (code: string) => {
  let errMsg: string;
  if (code == 'E080305') {
    errMsg = '产品状态异常！';
  } else if (code == 'E080306') {
    errMsg = '患者未绑定工作室！';
  } else if (code == 'E080307') {
    errMsg = '重复发起订单！';
  } else if (code == 'E080309') {
    errMsg = '患者未关注公众号！';
  } else if (code == 'E080312' || code == 'E080313') {
    errMsg = '设备状态异常！';
  } else {
    errMsg = '订单创建失败！';
  }

  return errMsg;
};

/* 服务包退款和硬件退款->退费信息rules */
export const refundRules = {
  refundDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
  companyName: [
    { required: true, message: '请选择分公司名称', trigger: 'change' },
  ],
  isReturnDevice: [
    {
      required: true,
      message: '请选择是否退回硬件设备',
      trigger: 'change',
    },
  ],
  deductDeviceMoney: [
    {
      required: true,
      message: '请选择是否扣除硬件设备费用',
      trigger: 'change',
    },
  ],
  returnReason: [
    { required: true, message: '请选择退款原因', trigger: 'blur' },
  ],
  returnReasonDetails: [
    { required: true, message: '请输入详情原因', trigger: 'blur' },
  ],
  refundMoney: [{ required: true, message: '请输入实退金额', trigger: 'blur' }],
  refundType: [
    { required: true, message: '请选择退款方式', trigger: 'change' },
  ],
  isInvoicing: [
    { required: true, message: '请选择是否开具发票', trigger: 'change' },
  ],
  companyId: [
    { required: true, message: '请选择分公司名称', trigger: 'change' },
  ],
  payObject: [{ required: true, message: '请选择支付对象', trigger: 'change' }],
};
/* 押金退款的rules */
export const depositRefundRules = {
  returnReason: [
    { required: true, message: '请选择退款原因', trigger: 'blur' },
  ],
  deviceDamageStatus: [
    { required: true, message: '请选择设备是否损坏', trigger: 'change' },
  ],
  refundType: [{ required: true, message: '请选择退款方式', trigger: 'blur' }],
  payObject: [{ required: true, message: '请选择支付对象', trigger: 'change' }],
};

/* 支付对象 */
export const payObjectList: IrefundReasonInfo[] = [
  {
    value: 1,
    label: '患者缴费',
  },
  {
    value: 2,
    label: '健康顾问缴费',
  },
  {
    value: 3,
    label: '公司账号缴费',
  },
];
