export * from './dynamic-form';
export * from './report-type';
export * from './role';
/** NIM 网易云信即时通讯图片地址 */
export const NIM_UPLOAD_PERSISTENT_URLS = 'https://nim-nosdn.netease.im/';

/** 七牛云图片上传相关配置 */
export const QINIU_UPLOAD_CONFIG = {
  // 凭证过期时间
  expiresInTime: 3600000,
  // 七牛云上传返回key（图片）拼接前缀正式环境
  url: 'https://image.scheartmed.com/',
  // 七牛云上传返回key（图片）拼接前缀测试&UAT
  urlQU: 'https://hrt-devimages.hrttest.cn/',
  // 兼容历史数据域名（http）
  urlHttp: 'http://image.scheartmed.com/',
};

/** 公共信息展示（中转）H5项目域名：目前用于结构化报告 */
export const INFORMATION_H5_URL = {
  development: 'https://www.hrttest.cn/information/#/',
  test: 'https://www.hrttest.cn/information/#/',
  uat: 'https://uat.hrttest.cn/information/#/',
  production: 'https://scheartmed.com/information/#/',
};
