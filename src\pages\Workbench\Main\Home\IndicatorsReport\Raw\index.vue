<template>
  <div class="wrapper">
    <Indicator
      :list="indicatorList"
      :active-item="actIdxItem"
      @handle-click="handleChangeIdx"
    />

    <div class="content-header mt-lg p-xs">
      <div>{{ actIdxItem?.name }}</div>
      <i-ep-circle-plus
        v-show="indexTermId !== -2"
        class="pl-2xs cursor-pointer text-primary text-lg font-semibold"
        @click="handleAdd"
      />
    </div>

    <TablePanel
      ref="TablePanelRef"
      :table-req-param="tReqParam"
      @handle-action="info => handleAction(globalData.userId!, info)"
    />

    <!-- 报告 -->
    <Dialog
      v-model:visible="repVisible"
      destroy-on-close
      :title="actIdxItem?.name"
      :width="960"
    >
      <!-- 动态血压检测详情 -->
      <div v-if="indexTermId === -2" class="p-sm">
        <BaseTable
          :max-height="480"
          :data="bloodPressureList"
          :cell-style="cellClassNameHandler"
          :pagination="false"
        >
          <el-table-column type="index" label="序号" width="88" />
          <el-table-column prop="timeFrame" label="时段" />
          <el-table-column prop="systolic" label="收缩压(mmHg)" />
          <el-table-column prop="diastolic" label="舒张压(mmHg)" />
          <el-table-column prop="measureTime" label="测量日期" />
        </BaseTable>
      </div>
      <template v-else>
        <div class="max-h-[470px] overflow-y-auto pt-sm">
          <SingleDynamicForm
            :key="singleDynamicFormKey"
            ref="DynamicFormRef"
            :mode="formMode"
            :category="actIdxItem?.key || ''"
            :extra-data="{
              source_type: actReItem?.sourceType || 3,
              report_id: actReItem?.reportId,
              patient_id: globalData.userId,
              index_term_id: indexTermId,
            }"
            :query-params="{
              source_type: actReItem?.sourceType || 3,
              report_id: actReItem?.reportId,
              patient_id: globalData.userId,
              index_term_id: indexTermId,
            }"
          />
        </div>
        <div v-if="formMode !== 'view'" class="btn-group">
          <el-button type="primary" @click="saveDynamicForm">保存</el-button>
          <el-button @click="repVisible = false">取消</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { CSSProperties } from 'vue';
import bus from '@/lib/bus';
import { SingleDynamicForm } from '@/features';
import BaseTable from '@/components/BaseTable';
import Dialog from '@/components/Dialog/index.vue';
import Indicator from '../Indicator.vue';
import TablePanel, {
  ITableReqParamProps,
  IHandleActionProps,
} from './TablePanel.vue';
import { useHandleData, useExaminationList } from '@/hooks';
import { formatTimeTemplate } from '@/utils';
import store from '@/store';
import { IRawActItem } from '@/pages/Workbench/Main/Home/IndicatorsReport/utils';
import { getReportBloodDetail, removeReport } from '@/api/indicatorsReport';
import {
  IApiPatientReportBloodDetail,
  IApiPatientReportListData,
} from '@/interface/type';
import { FormModeValues } from '@/constant';
import { promiseTimeout } from '@vueuse/core';
defineOptions({ name: 'IdxRaw' });

const globalData = store.useGlobal();
const indicatorInfo = store.useIndicatorInfo();

const DynamicFormRef = shallowRef();
const TablePanelRef = shallowRef();
const getTableList = () => TablePanelRef.value?.baseTableRef?.getTableList();

const repVisible = ref(false);
const singleDynamicFormKey = ref(0);
const formMode = ref<FormModeValues>();

const indicatorList = ref<IRawActItem[]>();
/** 当前操作指标项Act */
const actIdxItem = ref<IRawActItem>();
/** 当前操作表单[table]项 */
const actReItem = ref<IApiPatientReportListData>();
const indexTermId = computed(
  () => actReItem.value?.indexTermId || actIdxItem.value?.id
);
/** table请求参数 */
const tReqParam = reactive<ITableReqParamProps>({});
const { rawData } = useExaminationList();
watch(
  rawData,
  list => {
    if (list?.length) {
      const curList = list?.map(item => ({ ...item, id: item.indexTermId }));
      indicatorList.value = curList;
      actIdxItem.value = curList[0];
      setTReqParam(curList[0]);
      dealMsgBoxIndicator();
    }
  },
  { deep: true, immediate: true }
);
function handleChangeIdx(item: IRawActItem) {
  actIdxItem.value = item;
  actReItem.value = undefined;
  setTReqParam(item);
}
/** 设置Table参数 */
function setTReqParam(item: IRawActItem) {
  const { indexTermId } = item;
  if (indexTermId) {
    nextTick(() => {
      // 接口约定除动态血压外其他传1
      tReqParam.report = indexTermId === -2 ? undefined : 1;
      tReqParam.indexTermId = indexTermId;
      tReqParam.patientId = Number(globalData.userId);
    });
  }
}
/** 表格数据异常cell样式 */
const cellClassNameHandler = (data: any) => {
  const styleObj: CSSProperties = {};
  const { row, columnIndex } = data;
  const { errorLevel } = row;
  if (columnIndex === 2 && errorLevel > 0) {
    styleObj.color = 'red';
  }
  return styleObj;
};

/** 获取动态血压详情 */
const bloodPressureList = ref<IApiPatientReportBloodDetail>([]);
async function getReportBloodDetailData(patientId: number) {
  const { bloodPressureEndTime = '', checkTime = '' } = actReItem.value!;
  const resList = await getReportBloodDetail({
    bloodPressureEndTime,
    patientId,
    checkTime,
  });
  bloodPressureList.value = resList?.map(item => ({
    ...item,
    measureTime: formatTimeTemplate(item.measureTime),
  }));
}

/** table action */
async function handleAction(
  patientId: number,
  { actionType: acType, item }: IHandleActionProps
) {
  actReItem.value = item;
  switch (acType) {
    case 'edit':
    case 'view':
      repVisible.value = true;
      formMode.value = acType;
      // 动态血压
      if (item?.indexTermId === -2) {
        await getReportBloodDetailData(patientId);
      }
      break;
    case 'delete':
      await useHandleData(
        removeReport,
        { reportId: Number(item?.reportId) },
        '是否确认删除？'
      );
      getTableList();
      break;
  }
}
/** 新增报告 */
const handleAdd = () => {
  if (indexTermId.value) {
    actReItem.value = undefined;
    repVisible.value = true;
    DynamicFormRef.value?.clear();
    formMode.value = 'create';
  }
};
/** 保存表单 */
const saveDynamicForm = async () => {
  const res = await DynamicFormRef.value?.submit();
  if (res !== undefined) {
    repVisible.value = false;
    getTableList();
  }
};

watch(repVisible, visible => {
  if (visible) singleDynamicFormKey.value++;
});

/** 处理消息盒子推送传递标识 */
async function dealMsgBoxIndicator() {
  if (!indicatorInfo.bloodPressureMonitoring) return;
  await promiseTimeout(500);
  const { patientId, indexTermId } = indicatorInfo.bloodPressureMonitoring;
  if (Number(globalData.userId) === Number(patientId)) {
    actIdxItem.value = indicatorList.value?.find(
      item => item.indexTermId === indexTermId
    );
    indicatorInfo.bloodPressureMonitoring = undefined;
  }
}

onMounted(() => {
  bus.on('refresh-indicators-original-table', () => {
    if (indexTermId.value === -2) getTableList(); // 动态血压
  });
});
onBeforeUnmount(() => {
  bus.off('refresh-indicators-original-table');
});
</script>
<style scoped lang="less">
:deep(.el-button:not(.is-link)) {
  min-width: 76px;
}

.wrapper {
  :deep(.check-list) {
    .header {
      display: none;
    }
    .content {
      max-height: 440px;
    }
  }

  .btn-group {
    padding: 24px 0 0 106px;
    .el-button {
      width: 76px;
    }
  }
}

.content-header {
  position: relative;
  border-bottom: 3px solid #8193a3;
  margin-bottom: 7px;
  display: flex;
  align-items: center;
  &:before,
  &:after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
  }
  &:before {
    top: -6px;
    height: 6px;
    background: #f6f8fb;
  }
  &:after {
    bottom: -6px;
    height: 1px;
    background: #8193a3;
  }
}
</style>
