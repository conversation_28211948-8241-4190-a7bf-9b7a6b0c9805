<template>
  <div class="">
    <div v-if="types === 1" class="result p-12 mb-16">
      <p class="pb-4">
        总分：{{ score?.allScore }}
        <span v-if="score?.extraScoreContent" class="pl-3xs">
          {{ score?.extraScoreContent }}
        </span>
      </p>
      <div>
        {{
          isLatest
            ? conclusion.content?.content
            : conclusion.levelResult?.content
        }}
      </div>
    </div>
    <!-- 焦虑抑郁筛查问卷-->
    <div
      v-if="types === 4 && conclusion?.content?.length"
      class="result p-12 pb-1 mb-16"
    >
      <div v-for="(item, i) in conclusion.content" :key="i" class="flex mb-12">
        <div class="label w-60 mr-16">{{ item.name }}</div>
        <div class="flex-1">{{ item.text || '--' }}</div>
      </div>
    </div>
    <!--    一般情况问卷-->
    <div v-if="types === 2" class="result p-12 pb-1 mb-16">
      <div v-for="(item, i) in defaultSurvey" :key="i" class="flex mb-12">
        <div class="label w-60 mr-16">{{ item.name }}</div>
        <div class="flex-1">{{ item.text || '--' }}</div>
      </div>
    </div>
    <!--    生活质量问卷-->
    <div
      v-if="
        types === 3 &&
        (liveSurveyObj.conclusion || liveSurveyObj.scoreList?.length)
      "
      class="result p-12 pb-1 mb-16"
    >
      <div class="mb-12">{{ liveSurveyObj.conclusion }}</div>
      <div
        v-for="(item, i) in liveSurveyObj.scoreList"
        :key="i"
        class="flex mb-12"
      >
        <div class="label w-90 mr-16">{{ item.name }}</div>
        <div class="flex-1">
          {{ item.score || '--' }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface IProps {
  conclusion?: object;
  score?: object;
  // 是否是最新版本
  isLatest?: boolean;
}
const props = defineProps<IProps>();

const defaultSurvey = ref([
  { name: 'BMI', text: '', keyName: 'BMIResultMessage' },
  { name: '饮酒史', text: '', keyName: 'drinkResultMessage' },
  { name: '吸烟史', text: '', keyName: 'smokingResultMessage' },
  { name: '饮食习惯', text: '', keyName: 'dietaryHabitResultMessage' },
  { name: '运动', text: '', keyName: 'sportPerWeekTimeResultMessage' },
]);
const liveSurveyObj = ref({
  conclusion: '',
  scoreList: [
    { name: '生理机能', score: '', keyName: 'physicalFunctioning' },
    { name: '躯体疼痛', score: '', keyName: 'bodilyPain' },
    { name: '精力', score: '', keyName: 'vitality' },
    { name: '情感职能', score: '', keyName: 'roleEmotional' },
    { name: '健康变化', score: '', keyName: 'reportedHealthTransition' },
    { name: '生理职能', score: '', keyName: 'rolePhysical' },
    { name: '一般健康状况', score: '', keyName: 'generalHealth' },
    { name: '社会功能', score: '', keyName: 'socialFunctioning' },
    { name: '精神健康', score: '', keyName: 'mentalHealth' },
  ],
});
const initResult = () => {
  const conclusion = props.isLatest
    ? props.conclusion?.content
    : props.conclusion;
  if (types.value == 2) {
    defaultSurvey.value = defaultSurvey.value.map(re => {
      re['text'] = conclusion[re.keyName];
      return re;
    });
  }
  if (types.value == 3) {
    liveSurveyObj.value['conclusion'] = props.isLatest
      ? conclusion
      : conclusion.levelResult;

    liveSurveyObj.value['scoreList'] = props.score
      ? liveSurveyObj.value['scoreList'].map(re => {
          re['score'] = Number(props.score?.otherScore?.[re.keyName]);
          return re;
        })
      : [];
  }
};
const types = computed(() => {
  let type = 1;
  const conclusionType = props.conclusion?.type || '';
  if (props.isLatest && conclusionType) {
    switch (conclusionType) {
      case 'GAD-PHQ':
        type = 4;
        break;
      case 'LIFE':
        type = 3;
        break;
      case 'NORMAL':
        type = 2;
        break;
      case 'PSQI':
      case 'KCCP':
        type = 1;
        break;
    }
  } else {
    if (
      props.conclusion.levelResult &&
      typeof props.conclusion.levelResult === 'object'
    ) {
      type = 1;
    }
    if (!props.conclusion.levelResult) {
      type = 2;
    }
    if (
      props.conclusion.levelResult &&
      typeof props.conclusion.levelResult === 'string'
    ) {
      type = 3;
    }
  }
  return type;
});
onMounted(() => {
  initResult();
});
watch(
  () => props.conclusion,
  () => {
    initResult();
  }
);
</script>

<style scoped lang="less">
.result {
  width: 100%;
  background: #ecf4fc;
  border-radius: 4px;
  font-size: 14px;
  color: #3a4762;
  line-height: 20px;
  p,
  .label {
    font-weight: bold;
    font-size: 14px;
    color: #101b25;
  }
}
</style>
