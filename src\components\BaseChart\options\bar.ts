import { BarECOption } from '../type';
import { BarSeriesOption } from 'echarts/charts';

import { dataZoomData } from './index';

export const getBarEchartsOptions = (config: any): BarECOption => {
  const {
    title = {},
    xAxisData,
    seriesConfig,
    yAxis = {},
    dataZoom = { enable: false },
    grid = {},
  } = config;

  return {
    title,
    grid: {
      left: 60,
      bottom: '8%',
      top: '20%',
      ...grid,
    },
    dataZoom: dataZoom.enable ? dataZoomData : null,
    xAxis: {
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#C8D0D7',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#7D8292',
      },
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
      alignTicks: true,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#C8D0D7',
          type: 'dashed',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#7D8292',
        },
      },
      ...yAxis,
    },
    series: generateBarSeries(seriesConfig),
  };
};

export const generateBarSeries = (
  series: Array<BarSeriesOption>
): BarECOption['series'] => {
  if (!series?.length) return [];
  return series.map(item => {
    return {
      type: 'bar',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          x2: 0,
          y: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#62A5FF',
            },
            {
              offset: 1,
              color: '#3365FF',
            },
          ],
        },
      },
      barMinHeight: 1,
      ...item,
    };
  }) as BarECOption['series'];
};
