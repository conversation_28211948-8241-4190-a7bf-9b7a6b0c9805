import {
  getAbnormalDistributionQuery,
  getEvenDistributionQuery,
  getLineChartQuery,
} from '@/api/indicatorsReport';
import {
  getEchartsLineConfig,
  getEvenEchartsLineConfig,
  getAbnormalEchartsLineConfig,
} from './utils';
import { LineECOption, BarECOption } from '@/components/BaseChart';
import useAbnormalContent from '../hooks/useAbnormalContent';

const frequencyMap: Record<string, string> = {
  1: '7天',
  2: '12周',
  3: '12月',
};

const anomalyFrequencyMap: Record<string, string> = {
  1: '12周',
  2: '12月',
};
/** @description chart */
export function useChartData() {
  const loading = ref(false);
  const lineOptions = ref<LineECOption>({});
  const averageOptions = ref<LineECOption>({});
  const anomalyOptions = ref<BarECOption>();
  const switchType = ref(1);
  const anomalySwitchType = ref(1);

  const { riskDesc, formatFinalData } = useAbnormalContent();

  async function getLineChartData(params: any) {
    loading.value = true;
    const res = await getLineChartQuery(params);
    loading.value = false;
    lineOptions.value = getEchartsLineConfig(
      res,
      params.checkType,
      params.indexType
    );
  }

  // 平均分布
  async function getAverageLineChartData(params: any) {
    loading.value = true;
    const { contentResponseDTO, ...rest } =
      await getEvenDistributionQuery(params);
    loading.value = false;
    averageOptions.value = getEvenEchartsLineConfig(rest, params.checkType);
    formatFinalData(contentResponseDTO, {
      frequency: frequencyMap[String(switchType.value)],
      id: params.checkType,
    });
  }

  // 异常分布
  async function getAnomalyChartData(params: any) {
    loading.value = true;
    const { contentResponseDTO, indexDataDTOList } =
      await getAbnormalDistributionQuery(params);
    loading.value = false;
    anomalyOptions.value = getAbnormalEchartsLineConfig(indexDataDTOList);
    formatFinalData(contentResponseDTO, {
      frequency: anomalyFrequencyMap[String(anomalySwitchType.value)],
      id: params.checkType,
    });
  }
  return {
    loading,
    riskDesc,
    switchType,
    anomalySwitchType,
    getLineChartData,
    lineOptions,
    averageOptions,
    anomalyOptions,
    getAverageLineChartData,
    getAnomalyChartData,
  };
}
