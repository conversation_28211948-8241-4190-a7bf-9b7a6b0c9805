<template>
  <div class="scientific">{{ TagMap[type] }}</div>
</template>

<script setup lang="ts">
interface IProps {
  type: number;
}

const TagMap = ref({
  2: '科研干预',
  3: '科研对照',
});
defineProps<IProps>();
defineOptions({
  name: 'PatientType',
});
</script>

<style scoped lang="less">
.scientific {
  position: absolute;
  top: 55px;
  width: 46px;
  height: 18px;
  line-height: 16px;
  font-style: italic;
  color: #3bb4cc;
  font-size: 10px;
  border-radius: 2px;
  border: 1px solid #3bb4cc;
}
</style>
