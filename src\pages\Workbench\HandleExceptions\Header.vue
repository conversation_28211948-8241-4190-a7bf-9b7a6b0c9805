<template>
  <div class="p-sm bg-white">
    <div class="base-info flex items-center text-sm">
      <div class="base-info-last-name text-base font-semibold">
        {{ userInfo.patientName?.slice(0, 1) }}
      </div>
      <div class="base-info-name px-xs">{{ userInfo.patientName }}</div>
      <div>|</div>
      <div class="px-xs">
        {{ userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '--' }}
      </div>
      <div>|</div>
      <div class="px-xs">{{ userInfo.age }}岁</div>
    </div>
    <div class="pt-sm diagnose flex items-center flex-wrap">
      <div
        v-for="item in targetList"
        :key="item.key"
        class="diagnose-item flex items-center text-sm px-2xs box-border my-3xs mr-2xs"
      >
        <span>
          <img :src="item.icon" alt="icon" />
          {{ item.name }}
        </span>
        <span class="flex-1 text-right box-border">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import bloodPressureIcon from '@/assets/icons/bloodPressure.png';
import heartRateIcon from '@/assets/icons/heartRate.png';
import bloodSugarIcon from '@/assets/icons/bloodSugar.png';
import bloodLipidIcon from '@/assets/icons/bloodLipid.png';
import { IApiRiskTargetValue } from '@/interface/type';
import store from '@/store';
defineOptions({
  name: 'HandleExceptions',
});
interface IProps {
  targetValue?: IApiRiskTargetValue;
}
const userInfo: any = computed(() => store.useGlobal().userInfo);
const props = defineProps<IProps>();
const targetOptions = [
  {
    icon: bloodPressureIcon,
    name: '血压',
    key: 'bloodPressure',
    value: '--',
  },
  {
    icon: heartRateIcon,
    name: '心率',
    key: 'hearRate',
    value: '--',
  },
  {
    icon: bloodSugarIcon,
    name: '空腹血糖',
    key: 'bloodSugarEmpty',
    value: '--',
  },
  {
    icon: bloodSugarIcon,
    name: '非空腹血糖',
    key: 'bloodSugarNotEmpty',
    value: '--',
  },
  {
    icon: bloodLipidIcon,
    name: '血脂',
    key: 'bloodFat',
    value: '--',
  },
];
const targetList = ref(targetOptions);

watch(
  () => props.targetValue,
  val => {
    if (val) {
      targetList.value = targetOptions.map(v => {
        const key = v.key as keyof typeof val;
        return {
          ...v,
          value: val[key] ? val[key]! : v.value,
        };
      });
    }
  }
);
</script>

<style scoped lang="less">
.base-info {
  color: #7a8599;
  &-last-name {
    width: 30px;
    height: 30px;
    color: #2e6be6;
    line-height: 30px;
    text-align: center;
    border-radius: 100%;
    border: 1px solid #efefef;
  }
  &-name {
    color: #3a4762;
  }
}
.diagnose-item {
  min-width: 180px;
  height: 28px;
  background: #f7f8fa;
  > span:first-child {
    color: #111;
    display: flex;
    align-items: center;
  }
  img {
    width: 16px;
    margin-right: 6px;
  }
}
</style>
