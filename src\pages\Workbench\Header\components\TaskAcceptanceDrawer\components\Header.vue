<template>
  <div class="header flex items-center justify-between">
    <div class="search-box flex items-center">
      <template v-if="!isShowTime">
        <div
          v-if="globalStore.currentRole !== 4"
          class="search-item flex items-center mr-24"
        >
          <div class="item-title mr-8">任务类型</div>
          <el-select
            v-model="form.taskType"
            filterable
            reserve-keyword
            placeholder="任务类型"
            style="width: 110px"
            @change="form.taskStatus = -1"
          >
            <el-option
              v-for="item in TASK_TYPE"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-item flex items-center">
          <div class="item-title mr-8">任务状态</div>
          <el-select
            v-model="form.taskStatus"
            filterable
            reserve-keyword
            placeholder="任务状态"
            style="width: 110px"
          >
            <el-option
              v-for="item in changeTaskStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </template>
      <div v-if="isShowTime" class="search-item flex items-center">
        <div class="item-title mr-8">完成时间</div>
        <el-date-picker
          v-model="form.completionTime"
          type="daterange"
          placeholder="选择日期"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="datepicker"
          style="width: 240px"
          value-format="x"
          format="YYYY-MM-DD"
          :disabled-date="pickerOptions"
        />
      </div>
    </div>
    <template v-if="globalStore.currentRole !== 4">
      <el-button
        v-if="!isShowTime"
        link
        class="complete-history"
        @click="queryData(true)"
      >
        完成历史
        <el-icon class="icon-add"><i-ep-arrow-right /></el-icon>
      </el-button>
      <el-button v-else plain @click="queryData(false)">返回</el-button>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { TASK_TYPE, TASK_STATUS } from '../config';
import { taskSearchType } from '../type';
import useGlobal from '@/store/module/useGlobal';
const globalStore = useGlobal();

const form = ref<taskSearchType>({
  taskType: -1,
  taskStatus: -1,
  completionTime: 0,
});
const emit = defineEmits(['queryDataList']);

const queryData = (flag: boolean) => {
  form.value.taskStatus = flag ? 2 : -1;
  isShowTime.value = flag;
};

/* 完成历史模块 */
const pickerOptions = (time: { getTime: () => number }) => {
  return time.getTime() > new Date().getTime();
};
const isShowTime = ref<boolean>(false);

watch(
  [form, isShowTime],
  () => {
    emit('queryDataList', form.value, isShowTime.value);
  },
  {
    immediate: true,
    deep: true,
  }
);

const changeTaskStatus = computed(() => {
  const taskType = globalStore.currentRole === 4 ? 4 : form.value.taskType;
  const list = TASK_STATUS.filter(item => item.type?.includes(taskType));

  return form.value.taskType !== -1 || globalStore.currentRole === 4
    ? list
    : TASK_STATUS;
});
</script>
<style lang="less" scoped>
.item-title {
  color: #3a4762;
  font-size: 14px;
}
.complete-history {
  font-size: 14px;
  color: #2e6be6;
  display: flex;
  align-items: center;
  &:hover {
    opacity: 0.75;
    cursor: pointer;
    color: #2e6be6;
  }
  &:active {
    opacity: 1;
  }
}
</style>
