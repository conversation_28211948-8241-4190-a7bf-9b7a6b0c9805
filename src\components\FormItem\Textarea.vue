<template>
  <div v-if="mode === 'view'" class="break-all">{{ value ?? '--' }}</div>
  <el-input
    v-else
    :model-value="value"
    type="textarea"
    :rows="4"
    resize="none"
    :placeholder="placeholder ?? '请输入'"
    @input="textChangeHandler"
  />
</template>

<script setup lang="ts">
import { Base } from './type';

interface IProps extends Base {
  placeholder?: string;
}
defineProps<IProps>();
const emit = defineEmits(['change']);

const textChangeHandler = (val: string | undefined) => {
  emit('change', val);
};

defineOptions({
  name: 'TextArea',
});
</script>
