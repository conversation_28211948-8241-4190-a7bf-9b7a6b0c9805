<template>
  <div class="mh-40 pb-0 bg-slate">
    <div
      v-for="(config, index) in filterConfigs"
      :key="index"
      class="flex pb-8"
    >
      <CheckGroup
        v-if="config.type.startsWith('checkbox')"
        :value-data="filterValue"
        :config="config"
        @change="checkGroupChange"
      />
      <template v-if="config.type === 'group'">
        <div v-for="(item, i) in config.children" :key="i">
          <el-select
            v-if="item.type === 'select'"
            v-model="filterValue[item.key as keyof typeof filterValue]"
            :placeholder="item.label"
            style="width: 100px; margin-right: 16px"
            @change="(val: string) => changeHandler(item.key, val)"
          >
            <el-option
              v-for="v in item.options"
              :key="v.value"
              :value="v.value"
              :label="v.label"
            />
          </el-select>
          <el-date-picker
            v-if="item.type === 'daterange'"
            v-model="dateValue"
            :type="item.type"
            :start-placeholder="item.label"
            value-format="x"
            style="width: 240px"
            :range-separator="dateValue?.[0] ? '-' : ' '"
            @change="(val: string[]) => changeHandler(item.key, val)"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import CheckGroup from './CheckGroup.vue';

interface IProps {
  filterConfigs: any;
  filterValue: any;
}
defineOptions({
  name: 'TabFilter',
});
const props = defineProps<IProps>();
const emits = defineEmits(['change']);
const dateValue = ref(['', '']);
const checkGroupChange = (data: any) => {
  emits('change', data);
};
const changeHandler = (key: string | string[], val: string | string[]) => {
  if (Array.isArray(key)) {
    props.filterValue[key[0]] = val ? val[0] : null;
    props.filterValue[key[1]] = val ? val[1] : null;
  } else {
    props.filterValue[key] = val;
  }
  checkGroupChange({ ...props.filterValue });
};
</script>

<style scoped lang="less">
// todo
</style>
