<template>
  <div class="archivalInformation">
    <div :class="show ? 'top' : 'topEdit'">
      备案信息<span v-if="!show" class="hr">/</span
      ><span v-if="!show" class="perfectMsg">完善信息</span>
    </div>

    <!-- 备案信息展示状态 -->
    <div v-if="show" class="content">
      <!-- 备案状态 -->
      <div class="title">
        <div class="titleLeft">
          备案状态：<span>{{ getRecord }}</span>
        </div>
        <div class="right-box">
          <div class="titleRight" @click="goBack">返回首页</div>
          <div
            v-if="form.status !== 2 && form.status !== 3"
            class="titleRight"
            @click="perfectMsg()"
          >
            完善信息
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="basicMsg">
        <div class="msgTitle">
          <img
            src="@/assets/imgs/indexHeader/msgShow.png"
            alt=""
            class="msgShowImg"
          />
          基本信息
        </div>
        <el-descriptions class="margin-top basic-msg" :column="3" border>
          <el-descriptions-item label="姓名：">
            {{ userName }}</el-descriptions-item
          >
          <el-descriptions-item label="联系方式：">
            {{ form.phone }}</el-descriptions-item
          >
          <el-descriptions-item label="职称：">
            {{ getTitle }}</el-descriptions-item
          >
          <el-descriptions-item label="性别：">
            {{
              !form.gender ? '--' : form.gender == 1 ? '男' : '女'
            }}</el-descriptions-item
          >
          <el-descriptions-item label="互联网科室：">
            {{ getAdministrative }}</el-descriptions-item
          >
          <el-descriptions-item label="医疗机构名称：">
            {{ form.medicalInstitution }}</el-descriptions-item
          >
          <el-descriptions-item label="身份证号：">
            {{ form.idCard }}</el-descriptions-item
          >
          <el-descriptions-item label="临床工作年限（年）：">
            {{ form.clinicalYears }}</el-descriptions-item
          >
          <el-descriptions-item label="医疗机构编码：">
            {{ form.medicalInstitutionCode }}</el-descriptions-item
          >
        </el-descriptions>
      </div>

      <!-- 资格证信息 -->
      <div class="basicMsg">
        <div class="msgTitle">
          <img
            src="@/assets/imgs/indexHeader/msgShow.png"
            alt=""
            class="msgShowImg"
          />资格证信息
        </div>
        <el-descriptions class="margin-top basic-msg" :column="3" border>
          <el-descriptions-item label="医师资格证号：">
            {{ form.qualificationCertificateNo }}</el-descriptions-item
          >
          <el-descriptions-item label="证书编码：">
            {{ form.certificateNo }}</el-descriptions-item
          >
          <el-descriptions-item label="专业：">
            {{ form.major }}</el-descriptions-item
          >
          <el-descriptions-item label="发证机关：">
            {{ form.issuingAuthority }}</el-descriptions-item
          >
          <el-descriptions-item label="学历：">
            {{ getEducation }}</el-descriptions-item
          >
          <el-descriptions-item label="发证日期：">
            {{
              form.issuingTime ? formatTime(form.issuingTime) : ''
            }}</el-descriptions-item
          >
          <el-descriptions-item label="签发人：">
            {{ form.issuer }}</el-descriptions-item
          >
          <el-descriptions-item label="毕业院校：">
            {{ form.graduationSchool }}</el-descriptions-item
          >
          <el-descriptions-item label="" />
        </el-descriptions>
      </div>

      <!-- 执业证信息 -->
      <div class="basicMsg">
        <div class="msgTitle">
          <img
            src="@/assets/imgs/indexHeader/msgShow.png"
            alt=""
            class="msgShowImg"
          />执业证信息
        </div>
        <el-descriptions class="margin-top basic-msg" :column="3" border>
          <el-descriptions-item label="医生执业证号：">
            {{ form.doctorLicenseNo }}</el-descriptions-item
          >
          <el-descriptions-item label="发证日期：">
            {{
              form.licenseNoTime ? formatTime(form.licenseNoTime) : ''
            }}</el-descriptions-item
          >
          <el-descriptions-item label="发证机关：">
            {{ form.licenseAuthority }}</el-descriptions-item
          >
          <el-descriptions-item label="证书编号：">
            {{ form.practicingCertificateNo }}</el-descriptions-item
          >
          <el-descriptions-item label="签发人：">
            {{ form.certificateIssuer }}</el-descriptions-item
          >
          <el-descriptions-item label="" />
        </el-descriptions>
      </div>

      <!-- 账号信息 -->
      <div class="basicMsg">
        <div class="msgTitle">
          <img
            src="@/assets/imgs/indexHeader/msgShow.png"
            alt=""
            class="msgShowImg"
          />账号信息
        </div>
        <el-descriptions class="margin-top basic-msg" :column="3" border>
          <el-descriptions-item label="是否有三医账号：">
            {{ form.medicalAccount == 1 ? '是' : '否' }}</el-descriptions-item
          >
          <el-descriptions-item label="安全保护问题：">
            {{ getSecurityQuestion }}</el-descriptions-item
          >
          <el-descriptions-item label="监管密码：">
            {{ form.password }}</el-descriptions-item
          >
          <el-descriptions-item label="邮箱：">
            {{ form.mailbox }}</el-descriptions-item
          >
          <el-descriptions-item label="安全保护答案：">
            {{ form.securityAnswer }}</el-descriptions-item
          >
          <el-descriptions-item label="" />
        </el-descriptions>
      </div>
    </div>

    <!-- 备案信息编辑状态 -->
    <div v-else class="content">
      <!-- 基本信息 -->
      <div class="baseMsgEdit">
        <div class="titleEdit">
          <img
            src="@/assets/imgs/indexHeader/msgEdit.png"
            alt=""
            class="msgShowImg"
          />基本信息
        </div>
        <div class="contentEdit">
          <div class="itemContent">
            <div class="conTitle">姓名</div>
            <div class="conContent">{{ userName }}</div>
          </div>
          <div class="itemContent">
            <div class="conTitle">联系方式</div>
            <div class="contentRight">
              <el-input
                v-model="form.phone"
                maxlength="11"
                show-word-limit
                placeholder="请输入联系方式"
                class="contentInput"
                @input="inputChange('phone')"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">职称</div>
            <div class="contentRight">
              <el-select
                v-model="form.title"
                placeholder="请选择职称"
                :style="{ width: '284px' }"
                class="contentSelect"
              >
                <el-option
                  v-for="item in optionsTitle"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">性别</div>
            <div class="contentRadio">
              <div class="radioItem" @click="changeGender(1)">
                <div :class="form.gender === 1 ? 'iconActive' : 'icon'"></div>
                男
              </div>
              <div class="radioItem" @click="changeGender(2)">
                <div :class="form.gender === 2 ? 'iconActive' : 'icon'"></div>
                女
              </div>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">互联网科室</div>
            <div class="contentRight">
              <el-select
                v-model="form.internetDepartmentId"
                filterable
                placeholder="请选择互联网科室"
                :style="{ width: '284px' }"
                class="contentSelect"
              >
                <el-option
                  v-for="item in optionsHospitals"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">医疗机构名称</div>
            <div class="conContent">{{ form.medicalInstitution }}</div>
          </div>
          <div class="itemContent">
            <div class="conTitle">身份证号</div>
            <div class="contentRight">
              <el-input
                v-model="form.idCard"
                maxlength="18"
                show-word-limit
                placeholder="请输入身份证号"
                class="contentInput"
                @input="inputChange('idCard')"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">临床工作年限（年）</div>
            <div class="contentRight">
              <el-input
                v-model="form.clinicalYears"
                maxlength="2"
                show-word-limit
                placeholder="请输入临床工作年限（年）"
                class="contentInput"
                @input="inputChange('clinicalYears')"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">医疗机构编码</div>
            <div class="conContent">{{ form.medicalInstitutionCode }}</div>
          </div>
        </div>
      </div>

      <!-- 资格证信息 -->
      <div class="baseMsgEdit mt56">
        <div class="titleEdit">
          <img
            src="@/assets/imgs/indexHeader/msgEdit.png"
            alt=""
            class="msgShowImg"
          />资格证信息
        </div>
        <div class="contentEdit">
          <div class="itemContent">
            <div class="conTitle">医师资格证号</div>
            <div class="contentRight">
              <el-input
                v-model="form.qualificationCertificateNo"
                maxlength="40"
                show-word-limit
                placeholder="请输入医师资格证号"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">证书编号</div>
            <div class="contentRight">
              <el-input
                v-model="form.certificateNo"
                maxlength="40"
                show-word-limit
                placeholder="请输入证书编号"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">专业</div>
            <div class="contentRight">
              <el-input
                v-model="form.major"
                placeholder="请输入专业"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">发证机关</div>
            <div class="contentRight">
              <el-input
                v-model="form.issuingAuthority"
                placeholder="请输入发证机关"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">学历</div>
            <div class="contentRight">
              <el-select
                v-model="form.education"
                placeholder="请选择学历"
                :style="{ width: '284px' }"
                class="contentSelect"
              >
                <el-option
                  v-for="item in educationArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">发证日期</div>
            <div class="contentRight">
              <el-date-picker
                v-model="form.issuingTime"
                type="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择发证日期"
                class="contentPicker"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">签发人</div>
            <div class="contentRight">
              <el-input
                v-model="form.issuer"
                placeholder="请输入签发人"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">毕业院校</div>
            <div class="contentRight">
              <el-input
                v-model="form.graduationSchool"
                placeholder="请输入毕业院校"
                class="contentInput"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 执业证信息 -->
      <div class="baseMsgEdit mt56">
        <div class="titleEdit">
          <img
            src="@/assets/imgs/indexHeader/msgEdit.png"
            alt=""
            class="msgShowImg"
          />执业证信息
        </div>
        <div class="contentEdit">
          <div class="itemContent">
            <div class="conTitle">医生执业证号</div>
            <div class="contentRight">
              <el-input
                v-model="form.doctorLicenseNo"
                maxlength="40"
                show-word-limit
                placeholder="请输入医生执业证号"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">发证日期</div>
            <div class="contentRight">
              <el-date-picker
                v-model="form.licenseNoTime"
                type="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择发证日期"
                class="contentPicker"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">发证机关</div>
            <div class="contentRight">
              <el-input
                v-model="form.licenseAuthority"
                placeholder="请输入发证机关"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">证书编号</div>
            <div class="contentRight">
              <el-input
                v-model="form.practicingCertificateNo"
                maxlength="40"
                show-word-limit
                placeholder="请输入证书编号"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">签发人</div>
            <div class="contentRight">
              <el-input
                v-model="form.certificateIssuer"
                placeholder="请输入签发人"
                class="contentInput"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 账号信息 -->
      <div class="baseMsgEdit mt56">
        <div class="titleEdit">
          <img
            src="@/assets/imgs/indexHeader/msgEdit.png"
            alt=""
            class="msgShowImg"
          />账号信息
        </div>
        <div class="contentEdit">
          <div class="itemContent">
            <div class="conTitle">是否有三医账号</div>
            <div class="contentRadio">
              <div class="radioItem" @click="changeAccount(1)">
                <div
                  :class="form.medicalAccount == 1 ? 'iconActive' : 'icon'"
                ></div>
                是
              </div>
              <div class="radioItem" @click="changeAccount(2)">
                <div
                  :class="form.medicalAccount == 2 ? 'iconActive' : 'icon'"
                ></div>
                否
              </div>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">安全保护问题</div>
            <div class="contentRight">
              <el-select
                v-model="form.securityQuestion"
                placeholder="请选择安全保护问题"
                :style="{ width: '284px' }"
                class="contentSelect"
              >
                <el-option
                  v-for="item in optionsQuestion"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">监管密码</div>
            <div class="contentRight">
              <el-input
                v-model="form.password"
                placeholder="请输入监管密码"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">邮箱</div>
            <div class="contentRight">
              <el-input
                v-model="form.mailbox"
                type="email"
                placeholder="请输入邮箱"
                class="contentInput"
              />
            </div>
          </div>
          <div class="itemContent">
            <div class="conTitle">安全保护答案</div>
            <div class="contentRight">
              <el-input
                v-model="form.securityAnswer"
                placeholder="请输入安全保护答案"
                class="contentInput"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="bottomBtn">
        <div class="cancel" @click="cancel">取消</div>
        <div class="submit" @click="submit">提交</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { formatTime } from '@/utils/index';
import {
  addPhysician,
  queryInternetHospital,
  queryPhysicianDetails,
} from '@/api/login';
import useUserStore from '@/store/module/useUserStore';

onMounted(() => {
  getHospitals();
  getInfo();
});

// 返回首页
const emit = defineEmits(['goIndex']);
let goBack = () => {
  emit('goIndex');
};
// 完善信息
let perfectMsg = () => {
  show.value = false;
};

// 取消
let cancel = () => {
  queryPhysician();
  show.value = true;
};

// 查询备案信息详情
let getInfo = () => {
  queryPhysicianDetails().then((res: any) => {
    if (res.data.major) {
      form.value = res.data;
      form.value.medicalInstitution = '成都双流哈瑞特互联网医院';
      form.value.medicalInstitutionCode = '91510100MA6914TX7R';
    }
  });
};

// 提醒消息
let getPrompt = (msg: string, style = '') => {
  let obj = {
    showClose: true,
    message: msg || '请检查是否完成！',
    type: style || 'warning',
  };
  let newObj = obj as any;
  ElMessage(newObj);
};

//判断手机号码
let validatorPhone = (val: string) => {
  return new RegExp('^1[3456789]\\d{9}$').test(val);
};

//判断身份证号码
let validatorId = (val: string) => {
  return /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
    val
  );
};

//判断邮箱号码
let validatorEmail = (val: string) => {
  return /^[a-zA-Z0-9]+([-_.][A-Za-zd]+)*@([a-zA-Z0-9]+[-.])+[A-Za-zd]{2,5}$/.test(
    val
  );
};

let form = ref({
  phone: '***********',
  name: '',
  title: 1,
  gender: 1,
  internetDepartmentId: '',
  medicalInstitution: '成都双流哈瑞特互联网医院',
  idCard: '',
  clinicalYears: '',
  medicalInstitutionCode: '91510100MA6914TX7R',
  qualificationCertificateNo: '',
  certificateNo: '',
  major: '',
  issuingAuthority: '',
  education: '',
  issuingTime: '',
  issuer: '',
  graduationSchool: '',
  doctorLicenseNo: '',
  licenseNoTime: '',
  medicalAccount: 1,
  licenseAuthority: '',
  practicingCertificateNo: '',
  certificateIssuer: '',
  securityQuestion: '',
  password: '',
  mailbox: '',
  securityAnswer: '',
  status: 0,
});

const userStore = useUserStore();
// 是否完善信息 true-查看状态  false-编辑状态
let show = ref<boolean>(true);

// 获取用户名
let userName = computed(() => userStore.userName) ?? '';

// 提交
let submit = () => {
  if (!form.value.phone) {
    getPrompt('请输入联系方式！');
  } else if (!validatorPhone(form.value.phone)) {
    getPrompt('请输入正确的手机号码！');
  } else if (!form.value.title) {
    getPrompt('请选择职称！');
  } else if (!form.value.internetDepartmentId) {
    getPrompt('请选择互联网科室！');
  } else if (!form.value.idCard) {
    getPrompt('请输入身份证号！');
  } else if (!validatorId(form.value.idCard)) {
    getPrompt('身份证号码格式不正确！');
  } else if (!form.value.clinicalYears) {
    getPrompt('请输入临床工作年限！');
  } else if (!form.value.medicalInstitutionCode) {
    getPrompt('请输入医疗机构编码！');
  } else if (!form.value.qualificationCertificateNo) {
    getPrompt('请输入医师资格证号！');
  } else if (!form.value.certificateNo) {
    getPrompt('请输入资格证证书编号！');
  } else if (!form.value.major) {
    getPrompt('请输入专业！');
  } else if (!form.value.issuingAuthority) {
    getPrompt('请输入资格证发证机关！');
  } else if (!form.value.education) {
    getPrompt('请选择学历！');
  } else if (!form.value.issuingTime) {
    getPrompt('请选择资格证发证日期！');
  } else if (!form.value.issuer) {
    getPrompt('请输入资格证签发人！');
  } else if (!form.value.graduationSchool) {
    getPrompt('请输入毕业院校！');
  } else if (!form.value.doctorLicenseNo) {
    getPrompt('请输入医生执业证号！');
  } else if (!form.value.licenseNoTime) {
    getPrompt('请选择执业证发证日期！');
  } else if (!form.value.licenseAuthority) {
    getPrompt('请输入执业证发证机关！');
  } else if (!form.value.practicingCertificateNo) {
    getPrompt('请输入执业证证书编号！');
  } else if (!form.value.certificateIssuer) {
    getPrompt('请输入执业证签发人！');
  } else if (!form.value.securityQuestion) {
    getPrompt('请选择安全保护问题！');
  } else if (!form.value.password) {
    getPrompt('请输入监管密码！');
  } else if (!form.value.mailbox) {
    getPrompt('请输入邮箱！');
  } else if (!validatorEmail(form.value.mailbox)) {
    getPrompt('请输入正确的邮箱格式！');
  } else if (!form.value.securityAnswer) {
    getPrompt('请输入安全保护答案！');
  } else {
    addOrUpdateMsg();
  }
};

// 添加或修改医师备案信息
let addOrUpdateMsg = () => {
  // form.value.issuingTime = formatTime(form.value.issuingTime);
  // form.value.licenseNoTime = formatTime(form.value.licenseNoTime);
  form.value.name = userName.value ?? '';

  let params = JSON.parse(JSON.stringify(form.value));
  console.log(params, 'params');
  addPhysician(params)
    .then((res: any) => {
      if (res.code == 'E000000') {
        show.value = true;
        getPrompt('操作成功！', 'success');
        getInfo();
      }
    })
    .catch(err => {
      getPrompt(err.msg);
    });
};

// 查询医助是否完善互联网医师备案
let queryPhysician = () => {
  console.log('获取数据');

  //   axios({
  //     url: '/physician/whether/register',
  //   })
  //     .then(res => {
  //       if (res.code == '**********') {
  //         if (res.data) {
  //           this.getInfo();
  //         }
  //       }
  //     })
  //     .catch(() => {});
};

// 获取职称
let optionsTitle = ref([
  {
    label: '主任医生',
    value: 1,
  },
  {
    label: '副主任医师',
    value: 2,
  },
  {
    label: '主治医生',
    value: 3,
  },
  {
    label: '医师',
    value: 4,
  },
  {
    label: '医士',
    value: 5,
  },
]);
let getTitle = computed(() => {
  let obj = '';
  optionsTitle.value.forEach(item => {
    if (item.value === form.value.title) {
      obj = item.label;
    }
  });
  return obj;
});

// 获取备案状态
let getRecord = computed(() => {
  let objName: string;
  if (form.value.status === 1) {
    objName = '待处理';
  } else if (form.value.status === 2) {
    objName = '备案中';
  } else if (form.value.status === 3) {
    objName = '已备案';
  } else {
    objName = '未备案';
  }
  return objName;
});

// 获取安全问题
let optionsQuestion = ref([
  {
    label: '我的爸爸名字是？',
    value: 1,
  },
  {
    label: '我的妈妈名字是？',
    value: 2,
  },
  {
    label: '我爸爸的生日是？',
    value: 3,
  },
  {
    label: '我妈妈的生日是？',
    value: 4,
  },
  {
    label: '我妻子的名字是？',
    value: 5,
  },
  {
    label: '我丈夫的名字是？',
    value: 6,
  },
  {
    label: '我的出生地是？',
    value: 7,
  },
]);
let getSecurityQuestion = computed(() => {
  let objName = '';
  optionsQuestion.value.forEach(item => {
    if (item.value === Number(form.value.securityQuestion)) {
      objName = item.label;
    }
  });
  return objName;
});

// 获取学历
let educationArr = ref([
  {
    label: '博士',
    value: 1,
  },
  {
    label: '硕士',
    value: 2,
  },
  {
    label: '研究生',
    value: 3,
  },
  {
    label: '大学本科',
    value: 4,
  },
  {
    label: '专科',
    value: 5,
  },
  {
    label: '大专以下',
    value: 6,
  },
]);
let getEducation = computed(() => {
  let objName = '';
  educationArr.value.forEach(item => {
    if (item.value === Number(form.value.education)) {
      objName = item.label;
    }
  });
  return objName;
});

// 获取互联网科室
let getAdministrative = computed(() => {
  let objName = '';
  optionsHospitals.value.forEach(item => {
    if (item.value === form.value.internetDepartmentId) {
      objName = item.label;
    }
  });
  return objName;
});

// 限制输入框输入
let inputChange = (val: string) => {
  if (val === 'phone') {
    form.value.phone = form.value.phone.replace(/[^0-9.]/g, '');
  } else if (val === 'clinicalYears') {
    form.value.clinicalYears = Number(
      form.value.clinicalYears.replace(/[^0-9.]/g, '')
    );
  } else if (val === 'idCard') {
    form.value.idCard = form.value.idCard.replace(/[\W]/g, '');
  }
};

// 选择性别
let changeGender = (val: number) => {
  form.value.gender = val;
};

// 查询所有备案互联网医院
interface HospitalsInfo {
  value: string;
  label: string;
}
let optionsHospitals = ref<HospitalsInfo[]>([]);
let getHospitals = () => {
  queryInternetHospital()
    .then((res: any) => {
      if (res.code === 'E000000') {
        res.data.forEach((item: { internetDepartmentId: any; name: any }) => {
          optionsHospitals.value.push({
            value: item.internetDepartmentId,
            label: item.name,
          });
        });
      }
    })
    .catch(() => {});
};

// 选择是否有三医账号
let changeAccount = (val: number) => {
  form.value.medicalAccount = val;
};
</script>
<style lang="less" scoped>
.archivalInformation {
  padding: 16px;
  text-align: left;
  box-sizing: border-box;
  .top {
    font-size: 14px;
    font-weight: 600;
    color: #2c89dc;
  }
  .topEdit {
    font-size: 14px;
    color: #111111;
    .hr {
      margin: 0 5px;
    }
    .perfectMsg {
      font-size: 14px;
      font-weight: 600;
      color: #2c89dc;
    }
  }
  .content {
    width: 100%;
    height: 800px;
    background: #fff;
    padding: 24px;
    box-sizing: border-box;
    margin-top: 16px;
    overflow-y: scroll;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .titleLeft {
        font-size: 16px;
        color: #111111;
        span {
          font-size: 16px;
          color: #e83333;
        }
      }
      .right-box {
        display: flex;
      }
      .titleRight {
        width: 96px;
        height: 40px;
        background: #2c89dc;
        border-radius: 2px;
        font-size: 16px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-left: 16px;
      }
    }

    // 底部操作按钮
    .bottomBtn {
      margin-top: 56px;
      display: flex;
      justify-content: flex-end;
      .cancel {
        width: 96px;
        height: 40px;
        border-radius: 2px;
        border: 1px solid #2c89dc;
        box-sizing: border-box;
        font-size: 16px;
        color: #2c89dc;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .submit {
        width: 96px;
        height: 40px;
        background: #2c89dc;
        border-radius: 2px;
        font-size: 16px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 16px;
        cursor: pointer;
      }
    }

    // 信息编辑
    .baseMsgEdit {
      .titleEdit {
        font-size: 16px;
        font-weight: bold;
        color: #111111;
        display: flex;
        align-items: center;
        .msgShowImg {
          margin-right: 6px;
          height: 12px;
        }
      }
      .contentEdit {
        margin-top: 32px;
        display: flex;
        flex-wrap: wrap;
        .itemContent {
          display: flex;
          margin-bottom: 28px;
          align-items: center;
          width: 33%;
          .conTitle {
            width: 130px;
            text-align: right;
            font-size: 14px;
            color: #111111;
          }
          .conContent {
            font-size: 14px;
            color: #999999;
            margin-left: 28px;
          }
          .contentRight {
            margin-left: 16px;
            :deep(.el-input__wrapper) {
              width: 284px;
            }
          }
          .contentRadio {
            display: flex;
            .radioItem {
              margin-left: 18px;
              display: flex;
              align-items: center;
              font-size: 14px;
              color: #111111;
              cursor: pointer;
              .iconActive {
                width: 10px;
                height: 10px;
                background: #2c89dc;
                border-radius: 50%;
                margin-right: 7px;
              }
              .icon {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 7px;
                border: 1px solid #979797;
              }
            }
          }
          :deep(.contentPicker) {
            width: 284px;
            .el-input__suffix {
              right: -50px;
              top: 2px;
              .el-input__icon {
                line-height: 32px;
              }
            }
          }
        }
      }
    }
    .mt56 {
      margin-top: 56px;
    }

    // 基本信息查看
    .basicMsg {
      margin-top: 24px;
      .msgTitle {
        font-size: 16px;
        font-weight: 700;
        color: #111111;
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .msgShowImg {
          margin-right: 6px;
        }
      }
      :deep(.basic-msg) {
        .el-descriptions__header {
          margin-bottom: 16px;
        }
        .el-descriptions__body {
          .el-descriptions__label {
            font-size: 14px;
            color: #666666;
            width: 200px;
          }
          .el-descriptions__content {
            font-size: 14px;
            color: #111111;
            width: 350px;
          }
        }
      }
    }
  }
}

/*修改滚动条样式*/
.content::-webkit-scrollbar {
  width: 0;
  height: 0;
  /**/
}

.content::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

.content::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 0;
}
</style>
