<template>
  <div class="w-full relative wrapper">
    <Toolbar
      :data="toolbarData"
      :current="current"
      :types="types"
      :height="toolbarHeight"
      @click-item="clickToolbarItem"
    >
      <template #content="{ data }">
        <slot name="header" :data="data"></slot>
      </template>
    </Toolbar>
    <el-scrollbar ref="scrollbarRef" always>
      <div ref="wrapRef" class="content">
        <div ref="lineRef" class="line"></div>
        <div v-for="v in list" :key="v.type" :class="v.type">
          <template v-for="(item, index) in v.data" :key="item.timestamp">
            <div
              class="item"
              :class="[
                disabledHandler(item) ? 'disabled' : '',
                showItem(item) ? '' : 'hidden',
                item.merged ? 'merged' : '',
              ]"
              :style="{ width: cardWidth }"
              @click="() => clickItem(item)"
            >
              <div
                class="text"
                :style="{ background: colorMap[item.data.type ?? 4]?.[0] }"
              >
                <slot
                  :scope="{ data: exposeData(item), index, type: v }"
                ></slot>
                <span
                  v-if="customStatus || disabledHandler(item)"
                  class="status inline-flex items-center"
                >
                  <slot
                    :scope="{ data: exposeData(item), index, type: v }"
                    name="status"
                    >(未开始)</slot
                  >
                </span>
              </div>
              <div class="date">
                {{ exposeData(item).date }}
              </div>
              <div
                class="outer"
                :style="{ borderColor: colorMap[item.data.type ?? 4]?.[0] }"
              ></div>
              <div
                class="inner"
                :style="{ borderColor: colorMap[item.data.type ?? 4]?.[1] }"
              ></div>
              <div
                class="link"
                :style="{ borderColor: colorMap[item.data.type ?? 4]?.[0] }"
              ></div>
            </div>
          </template>
          <div class="w-1 ml-16 relative shrink-0"></div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { isEmpty } from 'lodash-es';
import Toolbar from './toolbar.vue';
import {
  unitedDuration,
  processData,
  getToolbarData,
  getCurMonth,
  prettierData,
} from './utils';

interface IDataItem {
  time: number | string;
  data: Record<string, any>;
}

interface IProps {
  topData?: IDataItem[];
  bottomData?: IDataItem[];
  /** 是否自定义状态显示 */
  customStatus?: boolean;
  /** 自定义是否当前项是否禁用 */
  customDisabled?: (data: any) => boolean;
  customStatusColor?: (data: any) => string;
  /** top 标题 */
  topTitle: string;
  /** bottom 标题  */
  bottomTitle: string;
  /** 类型对应颜色  当前默认类型 0， 1， 2， 3， default  */
  colorMap?: Record<number, string[]>;
  /** 需要展示的类型  */
  types?: number[];
  toolbarHeight?: string;
  cardWidth?: string;
}
const emits = defineEmits(['clickItem']);
const originTopData = ref<any>([]);
const originBottomData = ref<any>([]);
const scrollbarRef = shallowRef();
const toolbarData = ref<any[]>([]);
const current = ref(getCurMonth());
const list = ref<any>([
  {
    type: 'top',
    data: [],
  },
  {
    type: 'bottom',
    data: [],
  },
]);
const wrapRef = shallowRef();
const lineRef = shallowRef();

const props = withDefaults(defineProps<IProps>(), {
  topData: () => [],
  bottomData: () => [],
  showBackground: false,
  toolbarHeight: '100px',
  cardWidth: '90px',
  types: () => [],
  colorMap: () => ({
    0: ['#7A2EE5', '#C39FF5'], // 住院
    1: ['#E37221', '#F2BE99'], // 门诊
    2: ['#7A2EE5', '#C39FF5'], // 入组
    3: ['#2FB324', '#98E092'], // 复查
    4: ['#2E6BE6', '#9FBCF5'],
  }),
});

const clickToolbarItem = item => {
  current.value = item.date;
  getMonthDataList();
};
const disabledHandler = (item: any) => {
  const { customStatus, customDisabled } = props;
  if (customStatus && customDisabled) {
    const curItem = exposeData(item);
    return customDisabled?.(curItem.data);
  }
  return isFutureDate(item);
};
const exposeData = (item: any) => item;

const showItem = (item: any) => {
  return !isEmpty(item.data);
};
const isFutureDate = (item: any) => {
  return new Date(item.date).getTime() > Date.now();
};
const clickItem = (item: any) => {
  emits('clickItem', exposeData(item));
};

const resetLineStyle = () => {
  const scrollWidth = scrollbarRef.value.wrapRef.scrollWidth;
  const lineWidth = scrollWidth - 32;
  lineRef.value.style.width = `${lineWidth}px`;
};

onMounted(() => {
  scrollbarRef.value.wrapRef.style.borderRadius = '2px';
  scrollbarRef.value.wrapRef.style.border = '1px solid #DCDFE6';
  scrollbarRef.value.wrapRef.style.paddingRight = '16px';
  scrollbarRef.value.wrapRef.style.background = '#F7F8FA';
  getMonthDataList();
});
const getMonthDataList = () => {
  if (!toolbarData.value.length) return;
  const top = originTopData.value[current.value].data;
  const bottom = originBottomData.value[current.value].data;
  const [_top, _bottom] = prettierData(top, bottom);
  list.value[0].data = _top;
  list.value[1].data = _bottom;

  requestAnimationFrame(() => {
    // resetLineStyle();
  });
};
watch(
  [() => props.topData, () => props.bottomData],
  val => {
    const [a, b] = unitedDuration(val);
    originTopData.value = processData(a);
    originBottomData.value = processData(b);
    const monthList = getToolbarData(
      originTopData.value,
      originBottomData.value
    );
    toolbarData.value = monthList;
    if (monthList.length && !monthList.find(v => v.date === current.value)) {
      const valid = monthList.findLast(v =>
        v.allData.some(v => !isEmpty(v.data))
      );
      current.value = valid?.date ?? '';
    }
    getMonthDataList();
  },
  { immediate: true, deep: true }
);
</script>
<script lang="ts">
export default {
  name: 'TimeZone',
};
</script>
<style lang="less" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 160px;
}
.top,
.bottom {
  width: 100%;
  height: 80px;
  display: flex;
  border-left: 1px solid transparent;
}
.line {
  height: 1px;
  width: calc(100% - 32px);
  background: #b8becc;
  position: absolute;
  left: 16px;
  top: 50%;
}
.item {
  font-size: 12px;
  user-select: none;
  flex-shrink: 0;
  display: inline-block;
  height: 48px;
  width: 125px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 1px 4px 0px rgba(12, 78, 149, 0.2);
  position: relative;
  margin-left: 8px;
  cursor: pointer;
  &:hover {
    background: #e6eeff;
  }
  &:first-child {
    margin-left: 16px;
  }
  &:last-child {
    margin-right: 16px;
  }
  .outer {
    position: absolute;
    width: 0;
    height: 0;
    border: 5px solid transparent;
    border-radius: 100%;
    left: calc(50% - 5px);
    bottom: -16px;
  }
  .inner {
    position: absolute;
    width: 0;
    height: 0;
    border: 3px solid transparent;
    border-radius: 100%;
    left: calc(50% - 3px);
    bottom: -14px;
  }
  .link {
    position: absolute;
    border: 1px solid #2e6be6;
    height: 6px;
    width: 1px;
    left: calc(50% - 1px);
    bottom: -6px;
  }
  .text {
    background: #ccc;
    padding: 0 0 0 4px;
    color: #fff;
    height: 22px;
    line-height: 22px;
    white-space: nowrap;
    overflow: hidden;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    > span {
      color: #fff;
    }
    .status {
      white-space: nowrap;
      margin-left: 2px;
    }
  }
  .date {
    padding: 2px 0 0 8px;
    color: #7a8599;
  }
}
.top {
  .item {
    top: 22px;
  }
}
.bottom {
  .item {
    top: 10px;
    .outer {
      top: -14px;
    }
    .inner {
      top: -12px;
    }
    .link {
      top: -5px;
    }
  }
}
.merged {
  .outer {
    transform: rotate(-45deg);
    border-top-color: transparent !important;
    border-right-color: transparent !important;
  }
  .inner {
    transform: rotate(-45deg);
    border-top-color: transparent !important;
    border-right-color: transparent !important;
  }
}
.hidden {
  visibility: hidden;
}
</style>
