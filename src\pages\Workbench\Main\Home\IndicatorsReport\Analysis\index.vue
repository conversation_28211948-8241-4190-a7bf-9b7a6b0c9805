<template>
  <Indicator
    :list="analysisList"
    :active-item="actIdxItem"
    @handle-click="item => (actIdxItem = item)"
  />
  <div class="content pt-lg">
    <div v-if="actIdxItem" class="content-header relative p-xs flex-bc">
      <div class="content-header-title flex items-center text-base leading-xl">
        <div>{{ actIdxItem.name }}</div>
        <i-ep-circle-plus
          v-if="newIndicTypes.includes(actIdxItem.templateName || '')"
          class="pl-2xs cursor-pointer text-primary text-lg font-semibold"
          @click="handleAdd"
        />
      </div>
      <div>
        <LinkBtn
          v-if="
            actIdxItem.isError &&
            showDealRiskBtn.includes(actIdxItem.templateName || '')
          "
          class="mr-lg"
        >
          <img class="img-icon" :src="folderIcon" alt="icon" />
          <span @click="handleDealExceptions">批量处理异常</span>
        </LinkBtn>
        <LinkBtn v-if="canSetThreshold" @click="showThresholdDialog = true">
          <img class="w-16 h-16 mr-2" :src="settingIcon" alt="icon" />
          <span>阈值设置</span>
        </LinkBtn>
      </div>
    </div>

    <!-- 下级指标项 -->
    <SubIndicator
      :p-id="Number(actIdxItem?.id) || 0"
      :template-name="actIdxItem?.templateName!"
      :list="analysisSubList"
      :active-item="actSubIdxItem"
      @tab-change="item => (actSubIdxItem = item)"
    />

    <!-- table 图表 类容面板 -->
    <el-tabs
      v-model="indicActiveTab"
      type="card"
      class="custom-tabs indicator-panel pt-sm"
    >
      <el-tab-pane
        v-for="item in indicTabFilterList"
        :key="item.id"
        :name="item.id"
        :label="item.name"
        :lazy="true"
      >
        <div
          v-show="['tablePanel', 'lineChart'].includes(indicActiveTab)"
          class="flex items-center pb-sm"
        >
          <div class="time-filter-title text-sm shrink-0">时间筛选：</div>
          <el-radio-group v-model="timeType">
            <el-radio
              v-for="time in times"
              :key="time.type"
              class="!mr-md"
              :value="time.type"
              @change="customRange = undefined"
            >
              <span
                :class="[
                  time.type === '6' && timeType === '6' ? 'text-primary' : '',
                ]"
              >
                {{
                  displayCustomRange && time.type === '6'
                    ? displayCustomRange
                    : time.name
                }}

                <el-date-picker
                  v-if="time.type === '6'"
                  v-model="customRange"
                  type="daterange"
                  class="radio-el-date-picker"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :disabled-date="disabledDate"
                  value-format="x"
                  format="YYYY-MM-DD"
                  @change="handleDateChange"
                  @calendar-change="handleCalendarChange"
                />
              </span>
            </el-radio>
          </el-radio-group>
        </div>
        <component
          :is="item.component"
          :req-param="reqParam"
          :indic-active-tab="indicActiveTab"
          @handle-action="
            (option: any) =>
              handleTableAction({
                option,
                templateName: actIdxItem.templateName!,
              })
          "
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 新增数据弹框 -->
    <HrtDialog
      v-model="showAddIndicDialog"
      :title="`${actIdxItem?.name}`"
      size="large"
    >
      <AddIndicator
        ref="addIndicRef"
        :config-list="indicConfigMap[actIdxItem?.templateName!]"
        :item-data="currentEditIndicator"
      />
      <div style="padding-left: 30%" class="pb-2xs">
        <el-button
          style="width: 76px"
          type="primary"
          :loading="updateIndicLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
        <el-button style="width: 76px" @click="showAddIndicDialog = false">
          取消
        </el-button>
      </div>
    </HrtDialog>

    <!-- 阈值设置弹框 -->
    <ThresholdSet
      v-model:visible="showThresholdDialog"
      :title="`${thresholdSetTitle}阈值设置`"
      :width="thresholdSetWithMap[thresholdType]"
      :update-common-params="updateThresholdCommonParams"
      @handle-update="getTableList"
    />
  </div>
</template>

<script setup lang="ts">
import settingIcon from '@/assets/icons/setting.svg';
import { HrtDialog } from '@hrt/components';
import folderIcon from '@/assets/icons/folder.svg';
import LinkBtn from '@/pages/Workbench/Main/components/LinkBtn.vue';
import AddIndicator from './AddIndicator.vue';
import ThresholdSet from './ThresholdSet/index.vue';
import SubIndicator from './SubIndicator.vue';
import Indicator from '../Indicator.vue';
import {
  riskIndexMap,
  riskTabTitleMap,
} from '@/pages/Workbench/Right/components/PatientTodo';
import useUpdateIndicator, {
  newIndicTypes,
  IAddIndicatorCommonParams,
  thresholdIndicTypes,
} from './hooks/useUpdateIndicator';
import { thresholdTypeMapping } from './hooks/useThresholdSet';
import { IAnaActItem, IAnaSubIndicItem } from '../utils';
import {
  IHandleActionProps,
  thresholdSetWithMap,
  useContent,
  useTimeSelect,
} from './hooks/index';
import { useHandleData } from '@/hooks';
import {
  getIndexQuery,
  getIndexSubordinateQuery,
  removeIndex,
} from '@/api/indicatorsReport';
import { IApiIndexFromQueryParams } from '@/interface/type';
import store from '@/store';
import bus from '@/lib/bus';

interface IApiIndexFQParams
  extends Omit<IApiIndexFromQueryParams, 'startTime' | 'endTime'> {
  startTime?: string | number;
  endTime?: string | number;
}

const globalData = store.useGlobal();
const userStore = store.useUserStore();
const indicatorInfo = store.useIndicatorInfo();
const tabAction = store.useComponentsTabAction();

const showDealRiskBtn = ref([
  'BLOOD_SUGAR',
  'BLOOD_PRESSURE',
  'HEART_RATE',
  'WEIGHT',
]);

// 与接口返回类型映射转化后的组件type
const thresholdType = computed(
  () => thresholdTypeMapping[actIdxItem.value?.templateName || 'bloodPressure']
);

// 新增指标项所需公共参数
const updateCommonParams = computed<IAddIndicatorCommonParams>(() => ({
  templateName: actIdxItem.value?.templateName || '',
  patientId: Number(globalData.userId),
  roleType: globalData.currentRole,
  uid: userStore.accountId!,
}));

// 阈值调整所需要的公共参数
const updateThresholdCommonParams = computed(() => {
  const { patientId, uid, templateName } = updateCommonParams.value;
  return {
    type: thresholdType.value,
    templateName,
    patientId,
    uid,
  };
});

// 过滤内容面板tab
const indicTabFilterList = computed(() => {
  // 血压，心率
  return [44, 45].includes(Number(actIdxItem.value.id))
    ? indicTab
    : indicTab.slice(0, 2);
});

// 可调整阈值指标
const canSetThreshold = computed(() => {
  const { templateName, checkType } = actIdxItem.value;
  return (
    thresholdIndicTypes.includes(templateName || '') ||
    (templateName === 'INDEX' && checkType === 8)
  );
});

const {
  updateIndicLoading,
  addIndicRef,
  showAddIndicDialog,
  indicConfigMap,
  editorIndicator,
  addIndicatorSubmit,
  currentEditIndicator,
} = useUpdateIndicator();

const {
  times,
  reqParamTime,
  customRange,
  displayCustomRange,
  disabledDate,
  timeType,
  handleDateChange,
  handleCalendarChange,
} = useTimeSelect();

const { indicTab, indicActiveTab, getTableList, showThresholdDialog } =
  useContent();

// 确认新增数据
const handleSubmit = () => {
  addIndicatorSubmit(updateCommonParams.value, () => {
    showAddIndicDialog.value = false;
    indicActiveTab.value = indicTabFilterList.value[0].id;
    reloadData();
  });
};

// 新增指标
const handleAdd = () => {
  currentEditIndicator.value = {};
  addIndicRef.value?.clear();
  showAddIndicDialog.value = true;
};

// 操作面板-table
const handleTableAction = async (params: IHandleActionProps) => {
  const {
    option: { actionType: acType, item },
    templateName,
  } = params;
  switch (acType) {
    case 'edit':
      if (actIdxItem.value.checkType === 10) item.indexValue = item.giu;
      editorIndicator(item, actIdxItem.value.templateName!);
      break;
    case 'delete':
      {
        await useHandleData(
          removeIndex,
          { id: item.id, templateName },
          '是否确认删除？'
        );
        reloadData();
      }
      break;
  }
};

const reloadData = () => {
  getTableList();
  const { templateName, checkType } = actIdxItem.value;
  if (
    canSetThreshold.value ||
    //不展示阈值设置同时进行刷新
    (templateName === 'WEIGHT' && checkType === 46)
  ) {
    getIndexQueryData();
  }
};

function handleDealExceptions() {
  const tName = actIdxItem.value.templateName || '';
  tabAction.setAction({
    componentType: 4,
    name: riskTabTitleMap[riskIndexMap[tName]],
    mode: 'new',
    mainTabCode: 2,
    data: {
      sourceId: globalData.userId,
      sourceType: 2,
      riskType: riskIndexMap[tName],
    },
  });
  bus.emit('open-component-tab');
}

/** 获取一级指标列表 */
const analysisList = ref<IAnaActItem[]>([]);
/** 当前操作指标项Act 一级指标 */
const actIdxItem = ref<IAnaActItem>({} as IAnaActItem);
/** 阈值设置标题 */
const thresholdSetTitle = computed(() => {
  const { checkType, name } = actIdxItem.value;
  if (checkType === 8) return '低密度脂蛋白';
  return name || '';
});
/** 获取二级指标列表 */
const analysisSubList = ref<IAnaSubIndicItem[]>([]);
/** 当前操作指标项Act 二级指标 */
const actSubIdxItem = ref<IAnaSubIndicItem>({} as IAnaSubIndicItem);
// 内容面板请求参数
const reqParam = reactive<IApiIndexFQParams>({} as IApiIndexFQParams);
watch(
  [actIdxItem, actSubIdxItem, reqParamTime],
  ([idxItem, subActItem, timeObj]) => {
    const { checkType, templateName = '' } = idxItem || {};
    if (!checkType) return;
    reqParam.patientId = Number(globalData.userId);
    reqParam.checkType = checkType;
    reqParam.templateName = templateName;
    reqParam.startTime = timeObj?.start;
    reqParam.endTime = timeObj?.end;
    const subActItemId = subActItem.id;
    reqParam.indexType = subActItemId ? Number(subActItemId) : undefined;
  }
);
/** 获取二级指标项 */
const getAnaSubIndicData = async (checkType: number) => {
  const res = await getIndexSubordinateQuery({
    checkType,
    patientId: globalData.userId!,
  });
  if (res?.length) {
    analysisSubList.value = res.map(item => ({
      ...item,
      id: item.indexType,
    })) as IAnaSubIndicItem[];
    actSubIdxItem.value = analysisSubList.value[0] as IAnaSubIndicItem;
  } else {
    analysisSubList.value = [];
    actSubIdxItem.value = {} as IAnaSubIndicItem;
  }
};
watch(actIdxItem, item => {
  indicActiveTab.value = indicTabFilterList.value[0].id;
  getAnaSubIndicData(Number(item.id));
});
/** 获取一级指标项 */
const getIndexQueryData = async () => {
  const res = await getIndexQuery({ patientId: globalData.userId! });
  const responseDTOList = res?.responseDTOList;
  if (responseDTOList?.length) {
    // 是否有指标分析项目风险
    indicatorInfo.hasAnalysisRisk = Boolean(
      responseDTOList.find(item => item.isError)
    );
    analysisList.value = responseDTOList.map(item => ({
      ...item,
      id: item.checkType,
    })) as IAnaActItem[];
    if (!actIdxItem.value?.id) {
      actIdxItem.value = analysisList.value[0] as IAnaActItem;
    } else {
      const idxItem = analysisList.value.find(
        item => item.id === actIdxItem.value.id
      );
      if (idxItem) {
        actIdxItem.value = idxItem;
      }
    }
  } else {
    indicatorInfo.hasAnalysisRisk = false;
  }
};

onMounted(() => {
  getIndexQueryData();
  bus.on('update-analysis-indicator', getIndexQueryData);
});

onBeforeUnmount(() => {
  bus.off('update-analysis-indicator');
});
</script>
<style scoped lang="less">
.content {
  &-header {
    border-bottom: 3px solid #8193a3;
    margin-bottom: 7px;
    &:before,
    &:after {
      content: '';
      position: absolute;
      left: 0;
      width: 100%;
    }
    &:before {
      top: -6px;
      height: 6px;
      background: #f6f8fb;
    }
    &:after {
      bottom: -6px;
      height: 1px;
      background: #8193a3;
    }
    &-title {
      color: #203549;
      font-weight: 500;
    }
  }
  :deep(.check-list) {
    .header {
      display: none;
    }
    .content {
      max-height: 440px;
    }
  }

  .indicator-panel {
    & > :deep(div.el-tabs__header) {
      margin-bottom: 0;
    }
    & > :deep(.el-tabs__content) {
      border: 1px solid #e5e7eb;
      border-top: none;
      padding: var(--spacing-sm) var(--spacing-xs);
    }

    .time-filter-title {
      color: #7a8599;
    }

    :deep(.radio-el-date-picker) {
      i,
      span {
        display: none;
      }
      input {
        cursor: pointer;
        width: 50%;
      }
      opacity: 0;
      width: 100%;
      position: absolute;
      z-index: 1;
      left: 0;
      top: -2px;
      padding: 0;
    }
  }
}
</style>
