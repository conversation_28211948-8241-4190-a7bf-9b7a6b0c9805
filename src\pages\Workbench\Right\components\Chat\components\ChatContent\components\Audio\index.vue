<template>
  <div class="text audiobox" @click.stop="playAudio">
    <div class="text_img">
      <div class="img">
        <img v-if="choosedId === msg.idClient" alt="" :src="playgif" />
        <img v-else alt="" :src="playaudioImg" />
      </div>
    </div>
    <div class="audioTime">{{ Math.ceil((msg.dur ?? 0) / 1000) }}s</div>
    <audio
      v-show="false"
      ref="audioDom"
      :src="url"
      class="text_img"
      controls
    ></audio>
  </div>
</template>

<script setup lang="ts">
import playgif from '@/assets/imgs/chat/playaudio.gif';
import playaudioImg from '@/assets/imgs/chat/icon-playaudio.png';
interface IProps {
  url?: string;
  choosedId?: string;
  msg: any;
}

const audioDom = shallowRef();
const emits = defineEmits(['choosed']);
const props = defineProps<IProps>();
defineOptions({
  name: 'AudioCard',
});

watch(
  () => props.choosedId,
  val => {
    if (val && val !== props.msg.idClient) {
      audioDom.value.pause();
    }
  }
);

onMounted(() => {
  audioDom.value.addEventListener('ended', () => {
    emits('choosed', '');
  });
});
const playAudio = () => {
  let localCustom = '{"audio": "played"}';
  (window as any).nim.updateLocalMsg({
    idClient: props.msg.idClient,
    localCustom: localCustom,
  });
  emits('choosed', props.msg.idClient);
  if (!props.choosedId || props.choosedId !== props.msg.idClient) {
    audioDom.value.play();
  } else {
    audioDom.value.pause();
    emits('choosed', '');
  }
};
</script>

<style scoped lang="less">
.audiobox {
  min-height: 28px;
  font-size: 14px;
  border-radius: 6px;
  box-sizing: border-box;
  border-radius: 4px;
  border-top-right-radius: 0;
  cursor: pointer;
  position: relative;
  .audioTime {
    position: absolute;
    min-width: 50px;
    text-align: right;
    left: 28px;
    top: 5px;
    font-size: 14px;
    font-weight: 400;
    color: #101b25;
  }
  .text_img {
    .img {
      img {
        height: 28px;
        padding: 0 50px 0 0;
      }
    }
  }
}
</style>
