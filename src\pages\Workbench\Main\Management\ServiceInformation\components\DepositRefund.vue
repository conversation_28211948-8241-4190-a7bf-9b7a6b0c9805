<template>
  <div class="item">
    <div class="name">
      <span class="required-icon">*</span>
      付款截图/押金条
    </div>
    <div class="img-box mt-12">
      <UploadImages
        v-model:img-list="info.payPictureList"
        :show-status="false"
      />
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="required-icon">*</span>
      退回设备的实物图
    </div>
    <div class="img-box mt-12">
      <UploadImages
        v-model:img-list="info.returnDevicePictureList"
        :show-status="false"
      />
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="required-icon">*</span>
      退回设备的快递单号
    </div>
    <div class="img-box mt-12">
      <UploadImages
        v-model:img-list="info.returnDeviceExpressNoList"
        :show-status="false"
      />
    </div>
  </div>
  <div class="item">
    <div class="name">
      <template v-if="depositRefundInfo.hasPayObject">
        3、支付对象：
        <span class="fixed-text">
          {{
            info.payObject === 1
              ? '患者缴费'
              : info.payObject === 2
                ? '健康顾问缴费'
                : info.payObject === 3
                  ? '公司账号缴费'
                  : ''
          }}
        </span>
      </template>
      <template v-if="!depositRefundInfo.hasPayObject">
        <span class="index-num">
          3、
          <span class="required-icon">*</span>
          支付对象
        </span>
        <el-form-item prop="payObject">
          <el-select
            v-model="info.payObject"
            placeholder="请选择支付对象"
            :style="{ width: '240px' }"
            :disabled="isDisabled"
          >
            <el-option
              v-for="item in payObjectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="index-num">
        <span class="required-icon">*</span>
        退款原因
      </span>
      <el-form-item prop="returnReason">
        <el-select
          v-model="info.returnReason"
          placeholder="请选择退款原因"
          :style="{ width: '240px' }"
          :disabled="isDisabled"
        >
          <el-option
            v-for="item in depositRefundReasonList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="index-num">
        <span class="required-icon">*</span>
        设备是否损坏:
      </span>
      <el-form-item prop="deviceDamageStatus">
        <el-radio-group
          v-model="info.deviceDamageStatus"
          :disabled="isDisabled"
          @change="changeEquipmentDamaged"
        >
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>
  </div>
  <div class="item">
    <div class="name">
      实退金额：
      <span class="fixed-text">{{ info.refundMoney }}</span>
    </div>
  </div>
  <div class="item">
    <div class="name">
      实缴金额：
      <span class="fixed-text">{{ payPrice }}</span>
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="index-num">
        <span class="required-icon">*</span>
        退款方式:
      </span>
      <el-form-item prop="refundType">
        <el-radio-group
          v-model="info.refundType"
          :disabled="isDisabled"
          @change="emit('chooseRefundType')"
        >
          <el-radio
            v-if="info.payObject != 2 && info.payObject != 3"
            :label="1"
          >
            原路退回
          </el-radio>
          <el-radio :label="2">退回指定账户</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>
    <div v-if="info.refundType === 2" class="blood-content mt-16 ml-24">
      <div class="count-info">收款人姓名：{{ refundCountForm.payeeName }}</div>
      <div class="count-info mt-4">
        收款人账号：{{ refundCountForm.proceedsAccount }}
      </div>
      <div class="count-info mt-4">
        收款人开户行：{{ refundCountForm.bankOfDeposit }}
      </div>
    </div>
  </div>
  <div class="item">
    <div class="name">
      <span class="index-num">备注:</span>
      <el-form-item>
        <el-input
          v-model="info.remark"
          :rows="2"
          maxlength="200"
          show-word-limit
          :disabled="isDisabled"
          type="textarea"
          placeholder="请输入备注"
          :autosize="{ minRows: 2, maxRows: 6 }"
          style="width: 360px"
        />
      </el-form-item>
    </div>
  </div>
</template>
<script setup lang="ts">
import UploadImages from '@/components/UploadImages/index.vue';
import { payObjectList, depositRefundReasonList } from '../hooks';

const props = defineProps({
  depositRefundInfo: {
    type: Object,
    default: () => {},
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  refundCountForm: {
    type: Object,
    default: () => {},
  },
  payPrice: {
    type: String,
    default: '',
  },
});
const info = ref(props.depositRefundInfo);
const payPrice = ref(props.payPrice);

const emit = defineEmits(['chooseRefundType', 'changeEquipmentDamaged']);

const changeEquipmentDamaged = (value: any) => {
  console.log(value, 7898);

  emit('changeEquipmentDamaged', value);
};
</script>
<style scoped lang="less">
.item {
  margin-top: 20px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
  :deep(.name) {
    font-size: 14px;
    color: #303133;
    .required-icon {
      color: red;
      opacity: 0.7;
    }
    .index-num {
      line-height: 40px;
      vertical-align: middle;
      color: #303133;

      .required-icon {
        color: red;
        opacity: 0.7;
      }
    }
    .fixed-text {
      color: #f56c6c;
    }
    .el-form-item {
      margin-bottom: 0;
      margin-left: 10px;

      .el-radio__inner {
        width: 18px;
        height: 18px;
        // 去掉默认的中心填充
        &::after {
          display: none;
          transition: none;
        }
      }
      .el-radio__input.is-checked {
        .el-radio__inner {
          padding: 4px;
          background-color: #409eff;
          background-clip: content-box;
        }
      }
      .el-radio__label {
        color: #323233;
      }
      .el-form-item__content {
        .el-form-item__error {
          width: 300px;
        }
        .el-input__inner {
          font-size: 12px;
          width: 230px;
          &::-webkit-input-placeholder {
            color: #bfbfbf;
            font-size: 12px;
          }
        }
        .el-icon-arrow-up:before {
          content: '';
        }
      }
    }
    span {
      color: #999999;
    }
  }

  .content {
    margin-left: 24px;
    margin-top: 16px;
    display: flex;
  }

  :deep(.blood-content) {
    background: #fafafa;
    border-radius: 4px;
    padding: 12px 14px;
    .required-icon {
      color: red;
      opacity: 0.7;
    }
    .el-form-item__label {
      font-size: 14px;
      color: #323233;
    }
    .el-input__inner {
      color: #303133;
    }
    .blood-item {
      vertical-align: top;
      .imgbox {
        display: inline-block;
        vertical-align: top;
        border-radius: 8px;
        position: relative;
        .el-icon-error {
          position: absolute;
          border-radius: 50%;
          top: -8px;
          right: -8px;
          background-color: #ffffff;
        }
      }
      img {
        border-radius: 8px;
        object-fit: cover;
      }
    }
  }

  :deep(.input-content) {
    .el-form-item {
      margin-bottom: 0;
      display: flex;
      width: 100% !important;
      .el-form-item__content {
        width: 100%;
        .el-textarea {
          .el-textarea__inner {
            background-color: #fafafa;
            border: none;
            height: 93px;
            font-size: 14px;
            color: #303133;
          }
        }
      }
    }
  }
}
</style>
