<template>
  <div :class="[props.position, 'item', 'mt-16']">
    <div
      :class="[
        `item-label` + '-' + props.position,
        'w-100',
        'mr-16',
        'item-label',
      ]"
      :style="{ 'margin-top': props.titleTop + 'px' }"
    >
      <span v-if="props.title">{{ props.title }}：</span>
    </div>
    <div class="flex-1">
      <slot></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  title: string; // 显示title
  titleTop: number; // title距离顶部的高度
  position?: string;
}

const props = withDefaults(defineProps<Props>(), {
  position: 'flex',
});
</script>
<style scoped lang="less">
.item {
  .item-label {
    font-weight: 700;
    font-size: 14px;
    color: #111111;
  }
  .item-label-flex {
    text-align: right;
  }
  .ocr-label-col {
    text-align: left;
  }
}
</style>
