<template>
  <CardWrapper title="风险评估">
    <el-tabs v-model="risk" type="card" class="custom-tabs">
      <el-tab-pane v-for="item in riskTab" :key="item.state" :name="item.type">
        <template #label>
          <el-badge class="risk-badge" is-dot :type="RISK_LEVEL[item.state]" />
          {{ item.name }}
        </template>
      </el-tab-pane>
    </el-tabs>
    <Assessment :loading="loading" :data="assessmentData" />
  </CardWrapper>
</template>

<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import Assessment from './Assessment/index.vue';
import { useRiskAssess, RISK_LEVEL } from './hooks';
defineOptions({ name: 'RiskAssessment' });

const { loading, riskTab, risk, assessmentData } = useRiskAssess();
</script>
<style lang="less" scoped>
.custom-tabs {
  :deep(& > div.el-tabs__header) {
    .el-tabs__item {
      height: 39px;
      line-height: 39px;
      background: #f7f8fa;
      width: 156px;
      border-bottom: 1px solid #e5e7eb;
      &.is-active,
      &:hover {
        color: #323233;
      }
      &.is-active {
        background: #fff;
        border-bottom: none;
      }
    }
  }
}

.risk-badge {
  :deep(sup.el-badge__content.is-dot) {
    top: 0;
    right: 5px;
    width: 10px;
    height: 10px;
  }
}

.chart {
  height: 300px;
}
</style>
