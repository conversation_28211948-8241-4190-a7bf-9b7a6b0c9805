import type { Emitter } from 'mitt';
import mitt from 'mitt';

/** 全局公共事件需要在此处添加类型 */
type Events = {
  // 刷新患者列表
  'refresh-patient-list': undefined;
  // 消息通知
  'notification-message': undefined;
  // 刷新消息相关接口
  'refresh-message': undefined;
  // 清空对应类型消息
  'clear-message': undefined;
  // 清空患者列表过滤条件
  'clear-patient-list-filter': undefined;
  // 关闭修改密码等操作按钮的弹窗
  'close-operation-popup': undefined;
  // 更新待办列表
  'updete-todo-list': undefined;
  // 刷新报告指标 - 指标分析- table
  'refresh-indicators-analysis-table': undefined;
  // 刷新报告指标 - 报告原文- table
  'refresh-indicators-original-table': undefined;
  // 获取通讯录
  'get-address-books': undefined;
  // 打开tab标签页
  'open-component-tab': undefined;
  // 关闭ocr弹窗
  'close-ocr-dialog': undefined;
  // 更新随访时间轴
  'refresh-followup-list': undefined;
  // 更新时间轴数据
  'updata-review-list': undefined;
  //保存ocr信息
  'saved-ocr-info': undefined;
  //刷新附件
  'refresh-attachment': number;
  //根据 id 刷新段落数据
  'refresh-record-data': number;
  // 刷新患者事件信息
  'refresh-patient-info-event': undefined;
  // 获取患者工作室变更信息
  'get-patient-Group': undefined;
  // Ai消息通知刷新数量
  'ai-notification-message-refresh': undefined;
  // 清空 toast 队列里所有通知消息
  'notification-message-clear': undefined;
  // 批量ocr扫描成功更新任务数量
  'batch-ocr-success-refresh': undefined;
  // 撤销任务更新任务事项列表
  'updata-task-refresh': undefined;
  // 更新指标报告-指标分析-指标项
  'update-analysis-indicator': undefined;
  // 更新任务状态
  'update-task-status': undefined;
  // 批量ocr识别成功后审核结果确认后刷新病历数据
  'batch-ocr-success-refresh-data': undefined;
  // 打开临床事件新增弹窗
  'open-clinical-event': undefined;
  // 质疑定位
  'doubt-location': undefined;
};

const emitter: Emitter<Events> = mitt<Events>();

export default emitter;
