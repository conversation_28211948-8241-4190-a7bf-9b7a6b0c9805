<template>
  <div ref="historyDrug" class="flex-1 events-list">
    <div v-if="historyDrugRecord.length === 0" class="no-data">
      暂无用药信息
    </div>
    <div class="time-row">
      <div class="item-list">
        <div
          v-for="(item, index) in historyDrugRecord"
          :key="index"
          class="item"
        >
          <div class="top">
            <div class="flex-c">
              <div class="round">
                <img class="w-16 h-16" src="@/assets/imgs/drug/icon-drug.png" />
              </div>
              <span class="review-num">
                {{ item.userName }}
                {{
                  item.isStop
                    ? item.stopTime
                      ? dayjs(item.stopTime).format('YYYY-MM-DD HH:mm')
                      : '--'
                    : dayjs(item.createTime).format('YYYY-MM-DD  HH:mm')
                }}
                <!--                {{-->
                <!--                  item.clinicalTime-->
                <!--                    ? dayjs(item.clinicalTime).format('YYYY-MM-DD')-->
                <!--                    : '&#45;&#45;'-->
                <!--                }}-->
              </span>
            </div>
          </div>
          <div class="bottom">
            <div class="bottom-main">
              <div class="item-content">
                <div class="drug-info">
                  <div class="flex">
                    <div class="label w-100">开始用药日期：</div>
                    <div class="value flex-1">
                      {{
                        dayjs(item.medicationTime || item.createTime).format(
                          'YYYY-MM-DD'
                        )
                      }}
                    </div>
                  </div>
                  <div class="flex">
                    <div class="label w-100">调整原因：</div>
                    <div class="value flex-1">
                      <span
                        v-for="(ite, index) in item.adjustReason"
                        :key="index"
                      >
                        {{ index + 1 }}.
                        {{
                          ite === '其他'
                            ? `其他:${item.adjustReasonOther}`
                            : ite
                        }}
                        ;
                      </span>
                      <span v-if="item.adjustReason?.length === 0">--</span>
                    </div>
                  </div>
                  <div class="flex">
                    <div class="label w-100">调整方案：</div>
                    <div class="value flex-1">
                      {{
                        item.drugOperation && isJSON(item.drugOperation)
                          ? JSON.parse(item.drugOperation).join('；') || '--'
                          : item.drugOperation || '--'
                      }}
                    </div>
                  </div>
                </div>
                <AListOfMedications
                  :table-data="item.drugList"
                  :show-stop-status="item.isStop"
                  class="history-drug-list"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import AListOfMedications from './AListOfMedications.vue';
import { getDrugPatientHistory } from '@/api/drug';
import { useInfiniteScroll } from '@vueuse/core';
import useGlobal from '@/store/module/useGlobal';

const historyDrug = ref<HTMLElement | null>(null);
useInfiniteScroll(
  historyDrug,
  async () => {
    await getRecord();
  },
  {
    distance: 15,
  }
);
const getRecord = async () => {
  if (historyDrugRecord.value.length < totals.value) {
    ++pageNumber.value;
    getDrugPatientHistorys(pageNumber.value);
  }
};

const historyDrugRecord = ref([]);
const pageNumber = ref(1);
const totals = ref(10);
const getDrugPatientHistorys = pageNumber => {
  const params = {
    patientId: useGlobal().userId,
    pageNumber,
    pageSize: 10,
  };
  getDrugPatientHistory(params).then(res => {
    totals.value = res.totals;
    historyDrugRecord.value =
      pageNumber === 1 ? res.data : historyDrugRecord.value.concat(res.data);
  });
};
//判断是否为json字符串
const isJSON = str => {
  if (typeof str == 'string') {
    try {
      var obj = JSON.parse(str);
      if (typeof obj == 'object' && obj) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      // console.log('error：'+str+'!!!'+e);
      return false;
    }
  }
  // console.log('It is not a string!')
};
onMounted(() => {
  getDrugPatientHistorys(pageNumber.value);
});
watch(
  () => useGlobal().userId,
  () => {
    pageNumber.value = 1;
    getDrugPatientHistorys(pageNumber.value);
  }
);
defineExpose({ getDrugPatientHistorys });
</script>
<script lang="ts">
export default {
  name: 'ClinicalEvents',
};
</script>
<style scoped lang="less">
// todo
.events-list {
  padding: 0 16px;
  height: 100%;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    width: 8px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.no-data {
  font-size: 20px;
  color: #b8becc;
  line-height: 28px;
  text-align: center;
  margin: 24px 0 40px 0;
}
.time-row {
  box-sizing: border-box;
  .item-list {
    margin-top: 16px;
    .item {
      margin-bottom: 8px;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .round {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          margin-right: 16px;
          margin-left: -3px;
        }
        .review-num {
          font-size: 16px;
          color: #101b25;
        }
      }
      .bottom {
        border-left: 1px dashed #0a73e4;
        margin-left: 5px;
        margin-top: 7px;
        padding-bottom: 9px;
        cursor: pointer;
        .bottom-main {
          background: #f7f8fa;
          margin-left: 21px;
          border: 1px solid #f7f8fa;
          box-sizing: border-box;
        }
        .item-content {
          width: 100%;
          min-height: 70px;
          border-radius: 2px;
          padding: 12px;
          box-sizing: border-box;
          font-size: 14px;
          .drug-info {
            line-height: 20px;
            border-bottom: 1px solid #e9e8eb;
            padding-bottom: 12px;
            .label {
              color: #708293;
            }
            .value {
              color: #101b25;
            }
          }
        }
      }
    }
  }
}
.history-drug-list {
  :deep(.head-class) {
    background: #efefef;
    .el-table__cell {
      background-color: #efefef !important;
      padding: 6px 0;
      .cell {
        font-size: 14px;
        font-weight: normal !important;
        color: #708293;
        min-width: 60px;
        height: 20px;
      }
    }
  }
  :deep(.el-table) {
    background: #f7f8fa !important;
    tr {
      background: #f7f8fa !important;
    }
  }
}
</style>
