<template>
  <div v-for="(item, index) in data" :key="index" class="reply-card">
    <div class="title">
      {{ dayjs(item.createTime).format('YYYY年M月D日 HH:mm:ss') }}
    </div>
    <div class="content">
      <span>{{ item.createUserName }}</span>
      <span class="text-[#7A8599]">
        在状态【{{ DoubtStatusMap[item.doubtStatus!] }}】添加
      </span>
      <span>
        {{
          item.replyAction === 'DIRECT_SUBMIT' ? '定向提交数据' : item.remark
        }}
      </span>
    </div>
    <div
      v-if="
        item.replyAction === 'DIRECT_SUBMIT' || !!item.accessoryList?.length
      "
      class="file"
    >
      <span class="mr-12">附件:</span>
      <span
        v-if="item.replyAction === 'DIRECT_SUBMIT'"
        class="w-22 h-18 mr-16 mt-2 cursor-pointer"
        @click="() => getSnapShot(item.replyId)"
      >
        <img title="数据快照" :src="spImg" alt="数据快照" />
      </span>
      <FilePreview :file-list="item.accessoryList ?? []" size="small" />
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { IApiResearchDoubtInfo } from '@/interface/type';
import { DoubtStatusMap } from '@/constant/doubt';
import FilePreview from '@/features/FilePreview/index.vue';
import spImg from '@/assets/icons/snapshot.png';

interface IProps {
  data: IApiResearchDoubtInfo['doubtReplies'];
}
const emit = defineEmits(['showSnapshot']);
defineProps<IProps>();
const getSnapShot = id => {
  emit('showSnapshot', id);
};
defineOptions({
  name: 'ReplyList',
});
</script>

<style scoped lang="less">
.title {
  font-size: 14px;
  color: #7a8599;
}
.content {
  color: #3a4762;
  margin: 8px 0;
}
.file {
  display: flex;
  color: #7a8599;
}
.reply-card {
  padding: 12px 0 16px;
  border-bottom: 1px solid #e1e5ed;
}
</style>
