<template>
  <div v-if="mode === 'view'">{{ viewText || '--' }}</div>
  <div v-else class="duration">
    <el-input-number
      v-model="durationTime.hour"
      :stlye="{ width: '60px' }"
      controls-position="right"
      :min="0"
      :max="999999"
      @change="changeHandler"
    />
    小时
    <el-input-number
      v-model="durationTime.minute"
      controls-position="right"
      :min="0"
      :max="59"
      @change="changeHandler"
    />
    分
    <el-input-number
      v-model="durationTime.second"
      controls-position="right"
      :min="0"
      :max="59"
      @change="changeHandler"
    />
    秒
  </div>
</template>

<script setup lang="ts">
import { Base } from './type';

const durationTime = ref({
  hour: 0,
  minute: 0,
  second: 0,
});
const props = defineProps<Base>();
const emit = defineEmits(['change']);
const changeHandler = () => {
  emit('change', { ...durationTime.value });
};
const viewText = computed(() => {
  const { value } = props;
  if (!value) return '--';
  const { hour: h, minute: m, second: s } = value;
  return `${h}小时${m}分${s}秒`;
});
watch(
  () => props.value,
  val => {
    if (val) {
      durationTime.value = { ...durationTime.value, ...val };
    }
  }
);
defineOptions({
  name: 'Duration',
});
</script>

<style scoped lang="less">
.duration {
  :deep(.el-input-number) {
    width: 100px;
    margin-right: 4px;
  }
}
</style>
