import dayjs from 'dayjs';
import { groupBy, isEmpty, mapValues, sumBy } from 'lodash-es';

export const isSameDay = (a: number, b: number) => dayjs(a).isSame(b, 'day');
export const isNeedFill = (a: number, b: number) => {
  return dayjs(a).isBefore(dayjs(b)) && !isSameDay(a, b);
};
/** 填补缺失日期数据 */
export const fillDataHandler = (list: any) => {
  const res: any = [];
  let temp = null;
  let num = 0;
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    let fillTime = item.time;
    let filldata = item.data;
    if (temp) {
      if (isNeedFill(temp, item.time)) {
        fillTime = dayjs(temp).add(1, 'day').valueOf();
        filldata = {};
        i--;
      } else {
        if (isEmpty(res[res.length - 1].data)) {
          res.pop();
        }
      }
    }
    res.push({ ...formateDate(fillTime), index: num, data: filldata });
    temp = fillTime;
    num++;
  }
  return res;
};
export const formateDate = (time: number) => {
  const date = dayjs(time);
  return {
    timeStamp: date.valueOf(),
    date: date.format('YYYY-MM-DD'),
    shortDate: date.format('MM.DD'),
    yearMonth: date.format('YYYY-MM'),
    label: date.month() + 1 + '月',
  };
};

export const unitedDuration = (val: any) => {
  const [a, b] = val;
  if (!a.length && !b.length) return val;
  if (!a.length) {
    return [
      [
        { time: b[0].time, data: {} },
        { time: b[b.length - 1].time, data: {} },
      ],
      b,
    ];
  }
  if (!b.length) {
    return [
      a,
      [
        { time: a[0].time, data: {} },
        { time: a[a.length - 1].time, data: {} },
      ],
    ];
  }
  const minTime = Math.min(
    dayjs(a[0].time).valueOf(),
    dayjs(b[0].time).valueOf()
  );
  const maxTime = Math.max(
    dayjs(a[a.length - 1].time).valueOf(),
    dayjs(b[b.length - 1].time).valueOf()
  );
  if (isNeedFill(minTime, a[0].time)) {
    a.unshift({ time: minTime, data: {} });
  }
  if (isNeedFill(minTime, b[0].time)) {
    b.unshift({ time: minTime, data: {} });
  }
  if (isNeedFill(a[a.length - 1].time, maxTime)) {
    a.push({ time: maxTime, data: {} });
  }
  if (isNeedFill(b[b.length - 1].time, maxTime)) {
    b.push({ time: maxTime, data: {} });
  }
  return [a, b];
};

export const processData = data => {
  const fullData = fillDataHandler(data);
  const res = {};
  for (const v of fullData) {
    const curMonth = v.yearMonth;
    if (res[curMonth]) {
      res[curMonth].data.push(v);
    } else {
      res[curMonth] = {
        label: v.label,
        data: [v],
      };
    }
  }
  return res;
};

export const getToolbarData = (topData, bottomData) => {
  return Object.keys(bottomData).map(v => ({
    date: v,
    label: bottomData[v].label,
    allData: [...topData[v].data, ...bottomData[v].data],
  }));
};

export const getCurMonth = () => {
  return dayjs().format('YYYY-MM');
};
export const hasValidData = data => {
  return data.length > 1 || !isEmpty(data?.[0].data);
};
export const getFilterData = (data, key) => {
  return data.filter(v => v.date === key) ?? [];
};
let fillIndex = 10000;
export const getEmptyData = (time, num) => {
  const res: any[] = [];
  for (let i = 0; i < num; i++) {
    res.push({ ...formateDate(time), index: fillIndex++, data: {} });
  }
  return res;
};
export const prettierData = (a, b) => {
  const validA = a.filter(v => !isEmpty(v.data));
  const validB = b.filter(v => !isEmpty(v.data));
  const aCountMap = mapValues(groupBy(validA, 'date'), v => v.length);
  const bCountMap = mapValues(groupBy(validB, 'date'), v => v.length);
  const aKeys = validA.map(v => v.date);
  const bKeys = validB.map(v => v.date);
  const allKeys = [...new Set([...aKeys, ...bKeys])];
  const sortKeys = allKeys
    .map(v => Date.parse(v))
    .sort((a, b) => a - b)
    .map(v => dayjs(v).format('YYYY-MM-DD'));
  const _a: any[] = [];
  const _b: any[] = [];
  for (const key of sortKeys) {
    const filterA = getFilterData(validA, key);
    const filterB = getFilterData(validB, key);
    _a.push(...filterA);
    _b.push(...filterB);
    const dis = (aCountMap[key] ?? 0) - (bCountMap[key] ?? 0);
    if (dis > 0) {
      const res = getEmptyData(key, dis);
      _b.push(...res);
    } else if (dis < 0) {
      const res = getEmptyData(key, -dis);
      _a.push(...res);
    }
  }
  for (let i = 0; i < _b.length; i++) {
    if (!isEmpty(_a[i].data) && !isEmpty(_b[i].data)) {
      _b[i].merged = true;
    }
  }
  return [_a, _b];
};
