<template>
  <div class="follow mb-2xs">
    <div class="header flex items-center justify-between">
      <div class="left flex items-center">
        <div class="module-one"></div>
        <div class="module-two ml-10">随访记录</div>
      </div>
      <div class="right mr-16 flex items-center">
        <div class="all-record cursor-pointer" @click="queryAllList">
          全部记录<el-icon size="12px">
            <i-ep-arrow-right color="#2E6BE6" />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="toolbar">
      <div class="item" @click="addFollowUp">
        <img :src="addFollowupImg" /> 自定义随访
      </div>
      <div class="right-item">
        <div class="plan" @click="setPlan">
          <img :src="planImg" />调整随访计划
        </div>
      </div>
    </div>
    <div class="mt-17 time-zone">
      <TimeZone
        :key="globalStore.userId"
        :top-title="'症状随访'"
        :bottom-title="'生活方式随访'"
        custom-status
        :top-data="topData"
        :bottom-data="bottomData"
        :custom-disabled="customDsiableHandler"
        :custom-status-color="customStatusColor"
        :toolbar-height="'60px'"
        card-width="90px"
        @click-item="(data: any) => openFollowTab(data?.data)"
      >
        <template #default="{ scope: { data } }">
          <span>
            <!-- {{ data.data.name }}{{ data.data.followUpId ? '随访' : '方式随访' }} -->
            {{ data.data.name }}
          </span>
        </template>
        <template #status="{ scope: { data } }">
          <span>
            {{ getStatus(data.data.status) }}
          </span>
        </template>
      </TimeZone>
    </div>
    <AddFollowUpModal
      :visible="followupModalVisible"
      @close="followupModalVisible = false"
    />
    <!-- 全部记录 -->
    <Drawer v-model:visible="visible" title="全部随访记录">
      <div class="all-box">
        <el-checkbox-group v-model="checkList" @change="changeHandler">
          <el-checkbox :value="1" label="症状随访" />
          <el-checkbox :value="2" label="生活方式随访" />
        </el-checkbox-group>
        <ul class="list mt-18">
          <li v-for="(item, index) in dataList" :key="index" class="item mb-8">
            <div class="item-top flex items-center justify-between">
              <div class="top-left flex items-center">
                <img :src="addFollowupImg" alt="" class="w-14 h-14 mr-9" />
                {{ getFollowName(item) }}
              </div>
              <div class="top-right">
                {{ item.date ? getTime(item.date) : '--' }}
              </div>
            </div>
            <div
              class="item-bottom ml-24 flex items-center justify-between mt-6"
            >
              <span
                :class="
                  item.status === 0
                    ? 'unstart'
                    : item.status === 1
                      ? 'underway'
                      : item.status === 2
                        ? 'complete'
                        : 'uncomplete'
                "
                >{{ getFollowStatus(item) }}</span
              >
              <div
                v-if="item.status !== 0"
                class="details-box cursor-pointer"
                @click="queryDetails(item)"
              >
                查看详情
              </div>
            </div>
          </li>
        </ul>
      </div>
    </Drawer>
    <PlanStep
      :title="'调整随访计划'"
      :visible="planVisible"
      :types="[2, 3]"
      @close="planVisible = false"
    />
  </div>
</template>
<script setup lang="ts">
import TimeZone from '@/components/TimeZone/index.vue';
import useGlobal from '@/store/module/useGlobal';
import useComponentsTabAction from '@/store/module/useComponentsTabAction';
import bus from '@/lib/bus';
import { getFollowRecords } from '@/api/followup';
import {
  IApiFollowRecordsFollowUpInfo,
  IApiFollowRecordsQuestionnaireInfo,
} from '@/interface/type';
import dayjs from 'dayjs';
import addFollowupImg from '@/assets/imgs/review/followup.png';
import planImg from '@/assets/imgs/review/plan.png';
import AddFollowUpModal from './components/AddFollowUpModal.vue';
import Drawer from '@/components/Drawer/index.vue';
import { getFollowList } from '@/api/followup';
import PlanStep from '@/components/PlanStep/index.vue';

type Item = IApiFollowRecordsFollowUpInfo & IApiFollowRecordsQuestionnaireInfo;
defineOptions({
  name: 'FollowUp',
});

const useGlobalInfo = useGlobal();
const globalStore = useGlobal();
const topData = ref<any[]>([]);
const bottomData = ref<any[]>([]);
const tabAction = useComponentsTabAction();
const followupModalVisible = ref(false);
const visible = ref(false);
const dataList = ref<any>([]);
const checkList = ref([1, 2]);
const planVisible = ref(false);

const flag = ref(true);

// 加载到底部分页加载数据
const queryInfo = ref({
  pageSize: 10,
  page: 1,
  patientId: useGlobalInfo.userId,
  type: [1, 2],
});

const totalPageNumber = ref(0);

const setPlan = () => {
  planVisible.value = true;
};

const changeHandler = () => {
  dataList.value = [];
  getAllDataList();
};
// 查询全部数据
const getAllDataList = () => {
  const params = {
    patientId: useGlobalInfo.userId,
    followTypeList: checkList.value,
  };
  getFollowList(params).then((res: any) => {
    dataList.value = res;
  });
};
const getFollowName = item => {
  const isCustom = item.times === 0;
  const pre = isCustom ? '自定义' : '';
  return item.followId ? `${pre}症状随访` : `${pre}生活方式随访`;
};
// 随访
const getFollowStatus = computed(() => {
  return function (item: any) {
    const { status } = item;
    if (!item.followId) return '';
    let str = '';
    if (status === 0) {
      str = '未开始';
    }
    if (status === 1) {
      str = '进行中';
    }
    if (status === 2) {
      str = '已完成';
    }
    if (status === 3) {
      str = '未完成';
    }
    return str ? `${str}` : '';
  };
});
const getTime = (time: number) => dayjs(time).format('YYYY-MM-DD');
// 查看详情
const queryDetails = item => {
  const data = item.followId
    ? {
        followUpId: item.followId,
        followUpDate: item.date,
      }
    : {
        userQuestionnaireDate: item.date,
      };
  tabAction.setAction({
    componentType: data.userQuestionnaireDate ? 5 : 6,
    name: data.userQuestionnaireDate
      ? `${dayjs(data.userQuestionnaireDate).format('YYYY-MM-DD')} 生活方式随访`
      : `${dayjs(data.followUpDate).format('YYYY-MM-DD')} 症状随访`,
    data: {
      ...data,
      status: item.status,
    },
  });
  bus.emit('open-component-tab');
};
const queryAllList = () => {
  if (!visible.value) {
    dataList.value = [];
    getAllDataList();
  }
  visible.value = true;
};
const addFollowUp = () => {
  followupModalVisible.value = true;
};
const openFollowTab = (item: Item) => {
  if (item.status === 0) return;
  tabAction.setAction({
    componentType: item.userQuestionnaireDate ? 5 : 6,
    name: item.userQuestionnaireDate
      ? `${dayjs(item.userQuestionnaireDate).format('YYYY-MM-DD')} 生活方式随访`
      : `${dayjs(item.followUpDate).format('YYYY-MM-DD')} 症状随访`,
    data: item,
  });
  bus.emit('open-component-tab');
};
const getStatus = (val: number) => {
  switch (val) {
    case 1:
      return '(进行中)';
      break;
    case 2:
      return '(已完成)';
      break;
    case 3:
      return '(未完成)';
      break;
    default:
      return '(未开始)';
      break;
  }
};
const getFollowData = async (patientId: number) => {
  topData.value = [];
  bottomData.value = [];
  const res = await getFollowRecords({ patientId });
  const { followUpInfo = [], questionnaireInfo = [], startDate, endDate } = res;

  topData.value = followUpInfo.map(v => ({
    time: v.followUpDate,
    data: { ...v, name: '症状' },
  }));
  topData.value.unshift({ time: startDate, data: {} });
  topData.value.push({ time: endDate, data: {} });

  bottomData.value = questionnaireInfo.map(v => ({
    time: v.userQuestionnaireDate,
    data: { ...v, name: '生活' },
  }));
  bottomData.value.unshift({ time: startDate, data: {} });
  bottomData.value.push({ time: endDate, data: {} });
};
const refreshFollowData = () => {
  if (globalStore.userId) {
    getFollowData(globalStore.userId);
  }
};
const customStatusColor = (data: any) => {
  if (data?.status === 1) {
    return '#2E6BE6';
  } else if (data?.status === 2) {
    return '#4EC244';
  } else if (data?.status === 3) {
    return '#E63746';
  } else {
    return '';
  }
};
const customDsiableHandler = (data: any) => {
  return [0, 3].includes(data?.status);
};
onMounted(() => {
  bus.on('refresh-followup-list', refreshFollowData);
});

onUnmounted(() => {
  bus.off('refresh-followup-list');
});
watch(
  () => globalStore.userId,
  val => {
    if (val) {
      getFollowData(val);
    }
  },
  {
    immediate: true,
  }
);
</script>
<style scoped lang="less">
.follow {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 6px;
  padding: 16px 0;
  box-sizing: border-box;
  .header {
    .left {
      .module-one {
        width: 6px;
        height: 16px;
        background: #2e6be6;
        border-radius: 2px;
      }
      .module-two {
        font-size: 16px;
        font-weight: 700;
        color: #101b25;
      }
    }
    .right {
      font-size: 14px;
      color: #2e6be6;
    }
  }
}
.time-zone {
  padding: 0 16px;
  box-sizing: border-box;
}
.toolbar {
  margin-top: 24px;
  display: flex;
  padding: 0 16px;
  img {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
  .item {
    display: flex;
    justify-content: center;
    min-width: 112px;
    height: 32px;
    font-size: 14px;
    background: #ffffff;
    border-radius: 2px;
    margin-right: 8px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    align-items: center;
  }
  .right-item {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
    .plan {
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      color: #2e6be6;
    }
  }
}
.all-box {
  padding: 16px;
  box-sizing: border-box;
  .list {
    .item {
      height: 70px;
      background: #f7f8fa;
      border-radius: 4px;
      padding: 12px;
      box-sizing: border-box;
      .item-top {
        .top-left {
          font-size: 14px;
          font-weight: bold;
          color: #3a4762;
        }
        .top-right {
          font-size: 14px;
          color: #7a8599;
        }
      }
      .item-bottom {
        font-size: 14px;

        .complete {
          color: #2fb324;
        }
        .unstart {
          color: #7a8599;
        }
        .underway {
          color: #e37221;
        }
        .uncomplete {
          color: rgb(230, 55, 70);
        }
        .details-box {
          color: #2e6be6;
        }
      }
    }
  }
}
</style>
