<template>
  <div>
    <el-drawer :model-value="reminderVisible" direction="rtl" @close="close">
      <template #header>
        <div class="flex items-center">
          提醒事项
          <span class="count">({{ totalsRemindersNum || 0 }})</span>
        </div>
      </template>
      <template #default>
        <SelectGroup @params-change="paramsChange" />
        <div class="scroll-box" @scroll="handleScroll">
          <div ref="calenderBoxRef" class="px-16">
            <div class="calender-box">
              <Calendar
                v-if="expandCalender"
                v-model:date="checkedDate"
                v-model:init-date="initCalenderMonthDate"
                @change-checked-date="checkedDateChange"
                @get-month-date="getTargetMonthDate"
              />
              <CalenderWeek
                v-if="!expandCalender"
                v-model:date="checkedDate"
                @get-sunday="getWeekSunday"
                @change-checked-date="checkedDateChange"
                @get-month-date="getTargetMonthDate"
              />
              <div class="expand-btn" @click="expandCalender = !expandCalender">
                {{ !expandCalender ? '展开月历' : '收起月历' }}
                <el-icon
                  :size="16"
                  :class="[!expandCalender ? 'expand' : 'shrink']"
                >
                  <i-ep-DArrowLeft />
                </el-icon>
              </div>
            </div>
          </div>
          <div class="calenderWeekSimple-wrapper">
            <CalenderWeekSimple
              v-if="showCalenderWeekSimple"
              v-model:date="checkedDate"
              @get-sunday="getWeekSunday"
              @change-checked-date="checkedDateChange"
              @get-month-date="getTargetMonthDate"
            />
          </div>
          <ReminderList
            ref="reminderListRef"
            @delay-reminder="delayReminder"
            @load-more="loadReminderList"
            @close="close"
          />
          <el-divider v-if="isLoading">
            <el-icon :size="18" class="loading-icon">
              <i-ep-loading />
            </el-icon>
          </el-divider>
          <el-divider v-if="total === reminderListData.length">
            <span class="bottom-divider">没有更多了</span>
          </el-divider>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import useGlobal from '@/store/module/useGlobal';
import { debounce } from 'lodash-es';
import Calendar from './Calender/index.vue';
import CalenderWeek from './CalenderWeek/index.vue';
import CalenderWeekSimple from './CalenderWeekSimple/index.vue';
import ReminderList from './ReminderList/index.vue';
import SelectGroup from './SelectGroup/index.vue';
import dayjs from 'dayjs';
import { useReminder } from '@/store/module/useReminder';
import { getOnlyMineStatus } from '@/api/reminder';

const reminderStore = useReminder();

const props = defineProps({
  reminderVisible: {
    default: false,
    type: Boolean,
  },
});

const totalsRemindersNum = computed(() => reminderStore.totals);

const emit = defineEmits(['update:reminderVisible']);

const reminderListRef = ref(null);

let useGlobalInfo = useGlobal();

//日历切换
const checkedDate = ref(new Date());

const calenderBoxRef = ref();

const initCalenderMonthDate = ref(new Date());

let expandCalender = ref(false);

const showCalenderWeekSimple = ref(false);

const handleScroll = event => {
  reminderListRef.value?.closeAllPopover();
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  showCalenderWeekSimple.value =
    calenderBoxRef.value.clientHeight - scrollTop < 20;
  if (scrollTop === 0) {
    showCalenderWeekSimple.value = false;
  }
};

const getWeekSunday = value => {
  initCalenderMonthDate.value = value;
};

const loadReminderList = debounce(() => {
  if (pageNum.value < totalNumber.value) {
    pageNum.value++;
    getReminderListData(pageNum.value);
  }
}, 500);

//列表加载
const isLoading = ref(false);

const total = ref(60);

//第几页
const pageNum = ref(1);

//总页数
const totalNumber = ref(1);

const reminderListData = ref([]);

const selectParams = ref({
  options: { pid: null, name: '全部', type: null },
  onlyMine: parseInt(localStorage.getItem('onlyMineStatus') as string),
  status: null,
});

const getReminderListData = debounce(pageNum => {
  let params = {
    ...selectParams.value,
    startTime: dayjs(checkedDate.value ? checkedDate.value : new Date())
      .startOf('day')
      .valueOf(),
    endTime: dayjs(checkedDate.value ? checkedDate.value : new Date())
      .endOf('day')
      .valueOf(),
    patientId: useGlobalInfo.userId,
    pageNum,
    pageSize: 10,
  };
  isLoading.value = true;
  reminderStore
    .getReminderListFnc(params)
    .then(res => {
      totalNumber.value = res.totalNumber;
    })
    .finally(() => {
      isLoading.value = false;
    });
}, 500);

const checkedDateChange = () => {
  pageNum.value = 1;
  getReminderListData(pageNum.value);
};

const targetMonthDate = ref('');

const getTargetMonthDate = value => {
  targetMonthDate.value = value;
  getMonthDate();
};

const getMonthDate = debounce(() => {
  const dateRange = getMonthDateRange(
    targetMonthDate.value ? targetMonthDate.value : checkedDate.value
  );
  let params = {
    ...selectParams.value,
    patientId: useGlobalInfo.userId,
    startTime: dateRange.firstDayOfPreviousMonth,
    endTime: dateRange.lastDayOfNextMonth,
  };
  reminderStore.getReminderCountFnc(params);
}, 200);

const getMonthDateRange = date => {
  // 获取前一个月的第一天
  const firstDayOfPreviousMonth = dayjs(date)
    .subtract(1, 'month')
    .startOf('month')
    .startOf('day')
    .valueOf();

  // 获取后一个月的最后一天
  const lastDayOfNextMonth = dayjs(date)
    .add(1, 'month')
    .endOf('month')
    .endOf('day')
    .valueOf();
  return { firstDayOfPreviousMonth, lastDayOfNextMonth };
};

const paramsChange = value => {
  const { options, onlyMine, status } = value;
  selectParams.value.options = options[options.length - 1];
  selectParams.value.onlyMine = onlyMine;
  selectParams.value.status = status === 0 ? null : status;
  pageNum.value = 1;
  getReminderListData(pageNum.value);
  getMonthDate();
};

const getOnlyMineStatusReq = () => {
  let userAccount = localStorage.getItem('userAccount');
  getOnlyMineStatus({ account: userAccount, type: 2 }).then(res => {
    if (res.code === 'E000000') {
      localStorage.setItem('onlyMineStatus', res.data);
      selectParams.value.onlyMine = res.data;
    }
  });
};

//列表操作
const delayReminder = value => {
  let reminderIndex;
  let orginReminderList = [];
  let page;
  reminderStore.reminderList.forEach(item => {
    item.children.forEach(reminder => {
      orginReminderList.push(reminder);
    });
  });
  let childrenIndex = orginReminderList.findIndex(
    element => element.backlogId === value.backlogId
  );
  if (childrenIndex == -1) {
    reminderIndex = 1;
  } else {
    reminderIndex = childrenIndex;
  }
  if (orginReminderList.length >= 10) {
    page = Math.floor(reminderIndex / 10) + 1;
  } else {
    page = 1;
  }
  getReminderListData(page);
  getMonthDate();

  if (value.type && value.type === 12) close();
};

watch(
  () => props.reminderVisible,
  async value => {
    if (value) {
      await getOnlyMineStatusReq();
      pageNum.value = 1;
      getReminderListData(pageNum.value);
      getMonthDate();
    }
  }
);

// 关闭抽屉
const close = () => {
  emit('update:reminderVisible', false);
};
</script>
<style scoped lang="less">
:deep(.el-drawer) {
  height: calc(100% - 60px);
  top: 60px;

  .el-drawer__header {
    margin-bottom: 0;
    padding: 12px 16px;
    border-bottom: 1px solid #e9e8eb;
    font-size: 16px;
    font-weight: 600;
    color: #101b25;
  }
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    .scroll-box {
      flex: 1;
      background: #fff;
      overflow-y: scroll;
      box-sizing: border-box;
    }

    /*修改滚动条样式*/
    .scroll-box::-webkit-scrollbar {
      width: 0;
      height: 0;
      /**/
    }

    .scroll-box::-webkit-scrollbar-track {
      background: rgb(239, 239, 239);
      border-radius: 2px;
    }

    .scroll-box::-webkit-scrollbar-thumb {
      background: #bfbfbf;
      border-radius: 0;
    }
  }
}
.calender-box {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  padding: 4px 18px;
}
.reminder-card {
  height: 200px;
  background-color: #9ca3af;
}
.scroll-box {
  position: relative;
}
.calenderWeekSimple-wrapper {
  position: sticky;
  top: 0;
  z-index: 1;
}
.expand-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #2e6be6;
  cursor: pointer;
  font-size: 14px;
  padding: 16px 0;
  .el-icon {
    margin-left: 4px;
    transition: transform 0.3s ease;
  }
  .expand {
    transform: rotate(-90deg);
  }
  .shrink {
    transform: rotate(90deg);
  }
}
.bottom-divider {
  color: #7a8599;
}
.loading-icon {
  color: #7a8599;
  animation: spin 1s infinite linear;
}
.count {
  color: #2e6be6;
  margin-left: 8px;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  } /* 初始状态，角度为0度 */
  100% {
    transform: rotate(360deg);
  } /* 结束状态，角度为360度（完全旋转一周）*/
}
</style>
