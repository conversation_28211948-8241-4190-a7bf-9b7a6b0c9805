<template>
  <TopInfo>
    <div class="transcribe-box flex items-center">
      <div class="flex-1 text-right date">
        {{ dayjs(data.userQuestionnaireDate).format('YYYY-MM-DD') }}
        生活方式随访
      </div>
    </div>
  </TopInfo>
  <div class="num-box mt-58">
    <div class="text">
      共计{{ finalAssessmentList?.length || 0 }}个量表，已填写{{
        completeAssessmentLength
      }}个
    </div>
  </div>
  <div
    v-for="(item, i) in finalAssessmentList"
    :key="item.userQuestionnaireId || i"
    class="follows mb-2xs"
  >
    <div class="header flex items-center">
      <div class="left flex items-center">
        <div class="module-one"></div>
        <div class="module-two ml-10">{{ item.questionnaireName }}</div>
      </div>
      <div
        v-if="currEditId != item.userQuestionnaireId"
        class="flex flex-1 pr-16"
      >
        <div v-if="item.status === 0" class="status ml-16 mr-16 flex-1">
          未完成
        </div>
        <div v-else class="flex-1 text-right time">
          上次编辑人员/时间：{{ item.editorName || '--' }}
          {{
            item.editTime
              ? dayjs(item.editTime).format('YYYY-MM-DD HH:mm')
              : '--'
          }}
        </div>
        <el-button link class="link" @click="editAssessment(item)">
          <img
            class="w-11 h-13 ml-24 mr-6"
            src="@/assets/imgs/overview/icon-edit.png"
            alt=""
          />
          编辑
        </el-button>
      </div>
    </div>
    <template v-if="item.survey">
      <AssessmentShow
        v-if="item.status === 1 && currEditId != item.userQuestionnaireId"
        :survey-data="item.survey.pages[0].elements"
        :survey-answer="item.answerInfo ? JSON.parse(item.answerInfo) : null"
        :conclusion="JSON.parse(item.conclusion || '{}')"
        :score="(item.score && JSON.parse(item.score)) || undefined"
        :is-latest="item.questionnaireVersion === 2"
      />
      <AssessmentEdit
        v-if="currEditId === item.userQuestionnaireId"
        :survey-data="item.survey"
        :survey-answer="item.answerInfo ? JSON.parse(item.answerInfo) : null"
        :calculator="item.calculator"
        :only-one-times-data="onlyOneTimesData"
        :user-questionnaire-id="item.userQuestionnaireId"
        :status="item.status!"
        @computed-assessment="computedAssessment"
      />
    </template>
  </div>
</template>
<script setup lang="ts">
import dayjs from 'dayjs';
import {
  survey,
  survey2,
  survey3,
  survey4,
  survey5,
  kccpSurvey,
  anxdepSurvey,
  sleepSurvey,
  normalSurvey,
  lifeSurvey,
  healthSurvey,
  sportsSurvey,
} from 'follow-up-survey-json';
import AssessmentEdit from './AssessmentEdit.vue';
import AssessmentShow from './AssessmentShow.vue';
import { cloneDeep } from 'lodash-es';
import {
  getFollowLifestyleDetail,
  getFollowLifestyleRuleInfo,
} from '@/api/followup';
import store from '@/store';
import TopInfo from '../TopInfo.vue';
import {
  IApiFollowLifestyleDetail,
  IApiFollowLifestyleDetailItem,
} from '@/interface/type';
defineOptions({
  inheritAttrs: false,
});
interface IProps {
  data?: any;
}
const props = defineProps<IProps>();
const globalStore = store.useGlobal();
const lifeList = ref<IApiFollowLifestyleDetail>([]);
const currEditId = ref(null);
const surveyMap = {
  NORMAL: {
    survey: {
      before: survey.survey,
      latest: normalSurvey.survey,
    },
    calculator: {
      before: survey.calculator,
      latest: normalSurvey.calculator,
    },
  },
  PSQI: {
    survey: {
      before: survey2.survey,
      latest: sleepSurvey.survey,
    },
    calculator: {
      before: survey2.calculator,
      latest: sleepSurvey.calculator,
    },
  },
  LIFE: {
    survey: {
      before: survey3.survey,
      latest: lifeSurvey.survey,
    },
    calculator: {
      before: survey3.calculator,
      latest: lifeSurvey.calculator,
    },
  },
};
const assessmentList = [
  {
    title: '一般情况调查问卷',
    key: 'NORMAL',
  },
  {
    title: '睡眠质量调查问卷',
    key: 'PSQI',
  },
  {
    title: '生活质量调查问卷',
    key: 'LIFE',
  },
  {
    title: '焦虑评估调查问卷',
    survey: cloneDeep(survey4.survey),
    calculator: survey4.calculator,
  },
  {
    title: '抑郁评估调查问卷',
    survey: cloneDeep(survey5.survey),
    calculator: survey5.calculator,
  },
  {
    title: '焦虑抑郁合并筛查量表',
    survey: cloneDeep(anxdepSurvey.survey),
    calculator: anxdepSurvey.calculator,
  },
  {
    title: '堪萨斯城心肌病问卷-KCCQ',
    survey: cloneDeep(kccpSurvey.survey),
    calculator: kccpSurvey.calculator,
  },
  {
    title: 'EQ-5D-5L健康状态评估量表',
    survey: cloneDeep(healthSurvey.survey),
    calculator: healthSurvey.calculator,
  },
  {
    title: '运动能力调查量表',
    survey: cloneDeep(sportsSurvey.survey),
    calculator: sportsSurvey.calculator,
  },
];

const onlyOneTimesData = ref({});
const getOnlyNoeTimesData = () => {
  getFollowLifestyleRuleInfo({ patientId: Number(globalStore.userId) }).then(
    res => {
      if (!res) return;
      let info = res.desc ? JSON.parse(res.desc) : {};
      // 是否吸烟和是否喝酒，选择为不喝酒不抽烟时（value为0），才需要在填写问卷时过滤掉，否则，需要继续作答。
      if (info && info.isSmoking != 0) {
        delete info.isSmoking;
      }
      if (info && info.isDrinking != 0) {
        delete info.isDrinking;
      }
      onlyOneTimesData.value = info;
      if (info.height) {
        sessionStorage.setItem('height', info.height);
      }
    }
  );
};
const initList = () => {
  let params = {
    patientId: Number(globalStore.userId),
    userQuestionnaireDate: props.data.userQuestionnaireDate,
  };
  getFollowLifestyleDetail(params).then(res => {
    lifeList.value = res;
  });
  getOnlyNoeTimesData();
};
const editAssessment = item => {
  currEditId.value = item.userQuestionnaireId;
};

const finalAssessmentList = computed<
  Array<IApiFollowLifestyleDetailItem & { [key: string]: any }>
>(() => {
  return (
    lifeList.value.map(v => {
      const info = cloneDeep(
        assessmentList.find(re => re.title === v.questionnaireName)
      );
      if (info?.key) {
        const { survey, calculator } = cloneDeep(surveyMap[info.key]) || {};

        if (v.questionnaireVersion === 2) {
          info.survey = survey?.latest;
          info.calculator = calculator?.latest;
        } else {
          info.survey = survey?.before;
          info.calculator = calculator?.before;
        }
      }
      return {
        ...info,
        ...v,
      };
    }) || []
  );
});
const completeAssessmentLength = computed(() => {
  return lifeList.value.filter(re => re.status === 1).length;
});
const computedAssessment = type => {
  currEditId.value = null;
  if (type === 1) {
    initList();
  }
};
onMounted(() => {
  initList();
});
</script>

<style scoped lang="less">
.transcribe-box {
  .date {
    font-size: 14px;
    color: #3a4762;
  }
  .link {
    font-size: 14px;
    color: #2e6be6 !important;
    display: flex;
    align-items: center;
    &:hover {
      color: #2e6be6;
      opacity: 0.75;
      cursor: pointer;
    }
    &:active {
      color: #2e6be6 !important;
      opacity: 1;
    }
  }
}
.num-box {
  font-size: 14px;
  height: 52px;
  line-height: 52px;
  box-sizing: border-box;
  background: #fff;
  padding: 0 16px;
  border-radius: 6px 6px 0 0;
  .text {
    width: 100%;
    height: 100%;
    color: #7a8599;
    border-bottom: 1px solid #e9e8eb;
  }
}
.follows {
  width: 100%;
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(186, 200, 212, 0.5);
  border-radius: 6px;
  padding: 16px 0;
  box-sizing: border-box;
  &:nth-of-type(2) {
    border-radius: 0 0 6px 6px;
  }
  .header {
    .status {
      font-size: 14px;
      color: #e63746;
    }
    .time {
      color: #7a8599;
      font-size: 14px;
    }
    .link {
      font-size: 14px;
      color: #2e6be6 !important;
      display: flex;
      align-items: center;
      &:hover {
        color: #2e6be6;
        opacity: 0.75;
        cursor: pointer;
      }
      &:active {
        color: #2e6be6 !important;
        opacity: 1;
      }
    }
    .left {
      .module-one {
        width: 6px;
        height: 16px;
        background: #2e6be6;
        border-radius: 2px;
      }
      .module-two {
        font-size: 16px;
        font-weight: 700;
        color: #101b25;
      }
    }
  }
}
.time-zone {
  padding: 0 16px;
  box-sizing: border-box;
}
</style>
