import * as path from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import HrtResolver from '@hrt/components/dist/auto-import-resolver';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  css: {
    postcss: {
      plugins: [tailwindcss, autoprefixer],
    },
  },
  plugins: [
    vue(),
    vueJsx(),
    // vueDevTools({
    //   launchEditor: 'webstorm',
    // }),
    AutoImport({
      eslintrc: {
        enabled: false,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      },
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'Icon',
        }),
      ],
      imports: ['vue', 'vue-router'],
    }),
    Components({
      dirs: [],
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ['ep'],
        }),
        HrtResolver(),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
  ],
  esbuild: {
    drop: mode === 'production' ? ['debugger', 'console'] : [],
  },
  build: {
    rollupOptions: {
      input: 'index.html',
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')) {
            const arr = id.toString().split('node_modules/');
            return arr[arr.length - 1].split('/')[0].toString();
          }
        },
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8088,
    hmr: {
      host: '127.0.0.1',
      port: 8088,
    },
    proxy: {
      // '/api/ocr': {
      //   target: 'http://**************:9213/health-manage/',
      //   changeOrigin: true,
      //   // rewrite: (path: string) => path.replace(/^\/api/, ''),
      // },
      '/api': {
        // target: 'http://**************:8213/health-manage/',
        // target: 'http://***************:8213/health-manage/',
        target: 'https://www.hrttest.cn/health-manage/',
        // target: 'https://assistant.scheartmed.com/health-manage/',
        changeOrigin: true,
        // rewrite: (path: string) => path.replace(/^\/api/, ''),
      },
    },
  },
}));
