<template>
  <div
    class="nopx-node-content"
    :class="{ 'has-children': hasChild || isSpecial || checkType('title') }"
    :style="{
      width:
        (checkType('radio') || itemLayout === 'inline') && !hasChild
          ? 'auto'
          : '',
      marginBottom:
        level === 0 && hasChild && !checkType('title') ? '16px' : '',
    }"
  >
    <div
      v-if="checkType('title')"
      class="item"
      :style="{
        width: hasChild || isSpecial ? '100%' : 'auto',
        padding: '0 24px',
        background: level === 0 && (hasChild || isSpecial) ? '#f7f8fa' : '',
      }"
    >
      <div class="title">
        {{ node.diseaseName }}
      </div>
    </div>
    <div
      v-if="checkType('checkbox')"
      class="item"
      :style="{
        width: hasChild || isSpecial ? '100%' : 'auto',
        padding: '0 24px',
        background: level === 0 && (hasChild || isSpecial) ? '#f7f8fa' : '',
      }"
    >
      <el-checkbox
        :key="node.diseaseId"
        :model-value="selectedIds.includes(node.diseaseId)"
        @change="checkboxChange"
      >
        <el-icon v-show="hasCurChild && (selected || searchWords)">
          <i-ep-caret-bottom />
        </el-icon>
        <el-icon v-show="hasCurChild && !selected && !searchWords">
          <i-ep-caret-right />
        </el-icon>
        {{ node.diseaseName }}
      </el-checkbox>
    </div>
    <div v-if="isSpecial" class="special-box">
      <el-input
        v-model="localSpecialData[specialMap[node.diseaseId].key]"
        type="textarea"
        :rows="specialMap[node.diseaseId].rows || 4"
        :disabled="!selected"
        :maxlength="specialMap[node.diseaseId].maxLength || 400"
        placeholder="请输入内容"
        @input="specialChange"
      />
      <span
        v-if="localSpecialData[specialMap[node.diseaseId].key]"
        class="clear-btn"
        @click="specialClear"
        >清除</span
      >
    </div>
    <div
      v-if="checkType('radio')"
      class="item"
      :style="{
        width: hasChild ? '100%' : 'auto',
        'padding-left': '24px',
        background: level === 0 && (hasChild || isSpecial) ? '#f7f8fa' : '',
      }"
    >
      <el-radio
        :label="node.diseaseId"
        :model-value="
          selectedIds.includes(node.diseaseId) ? node.diseaseId : ''
        "
        @change="() => radioChange(node.diseaseId)"
      >
        <el-icon v-show="hasCurChild && (selected || searchWords)">
          <i-ep-caret-bottom />
        </el-icon>
        <el-icon v-show="hasCurChild && !selected && !searchWords">
          <i-ep-caret-right />
        </el-icon>
        {{ node.diseaseName }}
      </el-radio>
    </div>
    <div
      v-if="selected || searchWords || checkType('title')"
      class="item-children"
      :style="{ 'padding-left': 24 + 'px' }"
    >
      <TreeNode
        v-for="(item, index) in node?.children"
        :key="item.diseaseId + '-' + index"
        :search-words="searchWords"
        :selected-ids="selectedIds"
        :special-config="specialConfig"
        :special-info="specialInfo"
        :node-data="item"
        :data-map="dataMap"
        :is-first="index === 0"
        :level="level + 1"
        :item-layout="itemLayout"
        @inner-change="onChangeHandler"
        @inner-special-change="innerSpecialChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, keyBy, pullAll } from 'lodash-es';
import { getAfterIds, getForwardIds, getRelativeIds } from '../util';
import { ITreeNodeProps, IDiseaseItem } from '../type';
defineOptions({
  name: 'TreeNode',
});
type TSpecial = Record<number, any>;
const node = ref<IDiseaseItem>({} as IDiseaseItem);
const specialMap = ref<TSpecial>({});
const localSpecialData = ref<TSpecial>({});
const emit = defineEmits([
  'onChange',
  'innerChange',
  'onSpecialChange',
  'innerSpecialChange',
]);
const props = withDefaults(defineProps<ITreeNodeProps>(), {
  isFirst: () => true,
  level: () => 0,
});

watchEffect(() => {
  const { specialConfig } = props;
  if (specialConfig) {
    specialMap.value = keyBy(specialConfig, 'id');
  }
});
watchEffect(() => {
  const { specialInfo } = props;
  if (specialInfo) {
    localSpecialData.value = JSON.parse(JSON.stringify(specialInfo));
  }
});
watch(
  props.nodeData,
  newVal => {
    node.value = cloneDeep(newVal);
  },
  { immediate: true }
);
const selected = computed(() =>
  props.selectedIds.includes(node.value.diseaseId)
);
const isSpecial = computed(() => specialMap.value[node.value.diseaseId]);
const hasChild = computed(
  () => node.value.originChildren && node.value.originChildren.length > 0
);
const hasCurChild = computed(() => node.value.children?.length > 0);
const checkType = computed(
  () => (type: string) => node.value.chooseType === type
);
const specialChange = (val: string) => {
  const key = specialMap.value[node.value.diseaseId]?.key;
  if (!key) return;
  specialChangeHandler(key, val);
};
const innerSpecialChange = (data: TSpecial) => {
  if (props.level === 0) {
    emit('onSpecialChange', data);
  } else {
    emit('innerSpecialChange', data);
  }
};
const specialClear = () => {
  const key = specialMap.value[node.value.diseaseId]?.key;
  if (!key) return;
  localSpecialData.value[key] = '';
  specialChangeHandler(key, '');
};
const specialChangeHandler = (key: string, val: string) => {
  innerSpecialChange({
    ...localSpecialData.value,
    [key]: val,
  });
};
const checkboxChange = (val: boolean) => {
  const { diseaseId } = props.nodeData;
  let ids = [...props.selectedIds];
  if (val) {
    const forwardIds = props.searchWords
      ? getForwardIds(diseaseId, props.dataMap)
      : [];
    const forwardOmitIds = [];
    for (let v of forwardIds) {
      const item = props.dataMap[v];
      if (item.chooseType === 'radio') {
        forwardOmitIds.push(...getRelativeIds(item, props.dataMap));
      }
    }
    pullAll(ids, forwardOmitIds);
    ids.push(diseaseId, ...forwardIds);
  } else {
    const afterIds = getAfterIds(node.value);
    pullAll(ids, afterIds);
    if (isSpecial.value) {
      specialClear();
    }
  }
  onChangeHandler(ids);
};
const radioChange = (val: number) => {
  const newIds = [...props.selectedIds, val];
  const omitIds = getRelativeIds(node.value, props.dataMap);
  const forwardIds = props.searchWords ? getForwardIds(val, props.dataMap) : [];
  const forwardOmitIds = [];
  for (let v of forwardIds) {
    const item = props.dataMap[v];
    if (item.chooseType === 'radio') {
      forwardOmitIds.push(...getRelativeIds(item, props.dataMap));
    }
  }

  pullAll(newIds, [...omitIds, ...forwardOmitIds]);
  onChangeHandler([...newIds, ...forwardIds]);
};
const onChangeHandler = (ids: number[]) => {
  const resIds = [...new Set(ids)];
  if (props.level === 0) {
    emit('onChange', resIds);
  } else {
    emit('innerChange', resIds);
  }
};
</script>

<style scoped lang="less">
.nopx-node-content {
  width: 50%;
  box-sizing: border-box;
  .title {
    font-size: 14px;
    color: #323233;
    height: 35px;
    line-height: 35px;
    font-weight: bold;
  }
  .item {
    height: 35px;
    line-height: 35px;
  }
  .item-children {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
  .special-box {
    width: 100%;
    padding: 8px 24px;
    position: relative;
    :deep(.el-textarea__inner) {
      border-radius: 2px;
      color: #111111;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
    }
    .clear-btn {
      position: absolute;
      font-size: 12px;
      font-weight: 400;
      color: #dc0101;
      right: 36px;
      bottom: 18px;
      cursor: pointer;
    }
  }
}
.has-children {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
</style>
