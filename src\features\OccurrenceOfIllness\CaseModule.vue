<template>
  <DoubtWrapper
    :data="data"
    :disabled="mode !== FormMode.VIEW"
    :unit-key="`${navPrefix}${schema.key}`"
  >
    <div
      :id="navPrefix ? `${navPrefix}${schema.key}` : schema.key"
      :class="{
        'bg-white relative rounded-md py-16': type === 'top',
        'p-16': type === 'second',
      }"
      :style="{
        'box-shadow':
          type === 'top' ? '0 1px 2px 0 rgba(186, 200, 212, 0.5)' : 'none',
        ...cardStyle,
      }"
    >
      <div
        :class="[
          'flex items-center',
          {
            'bg-[#f7f8fa] leading-[44px] h-44 pr-26 pl-16': type === 'second',
          },
        ]"
      >
        <div
          :class="[
            'bg-[#2E6BE6] rounded-sm',
            {
              'w-6 h-16 mr-10': type === 'top',
              'w-10 h-10 rounded-xl mr-6': type === 'second',
            },
          ]"
        ></div>
        <h3 class="m-0 text-base font-bold text-[#101B25]">
          {{ schema?.value || '--' }}
        </h3>
        <div
          v-if="mode === FormMode.VIEW"
          class="flex-1 flex justify-end text-[#7a8599] text-sm"
        >
          上次编辑人/时间：{{ lastEditor }}
          <span
            v-if="!readonly"
            class="flex items-center cursor-pointer ml-24 text-[#2c89dc] gap-4"
            @click="handleEdit"
          >
            <img
              class="w-11 h-13"
              src="@/assets/imgs/overview/icon-edit.png"
              alt="编辑按钮"
            />
            编辑
          </span>
        </div>
        <div
          v-if="
            type === 'second' && [FormMode.EDIT, FormMode.CREATE].includes(mode)
          "
          class="flex-1 flex justify-end text-[#7a8599] text-sm"
        >
          <span
            class="flex items-center cursor-pointer ml-24 text-[#DC0101] gap-4"
            @click="handleDelete"
          >
            删除
          </span>
        </div>
      </div>
      <div class="px-16 py-12">
        <FormList
          v-if="schema"
          :mode="mode"
          :form-config="schema"
          :form-value="formData"
          @formdata-change="handleFormChange"
        />
        <div v-else class="py-md">
          <el-skeleton :rows="5" animated />
        </div>
      </div>
      <div class="ml-124">
        <div class="flex">
          <template v-if="mode !== FormMode.VIEW">
            <el-button
              v-if="mode === FormMode.EDIT"
              :loading="loading"
              @click="handleCancel"
            >
              取消
            </el-button>
            <el-button type="primary" :loading="loading" @click="handleSave">
              {{ mode === FormMode.CREATE ? '保存' : '确定' }}
            </el-button>
          </template>
        </div>
      </div>
    </div>
  </DoubtWrapper>
</template>

<script setup lang="ts">
import FormList from '@/components/FormList/index.vue';
import DoubtWrapper from '@/features/DoubtWrapper/index.vue';
import { FormMode, FormModeValues } from '@/constant';
import dayjs from 'dayjs';
import { ElButton, ElSkeleton } from 'element-plus';
import { cloneDeep, isEmpty } from 'lodash-es';
import { CSSProperties, onMounted, ref, watch } from 'vue';

const {
  schema,
  mode = FormMode.CREATE,
  data = {},
  readonly = false,
  type = 'top',
  cardStyle = {},
  onSave,
  onDelete,
  navPrefix,
} = defineProps<{
  schema: any;
  mode: FormModeValues;
  data: Record<string, any>;
  readonly?: boolean;
  type?: 'top' | 'second';
  cardStyle?: CSSProperties;
  onSave?: (data: { key: string; id?: number | string }) => Promise<unknown>;
  onDelete?: (data: { key: string; id?: number | string }) => Promise<unknown>;
  /** 导航前缀 */
  navPrefix?: string;
}>();

/** 定义组件事件 */
const emit = defineEmits<{
  modeChange: [mode: FormModeValues];
  change: [data: Record<string, any>];
  save: [];
  close: [];
}>();

/** 表单数据 */
const formData = ref<Record<string, any>>({});
const loading = ref(false);

/** 上次编辑人/时间 */
const lastEditor = computed(() => {
  const modifyTime = formData.value.modify_time
    ? dayjs(formData.value.modify_time).format('YYYY-MM-DD HH:mm:ss')
    : '';
  const modifyUserName = formData.value.user_name ?? '';
  if (!modifyUserName && !modifyTime) {
    return '';
  }
  return `${modifyUserName} / ${modifyTime}`;
});

/** 监听数据变化 */
watch(
  () => data,
  newData => {
    if (newData) {
      formData.value = cloneDeep(newData);
    }
  },
  { immediate: true, deep: true }
);

/** 监听模式变化 */
watch(
  () => mode,
  newMode => {
    // 如果模式变为view，重置表单数据
    if (newMode === 'view') {
      formData.value = { ...data };
    }
  }
);

/** 处理表单数据变化 */
const handleFormChange = (data: Record<string, any>) => {
  const newFormData = { ...formData.value, ...data };
  formData.value = newFormData;
  emit('change', newFormData);
};

/** 处理编辑按钮点击 */
const handleEdit = () => {
  emit('modeChange', FormMode.EDIT);
};
/**
 * 删除当前record
 */
const handleDelete = async () => {
  await onDelete?.({ key: schema.key });
};
/**
 * 处理保存按钮点击
 */
async function handleSave() {
  loading.value = true;
  await onSave?.({ key: schema.key });
  loading.value = false;
}

/** 处理取消按钮点击 */
const handleCancel = () => {
  // 重置表单数据
  formData.value = { ...data };

  // 如果是创建模式，保持创建模式，否则切换到查看模式
  if (mode !== FormMode.CREATE) {
    emit('modeChange', FormMode.VIEW);
  }
  emit('close');
};

onMounted(() => {
  // 初始化表单数据
  if (!isEmpty(data)) {
    formData.value = cloneDeep(data);
  }
});
</script>
