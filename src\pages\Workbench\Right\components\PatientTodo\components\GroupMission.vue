<template>
  <div class="mission">
    <div class="postpone flex">
      <div class="title mt-4">{{ getTitle }}方式：</div>
      <div class="time-box mb-16">
        <el-checkbox-group v-model="checkTypeList" @change="changeMissionType">
          <el-checkbox label="电话" />
          <el-checkbox label="在线咨询" />
          <el-checkbox label="其他" />
        </el-checkbox-group>
        <div
          v-if="checkTypeList.some(item => item === '其他')"
          class="mission-type-reason mt-8"
        >
          <el-input
            v-model="miassionTypeInput"
            placeholder="其他方式"
            clearable
            show-word-limit
            maxlength="20"
            :rows="2"
            type="textarea"
            resize="none"
          />
        </div>
      </div>
    </div>
    <div class="postpone flex">
      <div class="title">{{ getTitle }}结果：</div>
      <div class="time-box mb-16">
        <div class="flex items-center">
          <div
            v-for="item in patientTypeList"
            :key="item.id"
            class="all-box flex items-center cursor-pointer mr-27"
            @click="changeResult(item.id)"
          >
            <div
              v-if="missionResult === item.id"
              class="change-box-checked mr-8 flex items-center justify-center"
            >
              <div class="interior-check"></div>
            </div>
            <div v-else class="change-box mr-8"></div>
            <span class="change-title">{{ item.title }}</span>
          </div>
        </div>
        <div v-if="missionResult === 2" class="result-box">
          <div class="checkbox-box ml-12 mt-6">
            <el-checkbox-group
              v-model="checkResultList"
              @change="changeMissionResult"
            >
              <el-checkbox label="无法联系" />
              <el-checkbox label="拒绝沟通" />
              <el-checkbox label="其他" />
            </el-checkbox-group>
          </div>
          <div
            v-if="checkResultList.some(item => item === '其他')"
            class="mission-type-reason mt-8"
          >
            <el-input
              v-model="miassionResultInput"
              placeholder="请输入失败原因（100字内）"
              clearable
              show-word-limit
              maxlength="100"
              :rows="3"
              type="textarea"
              resize="none"
            />
          </div>
        </div>
      </div>
    </div>
    <Btns @submit="submit" @cancel="cancel" />
  </div>
</template>
<script setup lang="ts">
import Btns from './Btns.vue';
import { dialogTip, getUserRoles, todoType } from '../index';
import useTodo from '@/store/module/useTodo';
let useTodoInfo = useTodo();
import { handleTodoApi } from '@/api/todo';
import useUserStore from '@/store/module/useUserStore';

const userStore = useUserStore();
// 方式
const checkTypeList = ref([]);
const miassionTypeInput = ref<string>('');
let changeMissionType = () => {
  if (!checkTypeList.value.some(item => item === '其他')) {
    miassionTypeInput.value = '';
  }
};

// 结果
const checkResultList = ref([]);
const miassionResultInput = ref<string>('');
let changeMissionResult = () => {
  if (!checkResultList.value.some(item => item === '其他')) {
    miassionResultInput.value = '';
  }
};
let missionResult = ref<number>(0);
let patientTypeList = ref([
  { title: '成功', id: 1 },
  { title: '失败', id: 2 },
]);
let changeResult = (id: number) => {
  missionResult.value = id;
  if (id === 1) {
    checkResultList.value = [];
    miassionResultInput.value = '';
  }
};

let submit = () => {
  if (!checkTypeList.value.length) {
    dialogTip('请选择' + getTitle.value + '方式！');
  } else if (
    checkTypeList.value &&
    checkTypeList.value.some(item => item === '其他') &&
    !miassionTypeInput.value
  ) {
    dialogTip('请填写' + getTitle.value + '方式-其他方式！');
  } else if (missionResult.value === 0) {
    dialogTip('请选择' + getTitle.value + '结果！');
  } else if (missionResult.value === 2 && !checkResultList.value.length) {
    dialogTip('请选择' + getTitle.value + '结果-失败原因！');
  } else if (
    checkResultList.value.some(item => item === '其他') &&
    !miassionResultInput.value
  ) {
    dialogTip('请填写' + getTitle.value + '方式-其他方式！');
  } else {
    let params = {
      checkTypeList: checkTypeList.value,
      miassionTypeInput: miassionTypeInput.value,
      checkResultList: checkResultList.value,
      miassionResultInput: miassionResultInput.value,
    };
    handleTodoApi({
      backlogId: useTodoInfo.todoInfo.backlogId,
      type: todoType[useTodoInfo.todoInfo.type - 1],
      content: JSON.stringify(params),
      headId: userStore.accountId,
      headRole: getUserRoles(),
    }).then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('处理成功！', 'success');
        cancel();
      } else {
        dialogTip(message);
      }
    });
  }
};
const emit = defineEmits(['close']);
let cancel = () => {
  emit('close');
};

let getTitle = computed(() => {
  let status = useTodoInfo.todoInfo.type;
  let title = '';
  if (status === 2) {
    title = '宣教';
  }
  if (status === 3 || status === 9 || status === 10 || status === 11) {
    title = '提醒';
  }

  return title;
});
</script>
<style scoped lang="less">
.mission {
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #3a4762;
  }
  .postpone {
    padding-left: 24px;
    margin-top: -4px;
    :deep(.time-box) {
      flex: 1;
      .result-box {
        background: #f7f8fa;
        .checkbox-box {
          padding-top: 6px;
          .el-checkbox-group {
            display: flex;
            flex-direction: column;
          }
        }
      }
      .all-box {
        .change-box {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #dcdee0;
        }
        .change-box-checked {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #2e6be6;
          .interior-check {
            width: 10px;
            height: 10px;
            background: #0a73e4;
            border-radius: 8px;
          }
        }
        .change-title {
          font-size: 14px;
          color: #3a4762;
        }
      }
      .el-checkbox-group {
        .is-checked {
          .el-checkbox__label {
            color: #3a4762;
          }
          .el-checkbox__inner {
            border-color: #0a73e4;
            background: #0a73e4;
          }
        }
      }
      .mission-type-reason {
        width: 100%;
        background: #f7f8fa;
        padding: 12px;
        box-sizing: border-box;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
        height: 32px;
      }
    }
  }
}
</style>
