<template>
  <HrtDialog
    v-model="chooseDrugVisibles"
    :width="1340"
    title="药物调整"
    size="extraLarge"
    draggable
    class="drug-dialog"
  >
    <div class="page-wrapper">
      <div class="reason-box">
        <div class="reason-label">药物调整原因</div>
        <div class="reason-choose">
          <el-checkbox-group v-model="adjustReason">
            <el-checkbox
              v-for="item in checkItemList"
              :key="item.value"
              :label="item.name"
              :value="item.name"
              :class="{ 'other-item': item.value === 16 }"
              @change="value => changeDrug(value, item)"
            >
              {{ item.name }}
              <el-input
                v-if="item.value === 16"
                v-model="adjustReasonOther"
                class="other-input"
                :disabled="!isDisabledOther"
              />
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="editdes-box">
          <div class="des-label">当前用药</div>
          <EditDrugTable ref="RefEditDrugTable" :params-data="paramsData" />
        </div>
        <div class="editdes-box">
          <div class="des-label">开始用药日期</div>
          <div>
            <el-date-picker
              v-model="medicationTime"
              type="date"
              clearable
              style="width: 360px"
              placeholder="选择日期时间"
              value-format="x"
              format="YYYY/MM/DD"
            />
          </div>
        </div>
        <div class="editdes-box">
          <div class="des-label">药物调整意见</div>
          <div>
            <el-input
              v-model="advice"
              placeholder="请输入"
              class="advice-input"
            />
          </div>
        </div>
        <div class="editdes-box">
          <div class="des-label">跟踪用药情况</div>
          <div>
            <el-switch v-model="adjustDrugTrack" />
            <span>
              勾选后系统会立即给患者发送调药提醒； 且3日后提醒您跟踪用药情况。
            </span>
          </div>
        </div>
      </div>
      <el-dialog
        :model-value="isShowChangeDrug"
        width="600px"
        :close-on-click-modal="false"
        center
        append-to-body
        class="second-dialog"
      >
        <div v-for="(item, i) in drugOperation" :key="i">{{ item }}</div>
        <div v-if="drugOperation.length === 0">本次未调整具体药物</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button class="close-btn" @click="isShowChangeDrug = false">
              取 消
            </el-button>
            <el-button type="primary" class="sure-btn" @click="confirmDrug">
              确 定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
    <template #footer>
      <div class="btn-box">
        <el-button plain @click="cancelEdit">取消</el-button>
        <el-button type="primary" plain class="sub-button" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </HrtDialog>
</template>
<script setup></script>
<script>
import { HrtDialog } from '@hrt/components';
import EditDrugTable from './EditDrugTable.vue';
export default {
  name: 'EditDrugInfo',
  components: { EditDrugTable },
  props: {
    paramsData: {
      type: Array,
      default: () => [],
      required: true,
    },
    otherAdjustDrugInfo: {
      type: Object,
      default: () => {
        return {
          adjustReasonList: [],
          advice: '',
          adjustReasonOther: '',
        };
      },
      required: false,
    },
    chooseDrugVisible: { type: Boolean },
  },
  data() {
    return {
      adjustDrugTrack: true,
      chooseDrugVisibles: false,
      adjustReason: [],
      adjustReasonOther: '',
      medicationTime: new Date().getTime(),
      advice: '',
      checkItemList: [
        { name: '心绞痛', value: 0 },
        { name: '血压偏高', value: 1 },
        { name: '血压偏低', value: 2 },
        { name: '心率偏快', value: 3 },
        { name: '心率偏慢', value: 4 },
        { name: '血糖偏高', value: 5 },
        { name: '血糖偏低', value: 6 },
        { name: '血脂不达标', value: 7 },
        { name: 'INR不达标', value: 8 },
        { name: '肝功能异常', value: 9 },
        { name: '肌肉损伤', value: 10 },
        { name: '心功能不全', value: 11 },
        { name: '电解质紊乱', value: 12 },
        { name: '消化道出血', value: 13 },
        { name: '出血风险高', value: 14 },
        { name: '糖化血红蛋白不达标', value: 15 },
        { name: '其他', value: 16 },
      ],
      isShowChangeDrug: false,
      drugOperation: [], //调整方案
      dealedParamsData: [],
    };
  },
  computed: {
    isDisabledOther() {
      return this.adjustReason.some(item => item === '其他');
    },
  },
  watch: {
    chooseDrugVisible: {
      handler() {
        this.chooseDrugVisibles = this.chooseDrugVisible;
      },
    },
    chooseDrugVisibles: {
      // 数据变化时执行的逻辑代码
      handler(newValue) {
        if (newValue === false) {
          this.$emit('update:chooseDrugVisible', false);
        }
      },
    },
  },
  created() {
    this.advice =
      this.otherAdjustDrugInfo.advice ||
      '按调整后用药方案服药，必要时请在在线咨询窗口联系您的专属医生';
    this.adjustReasonOther = this.otherAdjustDrugInfo.adjustReasonOther;
    this.adjustReason = this.otherAdjustDrugInfo.adjustReasonList;
    this.chooseDrugVisibles = this.chooseDrugVisible;
  },
  mounted() {},
  methods: {
    changeDateHanlder(val) {
      if (new Date(val) > Date.now()) {
        this.medicationTime = null;
        ElMessage.error('不能选择未来时间!');
      }
    },
    getDrugSpecStr(obj) {
      let ingredients = obj.ingredients + obj.contentUnit;
      let packageContent = obj.packageNum
        ? '*' + obj.packageNum + obj.unit
        : '';
      let packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
      return ingredients + packageContent + packageUnit;
    },
    submit() {
      const { dealedParamsData, drugOperation } =
        this.$refs.RefEditDrugTable.submit();
      if (dealedParamsData) {
        this.drugOperation = drugOperation;
        if (
          this.adjustReason.length === 0 ||
          !this.advice ||
          !this.medicationTime
        ) {
          ElMessage.warning('请填药物调整原因、开始用药日期和意见!');
        } else {
          this.dealedParamsData = dealedParamsData;
          if (!this.dealedParamsData.length)
            return ElMessage.warning('当前用药不能为空!');
          if (this.isDisabledOther && !this.adjustReasonOther)
            return ElMessage.warning('请填写其他药物调整原因!');
          this.isShowChangeDrug = true;
        }
      }
    },
    confirmDrug() {
      let params = {
        advice: this.advice,
        type: 3,
        drugOperation: JSON.stringify(this.drugOperation),
        adjustReason: this.adjustReason,
        drugDetailList: this.dealedParamsData,
        adjustReasonOther: this.adjustReasonOther,
        outId: '',
        adjustDrugTrack: this.adjustDrugTrack,
        medicationTime: this.medicationTime,
      };
      this.isShowChangeDrug = false;
      this.$emit('confirmDrug', params);
    },

    //
    changeDrug(value, item) {
      if (item.value === 16) this.adjustReasonOther = '';
    },
    //取消编辑
    cancelEdit() {
      this.$emit('cancelEdit');
    },
  },
};
</script>

<style lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
</style>
<style scoped lang="less">
.drug-dialog {
  .el-dialog {
    min-width: 920px;
    min-height: 700px;
  }
}
.page-wrapper {
  height: 100%;
  padding: 0 16px;
}
.reason-box {
  box-sizing: border-box;
  min-height: 500px;
  border-bottom: 1px solid #e9e8eb;
  overflow: auto;
  &::-webkit-scrollbar {
    height: 4px;
  }
  &::-webkit-scrollbar-thumb {
    height: 4px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.reason-label {
  font-size: 14px;
  font-weight: 400;
  color: #203549;
  margin: 12px 0;
}
:deep(.reason-choose) {
  .el-checkbox {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
    width: 156px;
    margin-bottom: 8px;
    margin-right: 0;
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      border-color: #0a73e4;
      background-color: #0a73e4;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #203549;
    }

    .other-input {
      width: 435px;
      height: 32px !important;
      .el-input__inner {
        height: 34px;
        border-radius: 2px;
      }
    }
  }
  .el-checkbox.other-item {
    width: 490px;
  }
}

.editdes-box {
  margin-top: 24px;
  box-sizing: border-box;
  .des-label {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
    margin-bottom: 8px;
  }
}
.btn-box {
  text-align: right;
  .sub-button {
    background-color: #0a73e4;
    color: #ffffff;
  }
  .el-button {
    width: 76px;
    height: 34px;
    border-radius: 2px;
    line-height: 34px;
    box-sizing: border-box;
    padding: 0;
    margin-top: 24px;
  }
}

:deep(.dialog-footer) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  border-top: 1px solid #e9e8eb;
  padding: 24px 20px;

  .delet-btn {
    font-size: 14px;
    color: #ff595a;
    margin-left: 28px;
    cursor: pointer;
  }
  .close-btn {
    width: 76px;
    height: 32px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #8193a3;
  }
  .sure-btn {
    margin-left: 14px;
    background: #2c89dc;
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0;
    font-size: 14px;
    color: #ffffff;
  }
}
.second-dialog {
  :deep(.el-dialog--center) {
    margin-top: 25vh !important;
  }

  :deep(.el-dialog__body) {
    padding-bottom: 0;
    font-size: 16px;
  }
  :deep(.dialog-footer) {
    display: flex;
    justify-content: flex-end;
    padding: 24px 0;
    border: none;
    margin: 0;
    .sure-btn {
      margin-left: 18px;
    }
  }
}
.dialog-footers {
  display: flex;
  justify-content: flex-end;
}
</style>
