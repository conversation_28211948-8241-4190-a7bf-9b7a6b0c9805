<template>
  <el-tooltip
    :disabled="!showTooltip"
    placement="top"
    :content="customTooltipContent || customText || children"
    :raw-content="customTooltipContent || customText ? true : false"
    popper-class="hrt-text"
  >
    <div
      class="text-ellipsis overflow-hidden whitespace-nowrap"
      :style="customStyle"
      @mouseenter="onmouseenter"
      v-html="customText || children"
    ></div>
  </el-tooltip>
</template>

<script setup lang="ts">
const showTooltip = ref(false);
interface IProps {
  placement?:
    | 'top'
    | 'top-start'
    | 'top-end'
    | 'bottom'
    | 'bottom-start'
    | 'bottom-end'
    | 'left'
    | 'left-start'
    | 'left-end'
    | 'right'
    | 'right-start'
    | 'right-end';
  customStyle?: string;
  customTooltipContent?: string;
  customText?: string;
}
withDefaults(defineProps<IProps>(), {
  placement: 'top',
  customStyle: '',
  customTooltipContent: '',
  customText: '',
});
const slots = useSlots();
const children = computed(() => {
  if (slots && slots.default) {
    return slots.default()?.[0].children;
  }
});
const onmouseenter = (e: MouseEvent) => {
  const el = e.target as HTMLElement;
  if (el.offsetWidth < el.scrollWidth) {
    showTooltip.value = true;
  } else {
    showTooltip.value = false;
  }
};
</script>

<script lang="ts">
export default {
  name: 'HrtText',
};
</script>

<style lang="less">
.hrt-text {
  max-width: 50%;
}
</style>
