import { defineStore } from 'pinia';
import { STORE_NAMES } from '@/constant/storeNames';
import { getQuestionnaire } from '@/api/followup';

interface IOption {
  questionnaireId: number;
  questionnaireName: string;
}
interface IState {
  followUpOptions: IOption[][];
}
const useExceptions = defineStore(STORE_NAMES.META_DATA, {
  state: () =>
    ({
      followUpOptions: [],
    }) as IState,
  actions: {
    getFollwUpOptions(patientId) {
      // 症状随访量表
      this.followUpOptions = [];
      getQuestionnaire({ patientId, type: 3 }).then(res => {
        this.followUpOptions[0] = res as IOption[];
      });
      // 生活方式随访
      getQuestionnaire({ patientId, type: 1 }).then(res => {
        this.followUpOptions[1] = res as IOption[];
      });
    },
  },
});

export default useExceptions;
