<template>
  <div v-if="mode === 'view'">
    <span v-if="viewTags.length === 0">--</span>
    <DiseaseTag v-else :data="viewTags" />
  </div>
  <BaseSelector
    v-else
    :title="'结论'"
    :list="conclusionList"
    :selected-data="getFlatIdList(value)"
    hide-input
    @on-change="changeHandler"
  />
</template>

<script setup lang="ts">
import DiseaseTag from '@/components/HospitalForm/components/diseaseTag.vue';
import BaseSelector from '@/components/HospitalForm/BaseSelector.vue';
import { transformList } from './utils';
import { getDataMap, idsTransform } from '@/components/StructuredSelector/util';
import { Base } from './type';
import { keyBy } from 'lodash-es';
import { formatTreeData, getFlatIdList } from './utils';
interface IProps extends Base {
  list: any[];
}
const props = defineProps<IProps>();
const emit = defineEmits(['delete', 'change']);
const conclusionList = computed(() =>
  transformList(props.list ?? [], 'value').map(v => ({
    ...v,
    chooseType: 'checkbox',
  }))
);
const listMap = computed(() => keyBy(props.list ?? [], 'id'));
const viewTags = computed(() => {
  const { mode, value } = props;
  if (mode !== 'view') return [];
  const dataMap = getDataMap(conclusionList.value);
  const ids = getFlatIdList(value)?.map(v => v.id);
  const res = idsTransform(ids, dataMap);
  const { showNameList } = res;
  return showNameList.map(v => v.text);
});
const changeHandler = val => {
  const { resultTreeData } = val;
  const res = formatTreeData(resultTreeData, listMap.value);
  emit('change', res);
};
defineOptions({
  name: 'Conclusion',
});
</script>

<style scoped lang="less">
// todo
</style>
