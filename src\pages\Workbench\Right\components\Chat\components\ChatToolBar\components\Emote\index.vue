<template>
  <div class="emoteBox" :style="{ left: focused ? 0 : '-15px' }">
    <span
      v-for="item in emoteList"
      :key="item"
      class="itemEmote"
      @click.stop="() => chooseEmote(item as string)"
      v-html="item"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface IProps {
  focused: boolean;
}
defineProps<IProps>();
const emits = defineEmits(['changeText']);
defineOptions({
  name: 'Emote',
});
const emoteList = computed(() => {
  const res: (number | string)[] = [
    128512, 128513, 128514, 128516, 128517, 128521, 128522, 128534, 128536,
    128540, 128550, 128567, 129300, 129319, 129325, 9994, 9996, 128076, 128079,
    128077, 127752, 127771, 127774, 127801, 127802, 127881, 128150, 129505,
    128262,
  ];
  // 生成常用表情
  for (let i = 0; i < res.length; i++) {
    res[i] = '&#' + res[i];
  }
  return res;
});
const chooseEmote = (val: string) => {
  emits('changeText', {
    val,
    type: 'emote',
  });
};
</script>

<style scoped lang="less">
.emoteBox {
  width: 260px;
  background: #ffffff;
  box-shadow: 0px 2px 24px 0px rgba(200, 201, 204, 0.5);
  border-radius: 2px;
  position: absolute;
  bottom: 48px;
  left: -15px;
  display: flex;
  flex-wrap: wrap;
  max-height: 200px;
  .itemEmote {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    margin: 4px;
    margin-bottom: 16px;
    font-size: 16px;
    cursor: pointer;
    &:hover {
      border-radius: 4px;
      background: rgba(200, 201, 204, 0.5);
    }
  }
}
</style>
