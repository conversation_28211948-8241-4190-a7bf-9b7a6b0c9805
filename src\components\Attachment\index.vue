<template>
  <div ref="allFile" class="flex relative mt-16 pt-16 border-t-[1px]">
    <div class="label">全部附件</div>
    <div class="flex-1" :style="customStyle">
      <UploadImages
        ref="uploadRef"
        v-model:img-list="imgList"
        :custom-style="customStyle"
        show-status-label
        :limit="40"
        show-status
        :is-batch="isBatch"
        :source-id="sourceId"
        :source-type="sourceType"
        :task-status="taskStatus"
        :task-method="taskMethod"
        :task-section="taskSection"
        :task-id="taskId"
        @on-delete="deleteHandler"
      />
    </div>
    <div v-if="imgList.length" class="expand" @click="toggle">
      {{ show ? '收起' : '展开' }}附件 ({{ imgList.length }})
      <el-icon :style="{ transform: 'rotate(' + (show ? 270 : 90) + 'deg)' }">
        <DArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  accessoryDelete,
  accessoryUpload,
  getAccessory,
} from '@/api/attchment';
import { queryCaseTaskApi } from '@/api/task';
import UploadImages from '@/components/UploadImages/index.vue';
import bus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
import { IRecordSourceType } from '@/store/module/useComponentsTabAction';
import { DArrowRight } from '@element-plus/icons-vue';
import useInternDrawer, { ModulesType } from '@/store/module/useInternDrawer';
import { debounce } from 'lodash-es';
interface IProps {
  sourceId?: number;
  sourceType?: 0 | 1 | 2 | 3;
  /** 是否批量扫描 */
  isBatch?: boolean;
  queryParams?: Record<string, Record<string, any>>;
}
import { ICaseTaskApi } from '@/pages/Workbench/Header/components/TaskAcceptanceDrawer/type';
const globalStore = useGlobal();
const useInternDrawerInfo = useInternDrawer();
const props = withDefaults(defineProps<IProps>(), {
  isBatch: true,
  queryParams: () => ({}),
});
const emits = defineEmits(['update-source-id', 'refresh', 'toggle']);
const uploadRef = shallowRef();
const imgList = ref<string[]>([]);
const show = ref(true);
const needUploaded = ref(true);
const customStyle = ref('');
const update = debounce(() => {
  getImgList();
}, 500);
const toggle = () => {
  const newVal = !show.value;
  uploadRef.value.toggleImgList(newVal);
  show.value = newVal;
  setTimeout(() => {
    emits('toggle');
  }, 16);
};
const getBaseParams = () => ({
  sourceId: props.sourceId || null,
  sourceType: props.sourceType!,
  ...(props.queryParams?.['all'] ?? {}),
  ...(props.queryParams?.['attachment'] ?? {}),
});
const uploadHandler = async (urls: string[]) => {
  const params: any = {
    ...getBaseParams(),
    patientId:
      globalStore.currentRole === 4
        ? useInternDrawerInfo.patientId
        : globalStore.userId,
    url: urls,
  };
  const res = await accessoryUpload(params);
  if (!props.sourceId && res.patientHistoryId) {
    emits('update-source-id', res.patientHistoryId);
  }
  emits('refresh', res.patientHistoryId);
};
const deleteHandler = async (val: string) => {
  if (!props.sourceId) return;
  const params = { ...getBaseParams(), url: val };
  const res = await accessoryDelete(params);
  if (res) {
    bus.emit('refresh-record-data', props.sourceId!);
  }
};
const getImgList = async () => {
  const params = {
    page: 1,
    pageSize: 1000,
    ...getBaseParams(),
  };
  const res = await getAccessory(params);
  needUploaded.value = false;
  imgList.value = res?.contents ?? [];
};

// 病历任务查询
const taskStatus = ref(null);
const taskMethod = ref<number>(0);
const taskSection = ref('');
const taskId = ref(0);
const getCaseTaskStatus = async () => {
  const info: ICaseTaskApi = {
    sourceId: props.sourceId!,
    taskType: props.sourceType!,
    taskMethod: 1,
  };

  if (globalStore.currentRole === 4) info.taskId = useInternDrawerInfo.taskId;

  await queryCaseTaskApi(info).then((res: any) => {
    const { data } = res;

    taskStatus.value = data ? data.taskStatus : null;
    taskSection.value = data ? data.taskSection : '';
    taskMethod.value = data ? data.taskMethod : -1;
    taskId.value = data ? data.taskId : 0;

    if (globalStore.currentRole === 4 && data) {
      useInternDrawerInfo.setBaseInfo({
        patientId: useInternDrawerInfo.patientId,
        taskId: data.taskId,
        caseId: useInternDrawerInfo.caseId,
        sourceType: props.sourceType as IRecordSourceType,
        sourceId: props.sourceId as number,
        sourceTime: data.statusTime,
        modules: (JSON.parse(data.taskSection) || []) as ModulesType[],
        submitSourceId: useInternDrawerInfo.taskId,
        internSubmitBatchId: useInternDrawerInfo.internSubmitBatchId,
      });
    }
  });
};

watch(
  imgList,
  (val, oldVal) => {
    if (!needUploaded.value) {
      needUploaded.value = true;
      return;
    }
    if (val.length > oldVal.length) {
      const newUrls = val.slice(oldVal.length);
      uploadHandler(newUrls);
    }
  },
  { deep: true }
);
const upDateHandler = (id: number) => {
  if (props.sourceId === id) {
    update();
  }
};
const updateTask = () => {
  if (props.sourceId) getCaseTaskStatus();
};

const allFile = ref<HTMLElement>();
let observer: any;
onMounted(() => {
  bus.on('refresh-attachment', upDateHandler);
  bus.on('update-task-status', updateTask);

  observer = new ResizeObserver(entries => {
    const width = entries[0].contentRect.width;
    const newWidth = width && props.isBatch ? `${width - 220}px` : '73%';
    customStyle.value = `width:${newWidth}`;
  });
  observer.observe(allFile.value);
});
onUnmounted(() => {
  bus.off('refresh-attachment', upDateHandler);
  bus.off('update-task-status', updateTask);
  observer.disconnect();
});

watch(
  () => props.sourceId,
  val => {
    if (val) {
      getImgList();
      if (props.sourceId) getCaseTaskStatus();
    }
  },
  { immediate: true }
);

defineOptions({
  name: 'AllAttachment',
});
</script>

<style scoped lang="less">
.label {
  margin-right: 10px;
  font-weight: bold;
  line-height: 40px;
}
.expand {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  top: 30px;
  right: 0;
  width: 100px;
  color: #2e6be6;
  font-size: 14px;
}
</style>
