<template>
  <div class="handle-exceptions-wrapper flex flex-col">
    <div class="flex-1 pb-2xs overflow-auto">
      <Header :target-value="targetValue" />
      <CardWrapper
        v-if="riskType === 1 || riskType === 5"
        class="mt-2xs"
        title="血压异常"
      >
        <BloodPressureAbnormal v-if="targetValue" :target-value="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 2 || riskType === 5"
        class="mt-2xs"
        title="心率异常"
      >
        <HeartRateAbnormal v-if="targetValue" :target-value="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 3 || riskType === 5"
        class="mt-2xs"
        title="血糖异常"
      >
        <BloodGlucoseAbnormal v-if="targetValue" />
      </CardWrapper>
      <CardWrapper
        v-if="riskType === 4 || riskType === 5"
        class="mt-2xs"
        title="体重异常"
      >
        <BloodWeightAbnormal v-if="targetValue" />
      </CardWrapper>
    </div>
    <HrtDialog
      v-model="dialogVisible"
      title="处理意见"
      :width="500"
      size="large"
    >
      <el-radio-group v-model="treatMethod" class="treat-method">
        <el-radio
          v-for="item in eventTreatList"
          :key="item.value"
          :label="item.value"
        >
          <div class="font-sm">
            {{ item.name }}
            <span>{{ item.tips }}</span>
          </div>
        </el-radio>
      </el-radio-group>
      <template #footer>
        <div class="p-sm pr-lg border-t">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            :loading="loadingDrugTableData || handleEventLoading"
            type="primary"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </HrtDialog>
    <div class="footer shrink-0 flex items-center justify-end px-sm bg-white">
      <el-button
        type="primary"
        :disabled="!canDealExceptions"
        @click="dialogVisible = true"
      >
        处理
      </el-button>
    </div>

    <EditDrug
      v-if="chooseDrugVisible"
      v-model:choose-drug-visible="chooseDrugVisible"
      :params-data="drugTableData"
      @confirm-drug="handleAdjustDrug"
      @cancel-edit="chooseDrugVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { HrtDialog } from '@hrt/components';
import Header from './Header.vue';
import BloodPressureAbnormal from './BloodPressureAbnormal.vue';
import BloodGlucoseAbnormal from './BloodGlucoseAbnormal.vue';
import BloodWeightAbnormal from './BloodWeightAbnormal.vue';
import HeartRateAbnormal from './HeartRateAbnormal.vue';
import CardWrapper from '@/components/CardWrapper/index.vue';
import { useToggle } from '@vueuse/core';
import store from '@/store';
import {
  getRiskTargetValue,
  riskHandleEvent,
  riskHandlePartEvent,
} from '@/api/exceptions';
import {
  IApiDrugPatientCurrentDrugList,
  IApiRiskTargetValue,
} from '@/interface/type';
import EditDrug from '@/components/DrugInfo/components/EditDrug.vue';
import { getDrugPatientAdjust, getDrugPatientCurrent } from '@/api/drug';
import bus from '@/lib/bus';
import { ITabProps } from '@/store/module/useTabs';

defineOptions({
  name: 'HandleExceptions',
});
const props = defineProps<Partial<ITabProps>>();

const riskType = computed(() => props.data?.riskType);

const globalStore = store.useGlobal();
const handleEventLoading = ref(false);
const canDealExceptions = ref(true);
const [dialogVisible, setDialogVisible] = useToggle();
const treatMethod = ref(4);
const targetValue = ref<IApiRiskTargetValue>();
const eventTreatList = [
  {
    value: 4,
    name: '观察',
    tips: '（不生成后续待办）',
  },
  {
    value: 5,
    name: '调药',
    tips: '（调药操作后3日，系统提醒您跟踪患者调药）',
  },
  {
    value: 6,
    name: '门诊',
    tips: '（系统3日后自动提醒您跟踪患者门诊情况）',
  },
  {
    value: 7,
    name: '住院',
    tips: '（系统3日后自动提醒您跟踪患者住院情况）',
  },
];
const riksTypeMap = {
  1: 'BLOOD_PRESSURE',
  2: 'HEART_RATE',
  3: 'BLOOD_SUGAR',
  4: 'WEIGHT',
};
const getTargetValue = async () => {
  const params = { patientId: globalStore.userId! };
  targetValue.value = await getRiskTargetValue(params);
};
onMounted(() => {
  getTargetValue();
});

// ‘处理’提交成功后需完成的事项
const handleDealOnSubmit = () => {
  bus.emit('updete-todo-list');
  bus.emit('update-analysis-indicator');
  canDealExceptions.value = false;
};
const treatmentMethodReq = async () => {
  const params = {
    patientId: globalStore.userId!,
    treatmentMethod: treatMethod.value,
    type: riskType.value,
  };
  try {
    handleEventLoading.value = true;
    await handlePartOrAllReq(params);
    ElMessage.success('处理成功');
    setDialogVisible(false);
    handleDealOnSubmit();
  } finally {
    handleEventLoading.value = false;
  }
};
const handleSubmit = async () => {
  if (treatMethod.value === 5) {
    await getDrugPatientCurrents();
    chooseDrugVisible.value = true;
  } else {
    await treatmentMethodReq();
  }
};

/*判断处理的事风险待办还是其他*/

const handlePartOrAllReq = async params => {
  if (riskType.value === 5) {
    delete params.type;
    await riskHandleEvent(params);
  } else {
    await riskHandlePartEvent(params);
  }
};

/** 调药 */
const loadingDrugTableData = ref(false);
const imStore = store.useIM();
const chooseDrugVisible = ref(false);
const drugTableData = ref<IApiDrugPatientCurrentDrugList[]>([]);
const getDrugPatientCurrents = async () => {
  loadingDrugTableData.value = true;
  try {
    const res = await getDrugPatientCurrent({ patientId: globalStore.userId! });
    drugTableData.value =
      res.drugList?.map(v => {
        if (v.drugAmount) delete v.drugAmount['custom'];
        return v;
      }) || [];
  } finally {
    loadingDrugTableData.value = false;
  }
};
const handleAdjustDrug = async (obj: any) => {
  const { sourceType, sourceId } = props.data || {};
  try {
    const res = await getDrugPatientAdjust({
      ...obj,
      patientId: globalStore.userId!,
      sourceId,
      sourceType,
      riskType: riksTypeMap[riskType.value],
    });
    if (obj.adjustDrugTrack) {
      // 跟踪用药需要发送药物调整消息卡片
      imStore.sendPatientCustomMsg({
        content: {
          name: '药物调整',
          id: res.drugInfoId,
          type: 7,
        },
      });
    }
    await treatmentMethodReq();
  } catch {
    ElMessage.error('调整用药失败!');
  } finally {
    chooseDrugVisible.value = false;
    setDialogVisible(false);
  }
};
</script>

<style scoped lang="less">
.handle-exceptions-wrapper {
  padding: 0 2px;
  height: calc(100vh - 162px);
  .treat-method {
    padding: 24px 56px;
    span {
      color: #7a8599;
    }
  }
  .el-button {
    width: 76px;
  }
}
.footer {
  height: 56px;
  border-top: 1px solid #e9e8eb;
}
</style>
