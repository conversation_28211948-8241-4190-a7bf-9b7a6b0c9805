<template>
  <el-config-provider :locale="zhCn">
    <div class="min-w-[1400px] h-screen max-w-screen" @click="close">
      <RouterView />
    </div>
  </el-config-provider>
</template>
<script setup lang="ts">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import bus from '@/lib/bus';
import { TOKEN_KEY } from '@/constant/cache';
let close = (e: any) => {
  try {
    if (!e.target.className.includes('operation-popup')) {
      bus.emit('close-operation-popup');
    }
  } catch {
    bus.emit('close-operation-popup');
  }
};

onMounted(() => {
  // 监听本地缓存变化， 其他页面如果重新登录， 自动刷新当前页面
  window.addEventListener('storage', val => {
    if (val.key === TOKEN_KEY && val.newValue) {
      window.location.reload();
    }
  });
});
</script>
