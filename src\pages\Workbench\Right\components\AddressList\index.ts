// 计算通话时长
export const calculateCallDuration = (startTime: number, endTime: number) => {
  const time = endTime - startTime;
  const minutes = Math.floor(time / 60);
  const remainingSeconds = time % 60;
  return minutes + '分' + remainingSeconds + '秒';
};

export const calculateBridgeDuration = (times: number) => {
  const minutes = Math.floor(times / 60);
  const remainingSeconds = times % 60;
  return minutes + '分' + remainingSeconds + '秒';
};

// 将分钟转为 分、秒
export const convertTime = (time: number) => {
  const minutes = Math.floor(time / 60);
  const remainingSeconds = time % 60;
  return minutes + '分' + remainingSeconds + '秒';
};
