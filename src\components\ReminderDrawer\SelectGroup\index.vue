<template>
  <div class="header-middle ml-16">
    <div class="select-container">
      <el-cascader
        v-model="selectData.caseValue"
        style="width: 240px"
        :options="options"
      />
      <el-select
        v-model="selectData.selectValue"
        placeholder="请选择"
        style="width: 120px; margin-left: 12px"
      >
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div class="reset-btn" @click="resetParams">
        <img src="@/assets/imgs/reminder/reset.png" alt="" />
        重置
      </div>
    </div>
    <div class="check-container">
      <el-checkbox
        v-model="selectData.onlyLookMain"
        label="仅看我的"
        :true-value="1"
        :false-value="0"
        class="look-me"
        @change="changeOnlyMine"
      />
      <div class="refresh-btn" @click="refresh">
        <el-icon :size="14">
          <i-ep-Refresh />
        </el-icon>
        刷新
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { debounce } from 'lodash-es';
import {
  getReminderListIOptions,
  getOnlyMineStatus,
  postOnlyMineStatus,
} from '@/api/reminder';

let userAccount = localStorage.getItem('userAccount');

let options = ref([]);
const selectOptions = [
  {
    value: 0,
    label: '全部',
  },
  {
    value: 1,
    label: '未处理',
  },
  {
    value: 3,
    label: '已逾期',
  },
  {
    value: 2,
    label: '已处理',
  },
  {
    value: 4,
    label: '已超期',
  },
];

const emit = defineEmits(['paramsChange']);

//重置
const resetParams = () => {
  selectData.caseValue = [{ pid: null, name: '全部', type: null }];
  selectData.selectValue = 0;
};
const getReminderListIOptionsReq = () => {
  getReminderListIOptions({ code: 2 }).then(data => {
    options.value = [
      {
        label: '全部',
        value: {
          pid: null,
          name: '全部',
          type: null,
        },
        children: [],
      },
      ...arrayToTreeOptions(data),
    ];
  });
};

const getOnlyMineStatusReq = debounce(() => {
  getOnlyMineStatus({ account: userAccount, type: 2 }).then(res => {
    if (res.code === 'E000000') {
      localStorage.setItem('onlyMineStatus', res.data);
      selectData.onlyLookMain = res.data;
    }
  });
}, 100);

const changeOnlyMine = () => {
  postOnlyMineStatus({
    account: userAccount,
    type: 2,
    onlyMine: selectData.onlyLookMain,
  }).then(res => {
    if (res.code === 'E000000') {
      getOnlyMineStatusReq();
    }
  });
};

const arrayToTreeOptions = items => {
  const map = {};
  const roots = [];
  items.forEach(item => {
    map[item.id] = {
      ...item,
      label: item.name,
      value: { pid: item.pid, name: item.name, type: item.type },
      children: [],
    };
  });

  items.forEach(item => {
    const pid = item.pid;
    if (pid === null) {
      roots.push(map[item.id]);
    } else {
      if (map[pid]) {
        map[pid].children.push(map[item.id]);
      }
    }
  });

  return roots;
};

//刷新
const refresh = async () => {
  await getOnlyMineStatusReq();
  let selectDataParams = {
    options: selectData.caseValue,
    onlyMine: selectData.onlyLookMain,
    status: selectData.selectValue,
  };
  emit('paramsChange', selectDataParams);
};

const selectData = reactive({
  caseValue: [{ pid: null, name: '全部', type: null }],
  onlyLookMain: parseInt(localStorage.getItem('onlyMineStatus')),
  selectValue: 0,
});

watch(
  () => selectData,
  async () => {
    await getOnlyMineStatusReq();
    let selectDataParams = {
      options: selectData.caseValue,
      onlyMine: selectData.onlyLookMain,
      status: selectData.selectValue,
    };
    emit('paramsChange', selectDataParams);
  },
  {
    deep: true,
  }
);

onMounted(() => {
  getReminderListIOptionsReq();
});
</script>

<style scoped lang="less">
.header-middle {
  :deep(.check-container) {
    .look-me {
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background: #2e6be6;
        border-color: #2e6be6;
      }
    }
  }
  .select-container {
    box-sizing: border-box;
    padding-top: 16px;
    display: flex;
    align-items: center;
    .reset-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #2e6be6;
      cursor: pointer;
      margin-left: 16px;
      img {
        margin-right: 4px;
        width: 14px;
        height: 14px;
      }
    }
  }
  .check-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    .refresh-btn {
      margin-right: 16px;
      color: #2e6be6;
      cursor: pointer;
    }
  }
}
</style>
