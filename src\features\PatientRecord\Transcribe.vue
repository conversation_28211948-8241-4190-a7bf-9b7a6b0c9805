<template>
  <HrtDialog
    v-model="props.visible"
    :width="600"
    :height="640"
    :title="data.title"
    size="large"
    destroy-on-close
    @update:visible="close"
  >
    <div class="header h-44 instructions flex items-center">
      转录申请提交后，将在实习生端生成一条转录任务
    </div>
    <div class="content">
      <div class="title mb-4">转录段落</div>
      <div class="instructions mb-12">
        请在以下{{ data.name }}记录的段落中选择需转录内容，支持多选
      </div>
      <div class="table-box">
        <BaseTable
          ref="multipleTableRef"
          :data="data.content"
          :pagination="false"
          row-key="sectionType"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="28" />
          <el-table-column prop="checkName" label="全选">
            <template #default="{ row }">
              <span
                class="text-base tracking-wider cursor-pointer"
                @click="toggleSelection(row)"
              >
                {{ row.checkName }}
              </span>
            </template>
          </el-table-column>
        </BaseTable>
      </div>
      <div class="title mb-4 mt-24">申请理由</div>
      <div class="check-box">
        <el-checkbox-group v-model="checkList" @change="changeArgument">
          <el-checkbox
            v-for="item in checkSelectList"
            :key="item.id"
            :label="item.id"
          >
            {{ item.title }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <el-input
        v-if="checkList.some(id => id === -1)"
        v-model="textarea"
        maxlength="100"
        placeholder="请输入理由，不超过100字"
        show-word-limit
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        resize="none"
        class="mt-6"
      />
    </div>
    <template #footer>
      <div class="footer p-sm pr-lg border-t flex-c">
        <div class="text-sm text-sub-text pr-sm">
          申请提交后将立即生效； 不可重复提交转录申请。
        </div>
        <el-button @click="close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submit">
          确定
        </el-button>
      </div>
    </template>
  </HrtDialog>
</template>
<script setup lang="ts">
import { PropType } from 'vue';
import { HrtDialog } from '@hrt/components';
import BaseTable from '@/components/BaseTable';
import { IRecordSourceType } from '@/store/module/useComponentsTabAction';
import { ModulesType } from './hooks';
import { generateTranscription } from '@/api/intern';
import useGlobal from '@/store/module/useGlobal';
import bus from '@/lib/bus';

interface ITranscribe {
  title: string;
  name: string;
  content: {
    checkName: string;
    // 段落类型
    sectionType?: ModulesType;
  }[];
}
const props = defineProps({
  sourceType: {
    type: Number as PropType<IRecordSourceType>,
    default: 0,
  },
  sourceId: {
    type: [Number, String],
    default: 0,
  },
  visible: Boolean,
});
const emits = defineEmits<{ (e: 'update:visible', value: boolean): void }>();

const loading = ref(false);
const multipleSelection = ref<ITranscribe['content']>([]);
const handleSelectionChange = (val: ITranscribe['content']) => {
  multipleSelection.value = val;
};
const multipleTableRef = ref<InstanceType<typeof BaseTable>>();
const toggleSelection = (row?: any[]) => {
  if (row) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    multipleTableRef.value?.element?.toggleRowSelection(row);
  } else {
    multipleTableRef.value?.element?.clearSelection();
  }
};
const transcribeMap: Record<IRecordSourceType, ITranscribe> = {
  0: {
    title: '住院信息转录申请',
    name: '住院',
    content: [
      {
        checkName: '入院记录',
        sectionType: 1,
      },
      {
        checkName: '手术记录',
        sectionType: 2,
      },
      {
        checkName: '出院记录',
        sectionType: 3,
      },
      {
        checkName: '住院检查',
        sectionType: 6,
      },
    ],
  },
  1: {
    title: '门诊信息转录申请',
    name: '门诊',
    content: [
      {
        checkName: '门诊记录',
        sectionType: 4,
      },
      {
        checkName: '门诊处方',
        sectionType: 5,
      },
      {
        checkName: '门诊检查',
        sectionType: 6,
      },
    ],
  },
  2: {
    title: '复查信息转录申请',
    name: '复查',
    content: [
      {
        checkName: '复查项目',
        sectionType: 6,
      },
    ],
  },
  3: {
    title: '入组信息转录申请',
    name: '入组',
    content: [
      {
        checkName: '入院记录',
        sectionType: 1,
      },
      {
        checkName: '手术记录',
        sectionType: 2,
      },
      {
        checkName: '出院记录',
        sectionType: 3,
      },
      {
        checkName: '住院检查',
        sectionType: 6,
      },
    ],
  },
};
const data = computed(() => transcribeMap[props.sourceType]);

// 申请理由
const checkList = ref<number[]>([]);
const checkSelectList = [
  {
    id: 0,
    title: '记录新增',
  },
  {
    id: 1,
    title: '原有内容错误',
  },
  {
    id: 2,
    title: '资料补充',
  },
  {
    id: -1,
    title: '其他',
  },
];
const textarea = ref('');
const changeArgument = () => {
  if (!checkList.value.some(id => id === -1)) textarea.value = '';
};

const submit = async () => {
  let errMsg = '';
  if (!multipleSelection.value.length) {
    errMsg = '请选择转录段落！';
  } else if (!checkList.value.length) {
    errMsg = '请选择申请理由！';
  } else if (checkList.value.some(id => id === -1) && !textarea.value) {
    errMsg = '请填写其他原因！';
  }
  if (errMsg) {
    return ElMessage({
      message: errMsg,
      type: 'warning',
    });
  }

  try {
    loading.value = true;
    const { userInfo, currentRole, accountId } = useGlobal();
    const { patientId, patientName } = userInfo as any;
    await generateTranscription({
      patientId,
      patientName,
      userId: accountId,
      userRole: String(currentRole),
      sourceId: Number(props.sourceId),
      taskType: props.sourceType,
      taskSection: multipleSelection.value?.map(
        item => item.sectionType
      ) as number[],
      reason: checkList.value,
      remark: textarea.value || undefined,
      taskMethod: 0,
      sourceType: 0,
    });
    ElMessage.success('操作成功!');
    bus.emit('update-task-status');
  } finally {
    loading.value = false;
  }
  close();
};
const close = () => {
  loading.value = false;
  textarea.value = '';
  checkList.value = [];
  multipleSelection.value = [];
  emits('update:visible', false);
};
</script>
<style scoped lang="less">
.header {
  background: #f7f8fa;
  padding-left: 24px;
}
.instructions {
  font-size: 14px;
  color: #7a8599;
}
.content {
  padding: 16px 54px 0;
  .title {
    font-size: 14px;
    font-weight: 700;
    color: #101b25;
  }
  .table-box {
    .style {
      font-size: 14px;
      color: #3a4762;
      &.active {
        color: #e63746;
      }
    }
    :deep(.is-checked) {
      .el-checkbox__inner {
        background: #0a73e4;
        border-color: #0a73e4;
      }
    }
  }
}
.check-box {
  .el-checkbox {
    margin-right: 43px;
  }
}
.footer {
  .el-button {
    width: 76px;
  }
}
</style>
