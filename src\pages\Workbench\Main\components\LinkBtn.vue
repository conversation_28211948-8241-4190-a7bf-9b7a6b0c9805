<template>
  <el-link
    :class="[
      { '!text-primary': type === 'primary' },
      { '!text-danger': type === 'danger' },
      'hover:opacity-80',
      '!text-sm',
    ]"
    :underline="false"
  >
    <slot></slot>
  </el-link>
</template>
<script setup lang="ts">
import { PropType } from 'vue';

defineProps({
  type: {
    type: String as PropType<'primary' | 'danger'>,
    default: 'primary',
  },
});
</script>
