import { RouteRecordRaw } from 'vue-router';
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Index',
    redirect: '/login',
  },
  {
    path: '/login',
    name: 'Login',
    meta: {
      title: '登录',
    },
    component: () => import('@/pages/Login/index.vue'),
  },
  {
    path: '/workbench',
    name: 'Workbench',
    meta: {
      role: [1, 2, 3],
      title: '首页',
    },
    component: () => import('../pages/Workbench/index.vue'),
  },
  {
    path: '/intern',
    name: 'Intern',
    meta: {
      role: [4],
      title: '实习生',
    },
    component: () => import('@/pages/Intern/index.vue'),
  },
  {
    path: '/:catchAll(.*)',
    redirect: '/login',
  },
];

export default routes;
