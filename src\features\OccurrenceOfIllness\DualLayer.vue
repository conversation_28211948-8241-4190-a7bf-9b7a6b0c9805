<!-- 用于展示双层模块（暂时只有检验检查）初始化时不会根据JSON Schema进行初始化，  -->
<!-- Schema中的items数据会用作后续的模块数据，当通过新增报告单按钮新增报告单时， -->
<!-- 会根据返回的值，对JSON Schema的items进行遍历，找出对应模块的数据进行初始化 -->
<script setup lang="ts">
import { saveCaseData } from '@/api';
import { removeReport } from '@/api/indicatorsReport';
import {
  FormCategory,
  FormCategoryValues,
  FormModeValues,
  SourceTypeValues,
} from '@/constant';
import CaseModule from '@/features/OccurrenceOfIllness/CaseModule.vue';
import SelectReport from '@/features/SelectReport/index.vue';
import { useHandleData } from '@/hooks';
import globalBus from '@/lib/bus';
import { HrtDialog } from '@hrt/components';
import { dynamicFormValidator } from '@/utils';
import { CirclePlus } from '@element-plus/icons-vue';
import { pick } from 'lodash-es';
import type { CSSProperties } from 'vue';
import { ref } from 'vue';

defineOptions({
  name: 'DualLayer',
});

const {
  module,
  mode,
  formData = [],
  cardStyle = {},
  sourceType,
  resourceId,
  additionalData,
  apiUrlMiddleware,
  schema,
  readonly = false,
  navPrefix,
} = defineProps<{
  module: any;
  mode: FormModeValues;
  formData: any[];
  cardStyle?: CSSProperties;
  sourceType: SourceTypeValues;
  /** 病例ID */
  resourceId?: number;
  /** 额外的查询参数 */
  queryParams?: Record<string, Record<string, unknown>>;
  /** 额外数据，用于保存模块数据时携带 */
  additionalData?: Partial<
    Record<FormCategoryValues | 'all', Record<string, any>>
  >; // 控制病例下具体展示哪些模块
  apiUrlMiddleware?: (key: string, mode: FormModeValues) => string; // 控制病例的状态
  schema: any;
  readonly?: boolean;
  navPrefix?: string;
}>();
const emit = defineEmits<{
  modeChange: [mode: FormModeValues];
  resourceIdChange: [resourceId: number];
  change: [data?: Record<string, any>];
  apiUrlMiddleware: [url: string];
  addNewItem: [id: number];
  deleteItem: [id: number];
  refreshList: [];
}>();

/** 新增报告单的弹窗显示控制 */
const reportVisible = ref(false);
/** 子模块状态, 用于保存每个子模块的状态,key为子模块的🆔 */
const childrenModeMap = ref<Record<string, FormModeValues>>({});
const dualLayerData = ref<Record<string, any>>({});

/** 二级模块的JSON Schema列表 */
const jsonSchemaList = computed(() => schema?.items || []);

watch(
  () => formData,
  val => {
    if (!val) return;
    for (const item of val) {
      if (item.id) {
        childrenModeMap.value[item.id] = mode;
        dualLayerData.value[item.id] = item;
      }
    }
  },
  {
    immediate: true,
  }
);

/** 渲染二级模块的JSON Schema列表 */
const renderJsonSchemaList = computed(() => {
  // 这里是根据接口返回的数据，数据中有哪一项就在JSON Schema列表中找到对应的项进行渲染
  return formData
    ?.map(item => {
      const schema = jsonSchemaList.value?.find(
        jsonSchema => jsonSchema.key === item.key
      );
      if (!schema) return null;
      return {
        ...schema,
        uiDisabled: false,
        id: item.id,
      };
    })
    .filter(Boolean);
});

function addReport() {
  reportVisible.value = true;
}

function handleReportUpdate(data: number) {
  reportVisible.value = false;
  emit('addNewItem', data);
  emit('resourceIdChange', data);
}

/**
 * 处理模块状态变更
 * @param id 模块的🆔
 * @param mode 模块的状态
 */
function handleModuleStateChange(id: number, mode: FormModeValues) {
  // 更新指定模块的状态
  childrenModeMap.value[id] = mode;
}

const handleModuleDelete = async item => {
  await useHandleData(
    removeReport,
    {
      ...pick(additionalData?.all, ['entry_task', 'sub_task_id']),
      reportId: item.id,
    },
    '是否确认删除？',
    '删除后不可撤回'
  );
  emit('deleteItem', item.id);
};

/**
 * 用于保存单个模块的数据，并在保存成功后将该模块切换为查看模式
 * @param id - 检查项🆔
 * @param key - 模块的唯一标识符
 * @returns
 */
async function handleModuleSave({
  id,
  key,
  item,
}: {
  id: number;
  key: string;
  item: any;
}) {
  try {
    if (!dynamicFormValidator(item, dualLayerData.value?.[id])) return;
    await saveCaseData({
      sourceId: resourceId,
      data: {
        ...(dualLayerData.value?.[id] || {}),
        ...(additionalData?.all || {}),
        ...(additionalData?.[key] || {}),
      },
      url: apiUrlMiddleware?.(key, mode) || `/api/case/history/${key}`,
    });
    if (resourceId !== undefined) {
      globalBus.emit('refresh-attachment', resourceId);
    }
    ElMessage.success('保存成功');
    // await getModuleDetails(id, key);
    emit('refreshList');
    handleModuleStateChange(id, 'view');
  } catch (error) {
    console.error(error);
    ElMessage.error('保存失败，请重试');
  }
}

function handleModuleDataChange(id: number, data: Record<string, any>) {
  if (data) {
    dualLayerData.value[id] = {
      ...(dualLayerData.value?.[id] || {}),
      ...data,
    };
  }
}

function handleModuleCancel(id: number) {
  handleModuleStateChange(id, 'view');
}
</script>

<template>
  <div
    v-if="module.key === 'diagnose_report'"
    :id="navPrefix ? `${navPrefix}${module?.schema.key}` : module?.schema.key"
    class="bg-white relative rounded-md py-16"
    style="box-shadow: 0 1px 2px 0 rgba(186, 200, 212, 0.5)"
  >
    <div class="flex items-center pr-16 mb-12">
      <div class="w-6 h-16 bg-[#2E6BE6] mr-10 rounded-sm"></div>
      <h3 class="m-0 text-base font-bold text-[#101B25]">
        {{ module?.schema.value }}
      </h3>
    </div>
    <template v-for="item in renderJsonSchemaList" :key="item.key">
      <CaseModule
        :nav-prefix="navPrefix + item.id"
        :readonly="readonly"
        type="second"
        :schema="item"
        :mode="childrenModeMap[item.id]"
        :data="dualLayerData?.[item.id]"
        :card-style="cardStyle"
        :on-save="() => handleModuleSave({ id: item.id, key: item.key, item })"
        :on-delete="() => handleModuleDelete(item)"
        @mode-change="curMode => handleModuleStateChange(item.id, curMode)"
        @change="data => handleModuleDataChange(item.id, data)"
        @close="() => handleModuleCancel(item.id)"
      />
    </template>
    <el-button
      v-if="module.schema?.key === FormCategory.DIAGNOSE_REPORT && !readonly"
      class="mt-4xl ml-16"
      type="primary"
      link
      @click="addReport"
    >
      <CirclePlus class="mr-3xs w-20" />
      新增报告单
    </el-button>
    <HrtDialog
      v-if="reportVisible"
      v-model="reportVisible"
      title="新增报告单"
      size="large"
      :width="600"
    >
      <SelectReport
        :source-type="sourceType"
        :source-id="resourceId"
        :additional-params="{
          ...(additionalData?.all || {}),
          ...(additionalData?.[FormCategory.DIAGNOSE_REPORT] || {}),
        }"
        @handle-update="handleReportUpdate"
        @handle-close="reportVisible = false"
      />
    </HrtDialog>
  </div>
</template>
