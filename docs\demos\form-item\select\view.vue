<script setup lang="ts">
import { ref } from 'vue';
import { Radio } from 'element-plus';
import Select from '@/components/FormItem/Select.vue';

const options = ref([
  {
    value: 1,
    label: '胸痛',
  },
  {
    value: 2,
    label: '头痛',
  },
  {
    value: 3,
    label: '呼吸困难',
  },
  {
    value: 4,
    label: '眼胀眼痛',
  },
  {
    value: 5,
    label: '晕倒晕厥',
  },
  {
    value: 6,
    label: '大汗淋漓',
  },
]);
const value = ref([1, 3, 2]);
const mode = ref<'create' | 'edit' | 'view'>('create');
</script>

<template>
  <div class="flex flex-col gap-16">
    <el-radio-group v-model="mode">
      <el-radio value="create">创建</el-radio>
      <el-radio value="edit">编辑</el-radio>
      <el-radio value="view">查看</el-radio>
    </el-radio-group>
    <Select v-model="value" multiple :options="options" :mode="mode" />
  </div>
</template>

<style scoped lang="less"></style>
