// 待办列表
// 待办类型(1患者入组、2入组宣教、3复查提醒、4风险处理、5症状异常处理、6调药跟踪、7门诊跟踪、8住院跟踪、9随访提醒、10自定义待办),
export const todoList = ref([
  {
    title: '患者入组',
    type: 1,
  },
  {
    title: '入组宣教',
    type: 2,
  },
  {
    title: '复查提醒',
    type: 3,
  },
  {
    title: '风险处理',
    type: 4,
  },
  {
    title: '症状异常处理',
    type: 5,
  },
  {
    title: '调药跟踪',
    type: 6,
  },
  {
    title: '门诊跟踪',
    type: 7,
  },
  {
    title: '住院跟踪',
    type: 8,
  },
  {
    title: '症状随访提醒',
    type: 9,
  },
  {
    title: '自定义待办',
    type: 10,
  },
  {
    title: '生活方式随访提醒',
    type: 11,
  },
  {
    title: '咨询回复',
    type: 12,
  },
  {
    title: '咨询回复',
    type: 12,
  },
  {
    title: '复查结论填写',
    type: 13,
  },
  {
    title: '临床事件跟踪',
    type: 14,
  },
  {
    title: '临床事件核实',
    type: 15,
  },
  {
    title: '量表随访提醒',
    type: 16,
  },
  {
    title: '访视质疑',
    type: 17,
  },
  {
    title: '科研患者加入',
    type: 18,
  },
  {
    title: '科研入组宣教',
    type: 19,
  },
]);

export const todoType = [
  'PATIENT_ACCESS',
  'ACCESS_TEACH',
  'REVIEW_REMINDER',
  'RISK_HANDLE',
  'SYMPTOM_HANDLE',
  'DRUG_TRACK',
  'OUTPATIENT_TRACK',
  'BE_HOSPITAL_TRACK',
  'SYMPTOM_FOLLOW_UP',
  'CUSTOM_BACKLOG',
  'LIFESTYLE_FOLLOW_UP',
  'CONSULT_REPLY',
  'REVIEW_CONCLUSION_FILL',
  'CLINIC_EVENT_TRACK',
  'CLINIC_EVENT_VERIFY',
  'SCALES_FOLLOW_UP',
  'VISITS_DOUBT',
  'SCIENTIFIC_RESEARCH_PATIENT_ACCESS',
  'SCIENTIFIC_RESEARCH_ACCESS_TEACH',
];

// 待办类型
interface titleInfo {
  type: number;
  title: string;
}
export const todoTitle = ref<titleInfo[]>([
  {
    type: 1,
    title: '添加自定义待办',
  },
  {
    type: 2,
    title: '稍后提醒设置',
  },
  {
    type: 3,
    title: '入组宣教',
  },
  {
    type: 4,
    title: '复查提醒',
  },
  {
    type: 5,
    title: '处理意见',
  },
  {
    type: 6,
    title: '调药跟踪',
  },
  {
    type: 7,
    title: '门诊跟踪',
  },
  {
    type: 8,
    title: '住院跟踪',
  },
  {
    type: 9,
    title: '症状随访提醒',
  },
  {
    type: 10,
    title: '监测提醒',
  },
  {
    type: 11,
    title: '生活方式随访提醒',
  },
]);

// 弹窗提醒
export const dialogTip = (
  msg: string,
  type?: 'success' | 'warning' | 'info' | 'error'
) => {
  return ElMessage({
    message: msg || '请稍等！',
    type: type || 'warning',
    showClose: true,
  });
};

// 获取用户角色类型
export const getUserRoles = () => {
  const item = JSON.parse(localStorage.getItem('userRoles') ?? '{}')[0];

  // 1医生、2健康管理师、3运动康复师
  const str =
    item === 'ASSISTANT'
      ? 1
      : item === 'CUSTOMER_SERVER'
        ? 2
        : item === 'REHAB'
          ? 3
          : 0;

  return str;
};

// 获取待办类型
export const getType = (type: number) => {
  let str = '';
  todoList.value.forEach(item => {
    if (item.type === type) str = item.title;
  });

  return str;
};

// 将标准时间转为 年月日 时分秒
export const convertDateTime = (dateTimeString: string) => {
  const dateTime = new Date(dateTimeString);
  const year = dateTime.getFullYear();
  let month: any = dateTime.getMonth() + 1; // getMonth() 返回的月份从0开始
  let day: any = dateTime.getDate();
  let hours: any = dateTime.getHours();
  let minutes: any = dateTime.getMinutes();

  // 将月份、日期、小时、分钟和秒转换为两位数格式，如果不足两位则前面补零
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  hours = hours < 10 ? '0' + hours : hours;
  minutes = minutes < 10 ? '0' + minutes : minutes;

  // 返回转换后的年月日时分秒格式
  return {
    integrityTime: `${year}-${month}-${day} ${hours}:${minutes}`,
    sectionTime: `${hours}:${minutes}`,
  };
};

// 处理待办时间显示格式
export const getTime = (dateTimeString: string) => {
  // 要显示的字段
  let showTime = '';
  // 将指定时间转为时间戳
  const appointTimeTimestamp = new Date(dateTimeString).getTime();
  // 获取当前时间的时间戳
  const currentTimeTimestamp = new Date().getTime();
  // 如果待办时间小于当前时间，则为已过期，否则为未过期
  if (appointTimeTimestamp < currentTimeTimestamp) {
    const differenceDays =
      (currentTimeTimestamp - appointTimeTimestamp) / 86400000;
    // 如果相差时间超过一天，则显示一天，如果小于一天则大于0天显示小时
    if (differenceDays > 1) {
      showTime = `（已逾期${Math.floor(differenceDays)}天）`;
    } else if (differenceDays > 0 && differenceDays < 1) {
      showTime = '（已逾期）';
    }
  } else {
    // 获取当天零时零分零秒的时间戳
    const now = new Date();
    const midnight = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      0,
      0,
      0,
      0
    );
    const time = midnight.getTime();
    // 获取待办期限与 time 相差的天数
    const differenceDays = (appointTimeTimestamp - time) / 86400000;
    // 如果小于1就显示今天，大于1小于2就显示明天，其余时间不显示
    if (differenceDays < 1) {
      showTime = `（今天${convertDateTime(dateTimeString).sectionTime}）`;
    } else if (differenceDays > 1 && differenceDays < 2) {
      showTime = `（明天${convertDateTime(dateTimeString).sectionTime}）`;
    }
  }
  return showTime;
};

/*1:血压 2:心率 3:血糖 4:体重 5全部展示*/
export const riskTypes = [1, 2, 3, 4, 5];

export const riskTabTitleMap = {
  1: '处理血压异常',
  2: '处理心率异常',
  3: '处理血糖异常',
  4: '处理体重异常',
  5: '批量处理异常',
};

export const riskIndexMap = {
  BLOOD_PRESSURE: 1,
  HEART_RATE: 2,
  BLOOD_SUGAR: 3,
  WEIGHT: 4,
};

export const getRiskType = content => {
  if (content.includes('血压')) return 1;
  if (content.includes('心率')) return 2;
  if (content.includes('血糖')) return 3;
  if (content.includes('体重')) return 4;
};
