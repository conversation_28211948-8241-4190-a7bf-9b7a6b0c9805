<template>
  <div class="index">
    <div class="check-box">
      <el-checkbox v-model="checked" label="选择" size="large" />
    </div>
    <div class="main mt-16">
      <div class="date-box flex items-center pb-sm">
        <div class="time-title">时间：</div>
        <div class="relative">
          <el-date-picker
            v-model="time"
            type="daterange"
            placeholder="选择日期"
            class="datepicker"
            value-format="x"
            format="YYYY-MM-DD"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :shortcuts="shortcuts"
            :clearable="false"
            @change="changeTime"
          />
          <img :src="changeTimeIng" alt="" class="w-14 h-14 change-time-icon" />
        </div>
      </div>
      <div class="tips">选择指标<span>（最多可选7项指标）</span></div>
      <div class="check-list-box flex items-center flex-wrap mt-12">
        <template v-for="item in checkList" :key="item.indexTermId">
          <el-button
            class="item mr-8 mb-8 cursor-pointer"
            :class="{ itemActive: item.flag }"
            :disabled="item.chooseStatus === 1"
            @click="changeIndex(item)"
          >
            <span v-if="item.alias"
              >{{ item.alias }}({{
                item.name === '葡萄糖' ? '血糖' : item.name
              }})</span
            >
            <span v-else>{{
              item.name === '葡萄糖' ? '血糖' : item.name
            }}</span>
          </el-button>
        </template>
      </div>
      <el-scrollbar>
        <div class="index-box">
          <div class="change-data-box flex mt-8">
            <div
              v-for="item in checkedList"
              :key="item.indexTermId"
              class="item-change cursor-pointer"
              :class="{ 'item-change-active': item.indexTermId === indexId }"
              @click="changeChecked(item)"
            >
              <span v-if="item.alias"
                >{{ item.alias }}({{
                  item.name === '葡萄糖' ? '血糖' : item.name
                }})</span
              >
              <span v-else>{{
                item.name === '葡萄糖' ? '血糖' : item.name
              }}</span>
              <div
                class="detele-index w-16 h-16 flex items-center justify-center"
                @click.stop="deteleIndex(item)"
              >
                <el-icon style="color: #fff; font-size: 12px">
                  <i-ep-Close />
                </el-icon>
              </div>
            </div>
          </div>
          <div class="hr"></div>
        </div>
      </el-scrollbar>
      <!-- echarts -->
      <Echarts :index-info="indexInfo" />
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  queryStructuredApi,
  queryStructuredDetailApi,
} from '@/api/managementSituation';
import useGlobal from '@/store/module/useGlobal';
import Echarts from './Echarts.vue';
import { shortcuts, timestampToDate } from '../../../hooks';
import changeTimeIng from '@/assets/imgs/callCenter/change-time.png';

const useGlobalInfo = useGlobal();

// 报告时间
const nowDate = Date.now();
const time = ref<number[]>([nowDate - 3600 * 1000 * 24 * 90, nowDate]);
const changeTime = (value: number[]) => {
  const [startTime, endTime] = value || [];
  queryInfo.value.startTime = startTime;
  queryInfo.value.endTime = endTime;
  time.value = [startTime, endTime];
  getAllIndexList();
};

// 查询数据参数
interface formInfo {
  patientId: number | undefined;
  startTime: number | string;
  endTime: number | string;
}
let queryInfo = ref<formInfo>({
  patientId: useGlobalInfo.userId,
  startTime: '',
  endTime: '',
});

// 是否选择
const checked = defineModel('checked');

// 获取所有指标项
let getAllIndexList = () => {
  queryStructuredApi(queryInfo.value).then((res: any) => {
    let arr: any = [];
    if (res.data && res.data.length) {
      res.data.forEach((item: any) => {
        arr.push({
          ...item,
          isInitial:
            item.indexTermId === 44 ||
            item.indexTermId === 45 ||
            item.indexTermId === 31,
          flag:
            item.indexTermId === 44 ||
            item.indexTermId === 45 ||
            item.indexTermId === 31,
        });
      });
    }
    checkList.value = arr;
  });
};
// 指标
let checkList = ref<any>([]);
// 选择指标
let changeIndex = (item: any) => {
  if (!item.flag && checkedList.value.length === 7) {
    ElMessage({
      message: '最多只能选7个！',
      type: 'warning',
    });
  } else {
    item.flag = !item.flag;
  }
};
// 已经选择的指标
let checkedList = computed(() => {
  let arr = checkList.value.filter((item: { flag: any }) => item.flag);
  let newArr = arr.sort((a: any, b: any) => {
    // 如果 a和b的isInitial都为true或都为false，则比较它们的顺序
    if (a.isInitial === b.isInitial) {
      return 0;
    }
    // 如果a的isInitial为true，而b的isInitial为false，则a应该在b之前
    if (a.isInitial) {
      return -1;
    }
    // 如果b的isInitial为true，而a的isInitial为false，则b应该在a之前
    return 1;
  });

  return newArr;
});

watch(checkedList, item => {
  changeChecked(item?.[0] || {});
});

// 查看指标
let indexId = ref(0);
let changeChecked = ({
  indexTermId,
  unit,
  checkType,
  indexType,
}: {
  indexTermId: number;
  unit: string;
  checkType: number;
  indexType: number;
}) => {
  indexId.value = indexTermId;
  if (!indexTermId) return;

  let form = {
    ...queryInfo.value,
    indexTermId: indexTermId,
    checkType: checkType,
    indexType: indexType,
  };
  getIndexDetails(form, unit);
};
// 删除选中的指标
let deteleIndex = (val: {
  flag: boolean;
  indexTermId: number;
  unit: string;
}) => {
  val.flag = false;
  let item = checkedList.value[0];
  if (!item) {
    indexInfo.value = {
      unit: '--',
      id: 0,
      xData: [],
      yData: [],
    };
    return;
  }
  let form = {
    ...queryInfo.value,
    indexTermId: item.indexTermId,
    checkType: item.checkType,
    indexType: item.indexType,
  };
  indexId.value = item.indexTermId;
  getIndexDetails(form, val.unit || '');
};
// 获取详情指标数据
let getIndexDetails = async (
  form: {
    indexTermId: number;
    checkType: number;
    indexType: number;
  },
  unit?: string
) => {
  await queryStructuredDetailApi(form).then((res: any) => {
    let xArr: any = [];
    let yArr: any = [];
    if (res.data && res.data.length) {
      xArr = res.data.map(item => {
        let time = timestampToDate(item.checkTime);
        return time;
      });
      if (form.indexTermId === 44) {
        let diastolicHigh = res.data.map(item => item.diastolicHigh || 0);
        let systolicHigh = res.data.map(item => item.systolicHigh || 0);
        yArr = [
          {
            name: '舒张压',
            color: '#0A73E4',
            data: systolicHigh,
          },
          {
            name: '收缩压',
            color: '#E58B48',
            data: diastolicHigh,
          },
        ];
      } else {
        let otherData = res.data.map(item => item.otherData);
        yArr = [otherData];
      }
    }
    indexInfo.value = {
      unit: unit || '--',
      id: form.indexTermId,
      xData: xArr,
      yData: yArr,
    };
  });
};

// 图表
let indexInfo = ref({
  unit: '--',
  id: 0,
  xData: [],
  yData: [],
});

onMounted(() => {
  const [startTime, endTime] = time.value || [];
  queryInfo.value.startTime = startTime;
  queryInfo.value.endTime = endTime;
  getAllIndexList();
});

defineExpose({
  checkedList,
  time,
});
</script>
<style lang="less" scoped>
.index {
  padding: 0 16px 16px;
  background: #ffffff;
  border-radius: 6px;
  .check-box {
    border-bottom: 1px solid #e9e8eb;
  }
  .date-box {
    :deep(.datepicker) {
      width: 360px;
      .el-range__icon {
        display: none;
      }
    }
    .change-time-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 8px;
    }
  }
  .main {
    .tips {
      font-size: 14px;
      color: #3a4762;
      span {
        color: #7a8599;
      }
    }
    .check-list-box {
      .item {
        padding: 6px;
        background: #ecf4fc;
        border-radius: 2px;
        font-size: 14px;
        color: #3a4762;
      }
      .itemActive {
        border: 1px solid #2e6be6;
        color: #2e6be6;
      }

      .el-button + .el-button {
        margin-left: 0px;
      }
    }
    .index-box {
      .hr {
        width: 100%;
        height: 1px;
        background: #e9e8eb;
        margin-top: -1px;
      }
      .change-data-box {
        .item-change {
          border: 1px solid #dcdee0;
          padding: 10px 28px;
          border-left: 0;
          white-space: nowrap;
          background: #f7f8fa;
          position: relative;
          .detele-index {
            background: #e63746;
            border-radius: 50%;
            position: absolute;
            top: -4px;
            right: -8px;
            z-index: 99;
          }
        }
        .item-change-active {
          border-bottom: 1px solid #fff;
          background: #fff;
        }
        .item-change:first-child {
          border-left: 1px solid #e9e8eb;
          border-top-left-radius: 4px;
        }
        .item-change:last-child {
          border-top-right-radius: 4px;
        }
      }
      .change-data-box::-webkit-scrollbar {
        /* 隐藏默认的滚动条 */
        -webkit-appearance: none;
      }
      .change-data-box::-webkit-scrollbar:vertical {
        /* 设置垂直滚动条宽度 */
        width: 0;
      }

      .change-data-box::-webkit-scrollbar:horizontal {
        /* 设置水平滚动条厚度 */
        height: 4px;
      }

      .change-data-box::-webkit-scrollbar-thumb {
        /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
        border-radius: 8px;
        border: 4px solid #cfcfcf;
        background: #cfcfcf;
      }
    }
  }
}
</style>
