import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import { formatTimeTemplate } from '@/utils';
import { TASK_TYPE_MAP } from '@/constant/intern';
import { FormCategory, FormCategoryValues, SourceTypeValues } from '@/constant';
/** 对应模块段落类型
 * 1:入院记录 2:手术记录 3:出院信息 4:门诊记录 5:门诊处方 6:检查项
 */
export type ModulesType = 1 | 2 | 3 | 4 | 5 | 6;
export interface IAction {
  visible: boolean;
  patientId: number;
  /** 单条任务Id(子任务) */
  taskId: number;
  /** 记录id */
  sourceId: number;
  /** 来源类型 0住院；1门诊；2复查；3入组*/
  sourceType: SourceTypeValues;
  /** 来源时间 */
  sourceTime: number | string;
  /** title */
  title: string;
  /** 组件段落类型 */
  modules: ModulesType[];
  illnessCards?: FormCategoryValues[];
  caseId: 0;
  /* 提交实习生任务id */
  submitSourceId?: number;
  /* 实习生提交识别id */
  internSubmitBatchId?: number;
}
const modulesMap = {
  1: FormCategory['ADMISSION_REPORT'],
  2: FormCategory['OPERATION_RECORD'],
  3: FormCategory['DISCHARGE_REPORT'],
  4: FormCategory['OUTPATIENT_RECORD'],
  5: FormCategory['OUTPATIENT_PRESCRIPTION'],
  6: FormCategory['DIAGNOSE_REPORT'],
};
/** 实习生端抽屉基础数据 */
export const useInternDrawer = defineStore(STORE_NAMES.Intern_Drawer, {
  state: (): IAction => ({
    visible: false,
    patientId: 0,
    sourceId: 0,
    sourceType: 0,
    taskId: 0,
    sourceTime: 0,
    title: '--',
    modules: [],
    illnessCards: [],
    caseId: 0,
    submitSourceId: 0,
    internSubmitBatchId: 0,
  }),
  actions: {
    setVisible(visible: boolean) {
      this.visible = visible;
    },
    async setBaseInfo({
      patientId,
      taskId,
      sourceId,
      sourceType,
      sourceTime,
      modules,
      caseId,
      submitSourceId,
      internSubmitBatchId,
    }: Omit<IAction, 'visible' | 'title' | 'illnessCards'>) {
      this.taskId = taskId;
      this.patientId = patientId;
      this.sourceType = sourceType;
      this.sourceId = sourceId;
      this.sourceTime = sourceTime;
      this.caseId = caseId;
      this.submitSourceId = submitSourceId;
      this.internSubmitBatchId = internSubmitBatchId;
      const time = formatTimeTemplate(sourceTime, 'YYYY-MM-DD') ?? '--';
      this.title = `${time}  ${TASK_TYPE_MAP[sourceType] + (sourceType === 3 ? '' : '记录')}`;

      this.modules = modules;
      this.illnessCards = modules?.map(item => modulesMap[item]) || [];
    },
  },
});

export default useInternDrawer;
