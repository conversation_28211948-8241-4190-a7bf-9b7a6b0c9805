<template>
  <div class="flex-1">
    <!-- 头部基本信息 -->
    <div class="sticky top-0 z-10">
      <Title />
      <ManageObjective />
    </div>
    <!-- 阶段性总结报告 -->
    <StructuredReport />
    <!-- 服务统计 -->
    <ServiceStatistics />
    <!-- 管理情况 -->
    <ManagementSituation />
    <!-- 服务信息 -->
    <ServiceInformation />
  </div>
</template>

<script setup lang="ts">
import Title from '../Title.vue';
import ManageObjective from '../ManageObjective.vue';
import StructuredReport from '../StructuredReport/index.vue';
import ServiceStatistics from '../ServiceStatistics/index.vue';
import ManagementSituation from '../ManagementSituation/index.vue';
import ServiceInformation from '../ServiceInformation/index.vue';
</script>
