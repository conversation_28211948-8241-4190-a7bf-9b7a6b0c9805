<template>
  <div class="double-block">
    <div class="form-block">
      <div class="form-label">
        <span class="required-icon">*</span>
        类型
      </div>
      <div class="form-content">
        <div class="select-box large-width">
          <el-select
            v-model="indexParams.sourceType"
            placeholder="请选择保存类型"
            @change="changeSaveType"
          >
            <el-option
              v-for="item in saveOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div class="form-block">
      <div class="form-label">
        <span class="required-icon">*</span>
        记录日期
      </div>
      <div class="form-content">
        <div class="select-box large-width">
          <el-select v-model="indexParams.sourceId" placeholder="请选择记录">
            <el-option
              v-for="(item, index) in timeOptions"
              :key="index"
              :label="item.name"
              :value="item.patientHistoryId"
            />
          </el-select>
        </div>
      </div>
    </div>
  </div>
  <div class="bottom-line"></div>
</template>
<script setup lang="ts">
import {
  getInHospitalRecords,
  getOutPatientHospitalRecords,
  getReviewRecords,
} from '@/api/ocr';
import { TypeCheckHeadParams, TypeRecordReqMap, TypeTimeOptions } from './type';
import useGlobal from '@/store/module/useGlobal';
import dayjs from 'dayjs';

const globalData = useGlobal();

const emit = defineEmits(['getHeadParams']);

const indexParams = ref<TypeCheckHeadParams>({
  sourceType: 0,
  sourceId: null,
});

const saveOptions = ref([
  {
    label: '住院',
    value: 0,
  },
  {
    label: '门诊',
    value: 1,
  },
  {
    label: '复查',
    value: 2,
  },
]);

const stateMap: { [key: string]: { type: string; label: string } } = {
  1: { type: 'info', label: '未开始' },
  32: { type: 'danger', label: '未上传' },
  64: { type: 'success', label: '已上传' },
  128: { type: 'info', label: '已完成' },
  '-1': { type: 'info', label: '已失效' },
};

const timeOptions = ref<TypeTimeOptions[]>([]);

const recordRequestMap: TypeRecordReqMap = {
  0: getInHospitalRecords,
  1: getOutPatientHospitalRecords,
  2: getReviewRecords,
};

const changeSaveType = () => {
  indexParams.value.sourceId = null;
  timeOptions.value = [];
  getRecordsList();
};

const getRecordsList = () => {
  recordRequestMap[indexParams.value.sourceType as number]({
    userId: globalData.userId,
  }).then(data => {
    if ((data as any).historyInfo) {
      if (indexParams.value.sourceType === 0) {
        (data as any).historyInfo.forEach((item: any) => {
          const { patientHistoryId, isGroup } = item;
          timeOptions.value.push({
            patientHistoryId: patientHistoryId,
            name:
              dayjs(item.inTime).format('YYYY-MM-DD') +
              '' +
              (isGroup ? ' 入组' : ' 入院'),
          });
        });
      }
      if (indexParams.value.sourceType === 1) {
        (data as any).historyInfo.forEach((item: any) => {
          const { patientHistoryId } = item;
          timeOptions.value.push({
            patientHistoryId: patientHistoryId,
            name: dayjs(item.inTime).format('YYYY-MM-DD') + '' + ' 门诊',
          });
        });
      }
    }
    if ((data as any).reviewInfo) {
      (data as any).reviewInfo.forEach((item: any) => {
        const { reviewId } = item;
        timeOptions.value.push({
          patientHistoryId: reviewId,
          name:
            (item.times === 0 ? '个性化复查' : `第${item.times}次复查`) +
            ' ' +
            dayjs(item.date).format('YYYY-MM-DD') +
            ' ' +
            stateMap[item.status || '-1'].label,
        });
      });
    }
  });
};

watch(
  () => indexParams,
  newVal => {
    emit('getHeadParams', newVal.value);
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  getRecordsList();
});
</script>
<style scoped lang="less">
.form-block {
  font-family:
    PingFangSC,
    PingFang SC;
  margin-bottom: 16px;
  width: 100%;
  .form-label {
    font-size: 14px;
    font-weight: bold;
    color: #111111;
    margin-bottom: 8px;
    .required-icon {
      font-weight: bold;
      color: #ea1212;
    }
  }
}
.double-block {
  display: flex;
  flex-wrap: wrap;
  width: 70%;
  .form-block {
    flex: 1;
  }
}

.select-box {
  :deep(.el-select) {
    border-radius: 2px;
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 2px;
    padding: 0px 11px;
  }
}
.large-width {
  :deep(.el-select) {
    width: 240px;
  }
}
.bottom-line {
  border-bottom: 1px solid #dcdee0;
}
</style>
