import { http } from '@/network';
import {
  IApiBacklogQueryOptionsItem,
  IApiBacklogQueryMarkParams,
  IApiBacklogRemindListParams,
  IApiBacklogRemindListBacklogResponseList,
  IApiBacklogRemindNumItem,
} from '@/interface/type';
/** 获取提醒事项筛选条件 */
export const getReminderListIOptions = (data: { code: number }) => {
  return http.post<IApiBacklogQueryOptionsItem>({
    url: '/api/backlog/query/options',
    data,
  });
};
/** 获取仅看我的状态 */
export const getOnlyMineStatus = (data: IApiBacklogQueryMarkParams) => {
  return http.post({
    url: '/api/backlog/query/mark',
    data,
    customConfig: { reductDataFormat: false },
  });
};
/** 修改看我的状态 */
export const postOnlyMineStatus = (data: IApiBacklogQueryMarkParams) => {
  return http.post({
    url: '/api/backlog/leave/mark',
    data,
    customConfig: { reductDataFormat: false },
  });
};
/** 获取提醒事项列表 */
export const getReminderList = (data: IApiBacklogRemindListParams) => {
  return http.post<IApiBacklogRemindListBacklogResponseList>({
    url: '/api/backlog/remind/list',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: true },
  });
};

export const getReminderCount = (data: IApiBacklogRemindListParams) => {
  return http.post<IApiBacklogRemindNumItem>({
    url: '/api/backlog/remind/num',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: true },
  });
};
