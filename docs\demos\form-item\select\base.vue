<script setup lang="ts">
import { ref } from 'vue';
import Select from '@/components/FormItem/Select.vue';

const options = ref([
  {
    value: 1,
    label: '胸痛',
  },
  {
    value: 2,
    label: '头痛',
  },
  {
    value: 3,
    label: '呼吸困难',
  },
  {
    value: 4,
    label: '眼胀眼痛',
  },
  {
    value: 5,
    label: '晕倒晕厥',
  },
  {
    value: 6,
    label: '大汗淋漓',
  },
]);

const options2 = ref(['选项1', '选项2', '选项3']);
const value = ref(1);
const value2 = ref('选项1');
</script>

<template>
  <div class="grid grid-cols-2 gap-8">
    <div class="flex flex-col gap-8">
      <div>当前值：{{ value }}</div>
      <Select v-model="value" :options="options" mode="edit" />
    </div>
    <div class="flex flex-col gap-8">
      <div>当前值：{{ value2 }}</div>
      <Select v-model="value2" :options="options2" mode="edit" />
    </div>
  </div>
</template>
