# Select 选择器

当选项过多时，使用下拉菜单展示并选择内容。

## 基础用法

适用广泛的基础单选 `v-model` 的值为当前被选中的 `options` 的 `value` 属性值，`options` 支持2种格式：

- 对象数组：`{ value: string | number; label: string; disabled?: boolean; }[]`
- 字符串数组：`string[]`

<demo vue="form-item/select/base.vue" scope="vue"></demo>

## 多选

设置 `multiple` 属性为 `true`，`v-model` 的值为被选中的 `options` 的 `value` 属性值组成的数组。
<demo vue="form-item/select/multiple.vue" scope="vue"></demo>

## 视图模式

设置 `mode` 属性为 `view`，`v-model` 的值为被选中的 `options` 的 `label` 属性值组成的字符串。
<demo vue="form-item/select/view.vue" scope="vue"></demo>

## Select API

| 参数          | 说明   | 类型                                                                              | 默认值     |
|-------------|------|---------------------------------------------------------------------------------|---------|
| v-model     | 绑定值  | `string \| number \| string[] \| number[]`                                      | -       |
| options     | 选项   | `{ value: string \| number; label: string; disabled?: boolean; }[] \| string[]` | `[]`    |
| placeholder | 占位符  | `string`                                                                        | -       |
| multiple    | 是否多选 | `boolean`                                                                       | `false` |
| mode        | 模式   | `view \| edit \| create`                                                        | -       |
