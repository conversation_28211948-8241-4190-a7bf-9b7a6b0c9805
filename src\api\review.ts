import { http } from '@/network';
import { IApiPatientReviewAddParams } from '@/interface/type';

// 全部报告项查询
export function getReportListApi() {
  return http.post({
    url: '/api/case/history/medicine/examination/key/list',
    method: 'post',
    data: {
      enCategory: false,
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取复查/诊疗时间轴数据
export function queryReviewTreatList(data: any) {
  return http.post({
    url: '/api/patient/review/treat/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 分页获取复查/诊疗全部数据
export function queryReviewTreatPage(data: any) {
  return http.post({
    url: '/api/patient/review/treat/page',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 添加自定义复查
export function addReviewApi(data: IApiPatientReviewAddParams) {
  return http.post({
    url: '/api/patient/review/add',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 主诉/现病史查询
export function querySuitApi() {
  return http.post({
    url: '/api/case/history/suit',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 查询既往史病历
export function diseaseListApi(data: any) {
  return http.post({
    url: '/api/case/history/disease',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 入院信息保存
export function addHospitalInfoApi(data: any) {
  return http.post({
    url: '/api/case/history/in/hospital/info',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者第一次一般情况调查问卷吸烟饮酒
export function queryFirstQuestionnaireApi(data: any) {
  return http.post({
    url: '/api/case/history/patient/questionnaire',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 入院信息详情
export function addHospitalDetailsApi(data: any) {
  return http.post({
    url: '/api/case/history/search/in/hospital',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 新增或修改出院信息
export function addDiagnosisLeaveApi(data: any) {
  return http.post({
    url: '/api/case/history/diagnosis/leave/hospital',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取系统药品列表
export function querySystemDrugListApi(data: any) {
  return http.post({
    url: '/api/drug/system/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 出院详情
export function queryLeaveHospitalDetails(data: any) {
  return http.post({
    url: '/api/case/history/search/leave/hospital',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 新增或修改手术信息
export function addDiagnosisSurgeryApi(data: any) {
  return http.post({
    url: '/api/case/history/operation',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询节段列表
export function querySegmentApi(data: any) {
  return http.post({
    url: '/api/case/history/segment',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 手术详情
export function queryOperationDetailsApi(data: any) {
  return http.get({
    url: '/api/case/history/operation',
    params: data,
    customConfig: { reductDataFormat: false },
  });
}

// 删除手术
export function deleteOperationApi(data: any) {
  return http.post({
    url: '/api/case/history/delete/surgery',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 确认入组
export function sureInGroupApi(data: any) {
  return http.post({
    url: '/api/case/history/in/group',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 删除复查
export function deleteReview(data: any) {
  return http.post({
    url: '/api/patient/review/delete',
    method: 'post',
    data,
  });
}
// 批量移动天数
export function reviewBatchModify(data: any) {
  return http.post({
    url: '/api/patient/review/batch/modify',
    method: 'post',
    data,
  });
}
// 获取调整复查计划列表
export function getReviewList(data: any) {
  return http.post({
    url: '/api/patient/review/adjust/list',
    method: 'post',
    data,
  });
}
// 修改复查详情
export function updateReviewInfo(data: any) {
  return http.post({
    url: '/api/patient/review/update/info',
    method: 'post',
    data,
  });
}
