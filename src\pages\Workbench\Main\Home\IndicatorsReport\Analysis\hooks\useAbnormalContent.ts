/** @description 异常文案查询 */
import {
  IApiIndexContentQuery,
  IApiIndexContentQueryParams,
} from '@/interface/type';
import { getIndexContentQuery } from '@/api/indicatorsReport';
import { isNumber } from 'lodash-es';

export interface RiskDescItemProps {
  name: string;
  max: string;
  min: string;
  maxAbnormal: boolean;
  minAbnormal: boolean;
  avg?: string | number;
  ttR?: string | number;
}
export interface RiskDescProps {
  // 类型
  title: string;
  // 频率
  frequency: string;
  // 测量次数
  num: number;
  // 异常次数
  errorNum: number;
  // 是否展示平均值
  showAvg?: boolean;
  // 详情
  itemList: RiskDescItemProps[];
}

/**
 * @description 二级分类下需要获取文案数据对应的ID
 * */
export const subIndexGetAbnormalContent: {
  checkType: number; // 一级分类ID
  indexType: number; // 二级分类ID
  title: string;
  frequency?: string; // 测量频率
}[] = [
  // 血脂全套 - 低密度脂蛋白胆固醇
  { checkType: 8, indexType: 4, title: '血脂', frequency: '一年' },
  // 血糖 - 葡萄糖
  { checkType: 10, indexType: 1, title: '血糖', frequency: '6月' },
];
export default function useAbnormalContent() {
  const riskDesc = ref<RiskDescProps | undefined>();

  async function getRiskDesc(
    reqParams: IApiIndexContentQueryParams,
    desc: { frequency?: string }
  ) {
    const res = await getIndexContentQuery(reqParams);
    if (!res) return;
    formatFinalData(res, { ...desc, id: reqParams.checkType });
  }

  function formatFinalData(
    data: IApiIndexContentQuery,
    extraInfo: { frequency?: string; id: number }
  ) {
    const {
      maxHigh = 0,
      minHigh = 0,
      avgHigh = 0,
      ttR = 0,
      nttR = 0,
      totalNum = 0,
      errorNum = 0,
      maxBloodFat,
      minBloodFat,
      avgLow = 0,
      maxLow = 0,
      minLow = 0,
      highHeart = 0,
      lowHeart = 0,
      avgHeart = 0,
    } = data;
    let item: RiskDescProps | undefined = undefined;

    const { frequency = '', id } = extraInfo;
    if (id === 44) {
      item = {
        title: '血压',
        errorNum,
        frequency,
        num: totalNum,
        showAvg: true,
        itemList: [
          {
            name: '1.收缩压',
            max: `${maxHigh}mmHg`,
            maxAbnormal: Boolean(data.maxHighPressureErrorLevel),
            min: `${minHigh}mmHg`,
            minAbnormal: Boolean(data.minHighPressureErrorLevel),
            avg: `${avgHigh}mmHg`,
            ttR,
          },
          {
            name: '2.舒张压',
            max: `${maxLow}mmHg`,
            maxAbnormal: Boolean(data.maxLowPressureErrorLevel),
            min: `${minLow}mmHg`,
            minAbnormal: Boolean(data.minLowPressureErrorLevel),
            avg: `${avgLow}mmHg`,
            ttR,
          },
        ],
      };
    }
    if (id === 45) {
      item = {
        title: '心率',
        errorNum,
        frequency,
        num: totalNum,
        showAvg: true,
        itemList: [
          {
            name: '心率',
            max: `${highHeart}次/min`,
            maxAbnormal: Boolean(data.maxHeartRateErrorLevel),
            min: `${lowHeart}次/min`,
            minAbnormal: Boolean(data.minHeartRateErrorLevel),
            avg: `${avgHeart}次/min`,
            ttR,
          },
        ],
      };
    }
    if (id === 10) {
      item = {
        title: '血糖',
        errorNum,
        frequency,
        num: totalNum,
        showAvg: true,
        itemList: [
          {
            name: '1.空腹血糖',
            max: `${maxHigh}mmol/l`,
            maxAbnormal: Boolean(data.maxFastingGiuErrorLevel),
            min: `${minHigh}mmol/l`,
            minAbnormal: Boolean(data.minFastingGiuErrorLevel),
            avg: `${avgHigh}mmol/l`,
            ttR,
          },
          {
            name: '2.非空腹血糖',
            max: `${maxLow}mmol/l`,
            maxAbnormal: Boolean(data.maxNonFastingGiuErrorLevel),
            min: `${minLow}mmol/l`,
            minAbnormal: Boolean(data.minNonFastingGiuErrorLevel),
            avg: `${avgLow}mmol/l`,
            ttR: nttR,
          },
        ],
      };
    }
    if (id === 8) {
      item = {
        title: '血脂',
        errorNum,
        frequency,
        num: totalNum,
        showAvg: false,
        itemList: [
          {
            name: 'LDL-c',
            max: `${isNumber(maxBloodFat) ? maxBloodFat : '--'}mmol/l`,
            maxAbnormal: Boolean(data.maxContentErrorLevel),
            min: `${isNumber(minBloodFat) ? minBloodFat : '--'}mmol/l`,
            minAbnormal: Boolean(data.minContentErrorLevel),
          },
        ],
      };
    }
    riskDesc.value = item;
  }

  return { riskDesc, getRiskDesc, formatFinalData };
}
