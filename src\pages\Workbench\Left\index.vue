<template>
  <div class="left flex flex-col w-[392px] relative bg-white">
    <Search />
    <div class="p-16 pb-0 flex-1">
      <el-tabs
        v-model="userListStore.activeTab"
        type="border-card"
        class="h-full"
        :class="{ ['length_' + curTabsConfig.length]: true }"
      >
        <el-tab-pane
          v-for="(tab, index) in curTabsConfig"
          :key="tab.name"
          :name="index"
          lazy
        >
          <template #label>
            <span class="title">{{ getTitle(index) }}</span>
            <span v-if="userListStore.tabCount[index]" class="badge">
              ({{ userListStore.tabCount[index] }})
            </span>
          </template>
          <div class="h-full">
            <Content :type="index" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="globalStore.isDoubting" class="doubt-mask"></div>
  </div>
</template>

<script setup lang="ts">
import Search from './components/Search/index.vue';
import Content from './components/Content/index.vue';
import store from '@/store';
import { getPatientInfoBase } from '@/api/overview';
import { listCountMap } from './hooks/config';
import useMeta from '@/store/module/useMeta';
defineOptions({
  name: 'WorkBenchLeft',
});
const globalStore = store.useGlobal();
const userListStore = store.useUserList();
const tabsConfig = ref({
  1: [
    { title: '我的患者' },
    { title: '复查解读' },
    //  { title: '症状随访' }
  ],
  2: [
    // { title: '临床事件跟踪' },
    { title: '复查跟踪' },
    { title: '近期随访' },
    { title: '患者列表' },
  ],
  3: [{ title: '患者列表' }, { title: '管理中' }],
});
const getTitle = (i: number) => {
  const role = store.useGlobal().currentRole;
  return tabsConfig.value[role as keyof typeof tabsConfig.value][i].title;
};
const curTabsConfig = computed(() => {
  const role = store.useGlobal().currentRole;
  return tabsConfig.value[role];
});
const getNum = async (i: number) => {
  const role = store.useGlobal().currentRole;
  const { url: req, params, paramsKey, resultKey } = listCountMap[role]?.[i];
  const advacnedParams = userListStore.getAdvancedFormatData();
  const newParams = {
    [paramsKey]: { ...advacnedParams, ...params[paramsKey] },
  };
  if (req) {
    const res: any = await req(newParams);
    if (res) {
      userListStore.tabCount[i] = res[resultKey] ?? 0;
    }
  }
};
const getNumHandler = () => {
  const restConfig = curTabsConfig.value.slice(1);
  for (let i = 0; i < restConfig.length; i++) {
    getNum(i + 1);
  }
};
onMounted(() => {
  getNumHandler();
});

const getPatientBaseInfo = async (id: number) => {
  const res: any = await getPatientInfoBase({ patientId: id });
  globalStore.setUserInfo(res);
};
watch(
  () => userListStore.advancedData,
  (newVal, oldVal) => {
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      if (!userListStore.tabRendered[1]) {
        getNumHandler();
      }
    }
  }
);
watch(
  () => globalStore.userId,
  val => {
    if (val) {
      getPatientBaseInfo(val);
      useMeta().getFollwUpOptions(val);
    } else {
      globalStore.setUserInfo({ patientName: '' });
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="less">
.left {
  :deep(.el-tabs__item) {
    width: 180px;
    .el-badge__content {
      top: 0;
      background: #dc0101;
    }
    &.is-active {
      border-right-color: transparent;
    }
    &:first-child {
      border-top-left-radius: 6px;
      &.is-active {
        border-right-color: inherit;
      }
    }
    &:last-child {
      border-top-right-radius: 6px;
    }
  }
  :deep(.el-tabs__header) {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    border: 1px solid #ccc;
    .title {
      font-size: 14px;
      color: #3a4762;
    }
    .badge {
      font-size: 16px;
      font-weight: bold;
      color: #2e6be6;
    }
    .is-active {
      .title {
        font-weight: bold;
        color: #3a4762;
      }
    }
  }
  :deep(.el-tabs--border-card) {
    border: 0;
  }
  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    padding: 0;
    padding-top: 8px;
    overflow: visible;
    .el-tab-pane {
      height: 100%;
    }
  }
  .length_3 {
    :deep(.el-tabs__item) {
      width: 120px;
    }
  }
}
.doubt-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 98;
  left: 0;
  top: 0;
  background: #f6f8fb;
  opacity: 0.5;
}
</style>
