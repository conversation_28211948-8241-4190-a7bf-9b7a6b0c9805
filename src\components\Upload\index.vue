<template>
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    v-loading="loading"
    class="h-upload"
    :multiple="props.multiple"
    :drag="props.drag"
    :limit="props.multiple ? props.limit : 1"
    :accept="fileTypes.length ? `.${fileTypes.join(',.')}` : ''"
    :list-type="props.listType"
    :http-request="httpRequest"
    :before-upload="beforeUpload"
    :on-exceed="onExceed"
    :on-success="onSuccess"
    :on-error="onError"
    :on-preview="onPreview"
    :on-remove="onRemove"
  >
    <div
      v-if="props.drag"
      class="flex items-center"
      :style="props.dragTriggerStyle"
    >
      <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
      <div class="ml-6 el-upload__text">
        {{ props.uploadBtnText || '点击上传，或将文件拖动到此区域' }}
      </div>
    </div>
    <el-button v-else type="primary">
      {{ props.uploadBtnText || '点击上传' }}
    </el-button>
    <template v-if="props.tip" #tip>
      <div class="el-upload__tip">
        {{ props.tip }}
      </div>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import {
  type UploadRawFile,
  type UploadUserFile,
  type UploadFile,
  type UploadRequestOptions,
  type UploadProgressEvent,
  formItemContextKey,
  genFileId,
} from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import * as qiniu from 'qiniu-js';
import { useLocalStorage } from '@vueuse/core';
import loadingify from '@/utils/loadingify';
import { QINIU_UPLOAD_CONFIG } from '@/constant';
import { getQiNiuVoucher } from '@/api/common';
import { getFileType, getFileName } from '@/utils';

export type IModelValue = (Omit<UploadUserFile, 'name'> & { name?: string })[];
interface IProps {
  modelValue?: IModelValue;
  // 是否支持多个文件
  multiple?: boolean;
  // 是否启用拖拽上传
  drag?: boolean;
  // 是否禁用上传
  disabled?: boolean;
  // 文件类型限制，如['jpg', 'jpeg', 'png', 'pdf']
  fileTypes?: string[];
  // 提示，可以用于提示文件限制格式或者文件限制大小
  tip?: string;
  // 上传按钮的文字
  uploadBtnText?: string;
  // 允许上传文件的最大数量
  limit?: number;
  // 允许单个上传文件的大小
  limitSize?: number;
  // 文件列表的类型
  listType?: 'text' | 'picture';
  // 启用拖拽上传时，上传容器的样式
  dragTriggerStyle?: { [key: string]: any };
}
interface IQiNiuVoucher {
  // 有效期
  expiresIn?: number;
  data?: string;
}

const { expiresInTime, url, urlQU } = QINIU_UPLOAD_CONFIG;
const PERSISTENT_URL = import.meta.env.MODE !== 'production' ? urlQU : url;
const formItem = inject(formItemContextKey);
const props = withDefaults(defineProps<IProps>(), {
  modelValue: () => [],
  multiple: true,
  drag: true,
  disabled: false,
  fileTypes: () => [],
  tip: '文件大小不超过50MB，最多上传10个文件',
  uploadBtnText: '',
  limit: 10,
  limitSize: 50 * 1024 * 1024,
  listType: 'text',
  dragTriggerStyle: () => ({}),
});
const emits = defineEmits(['update:model-value']);
// 把modalValue转换成UploadUserFile数据格式
const transModalValue = (value: IModelValue): UploadUserFile[] => {
  value.forEach(item => {
    if (!item.name) {
      item.name = item.url ? getFileName(item.url) : '';
    }
  });

  return value as UploadUserFile[];
};
const uploadRef = ref();
const fileList = ref<UploadUserFile[]>(transModalValue(props.modelValue));
const loading = ref<boolean>(false);

watch(
  () => props.modelValue,
  newValue => {
    fileList.value = transModalValue(newValue);
  }
);

const beforeUpload = (rawFile: UploadRawFile): boolean => {
  const fileType = getFileType(rawFile.name);

  // 判断文件上传类型
  if (props.fileTypes.length && !props.fileTypes.includes(fileType)) {
    ElMessage.error(`请上传${props.fileTypes.join('、')}格式的文件`);

    return false;
  }

  // 判断文件上传大小
  if (rawFile.size > props.limitSize) {
    ElMessage.error(`请上传${props.limitSize / (1024 * 1024)}MB以内的文件`);

    return false;
  }

  return true;
};
const onExceed = (files: File[]) => {
  if (props.multiple) {
    ElMessage.error(`最多上传${props.limit}个文件`);
  } else {
    const file = files[0] as UploadRawFile;

    file.uid = genFileId();
    uploadRef.value!.clearFiles();
    uploadRef.value!.handleStart(file);
    uploadRef.value!.submit();
  }
};
const onSuccess = (
  response: any,
  uploadFile: UploadFile,
  uploadFiles: UploadFile[]
) => {
  uploadFile.url = response;
  updateModelValue(uploadFiles);
};
const onError = (
  error: Error,
  uploadFile: UploadFile,
  uploadFiles: UploadFile[]
) => {
  updateModelValue(uploadFiles);
};
const onPreview = (uploadFile: UploadFile) => {
  if (uploadFile.url) {
    window.open(uploadFile.url, '_blank');
  }
};
const onRemove = (uploadFile: UploadFile, uploadFiles: UploadFile[]) => {
  updateModelValue(uploadFiles);
};
const updateModelValue = (uploadFiles: UploadFile[]) => {
  const hasLoadingFile = uploadFiles.some(item => item.status === 'uploading');

  if (!hasLoadingFile) {
    const successFiles: UploadFile[] = [];

    uploadFiles.forEach(item => {
      if (item.status === 'success' && item.url) {
        successFiles.push(item);
      }
    });
    emits('update:model-value', successFiles);
    formItem?.validate('change');
  }
};
// 获取上传凭证
const getVoucher = loadingify(async (forceGet = false) => {
  const voucher = useLocalStorage<IQiNiuVoucher>(
    QINIU_UPLOAD_CONFIG.QINIU_VOUCHER,
    {}
  ).value;
  let qiNiuVoucher = '';

  if (forceGet || !voucher?.expiresIn || Date.now() > voucher?.expiresIn) {
    loading.value = true;

    try {
      const res = await getQiNiuVoucher();

      qiNiuVoucher = res;
      useLocalStorage(QINIU_UPLOAD_CONFIG.QINIU_VOUCHER, {}).value = {
        expiresIn: Date.now() + expiresInTime - 300,
        data: res,
      };
    } finally {
      loading.value = false;
    }
  } else {
    qiNiuVoucher = voucher.data!;
  }

  return qiNiuVoucher;
});
const httpRequest = (options: UploadRequestOptions) => {
  return new Promise(async (resolve, reject) => {
    try {
      const token = await getVoucher();
      const file = options.file;
      const key = `${Date.now()}/${file.name}`;
      const putExtra = {
        // 文件原文件名
        fname: '',
        // 用来放置自定义变量
        params: {},
      };
      const config = {
        // 表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
        useCdnDomain: true,
        // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
        region: qiniu.region.z2,
      };
      const observable = qiniu.upload(file, key, token, putExtra, config);

      observable.subscribe({
        next(response) {
          // 上传进度
          const progressEvt = {} as UploadProgressEvent;

          progressEvt.percent = response.total.percent;
          options.onProgress(progressEvt);
        },
        error: errResult => {
          getVoucher(true);
          ElMessage.error('网络错误，请稍后重试');
          reject(errResult);
        },
        complete: ({ key }) => {
          resolve(`${PERSISTENT_URL}${key}`);
        },
      });
    } catch (error) {
      reject(error);
    }
  });
};
</script>

<style lang="less" scoped>
.h-upload {
  width: 100%;
  :deep(.el-upload-dragger) {
    padding: 0 11px;
    &.is-dragover {
      border-width: 1px;
    }
    .el-icon--upload {
      color: var(--el-text-color-secondary);
      font-size: 20px;
      line-height: 1;
      margin-bottom: 0;
    }
    .el-upload__text {
      color: var(--el-text-color-secondary);
      text-align: left;
    }
  }
  :deep(.el-upload__tip) {
    line-height: 16px;
    margin-top: 3px;
    color: var(--el-color-warning);
  }
  :deep(.el-icon--close-tip) {
    display: none !important;
  }
  :deep(.el-upload-list) {
    margin-top: 0;
    .el-upload-list__item {
      line-height: 1.428572;
      margin: 5px 0;
      &.is-uploading .el-upload-list__item-name {
        padding-right: 37px;
      }
      .el-progress {
        position: relative;
        top: 0;
        margin-top: 2px !important;
      }
      .el-progress__text {
        min-width: auto;
      }
    }
  }
  :deep(.el-upload-list--picture) {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .el-upload-list__item {
      width: auto;
      padding: 0;
      margin: 10px 10px 0 0;
      overflow: visible;
      border: none;
      border-radius: 5px;
    }
    .el-upload-list__item-thumbnail {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: inherit;
    }
    .el-upload-list__item-info {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 8;
      margin-left: 0;
      opacity: 0;
    }
    .el-icon--close {
      width: 16px;
      height: 16px;
      border-radius: 16px;
      background-color: var(--color-danger);
      top: -10px;
      right: -10px;
      font-size: 12px;
      color: #ffffff;
      opacity: 1;
      z-index: 9;
    }
    .el-upload-list__item-status-label {
      display: none;
    }
  }
}
</style>
