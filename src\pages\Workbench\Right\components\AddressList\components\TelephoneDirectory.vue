<template>
  <div>
    <HrtDialog
      v-model="isTelephone"
      title="通讯录"
      width="32%"
      size="extraLarge"
      class="telephone-box"
    >
      <span>
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{
            background: '#F7F8FA',
          }"
          height="220px"
        >
          <el-table-column prop="name" label="姓名">
            <template #default="scope">
              <div v-if="!scope.row.isEdit">
                {{ scope.row.name }}
              </div>
              <el-input
                v-else
                v-model="scope.row.name"
                placeholder="请输入姓名"
              />
            </template>
          </el-table-column>
          <el-table-column prop="relation" label="关系">
            <template #default="scope">
              <div v-if="!scope.row.isEdit">
                {{ scope.row.relation }}
              </div>
              <el-select
                v-else
                v-model="scope.row.relation"
                placeholder="请选择关系"
                :style="{ width: '128px' }"
              >
                <el-option
                  v-for="item in freeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="phoneNumber" label="手机号">
            <template #default="scope">
              <div v-if="!scope.row.isEdit">
                {{ scope.row.phoneNumber }}
              </div>
              <el-input
                v-else
                v-model="scope.row.phoneNumber"
                maxlength="11"
                placeholder="请输入手机号"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <div v-if="!scope.row.isEdit" class="flex items-center">
                <div class="mr-16">
                  <PhoneCall
                    :disabled-confirm="true"
                    :name="scope.row.name"
                    :need-clear="true"
                    :tel="scope.row.phoneNumber"
                  />
                </div>
                <div
                  v-if="scope.$index > 0"
                  class="cursor-pointer edit-box mr-16 text-sm"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </div>
                <div
                  v-if="scope.$index > 0"
                  class="cursor-pointer delete-box text-sm"
                  @click="handleDelete(scope.row, scope.$index)"
                >
                  删除
                </div>
              </div>
              <div v-else class="flex items-center">
                <div
                  class="flex items-center justify-center sure-box cursor-pointer text-sm mr-10 w-56 h-30"
                  @click="sure(scope.row)"
                >
                  确定
                </div>
                <div
                  class="flex items-center justify-center text-sm cursor-pointer cancel-box w-58 h-32"
                  @click="cancel(scope.row, scope.$index)"
                >
                  取消
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="add flex items-center mt-20">
          <img
            :src="addImg"
            alt=""
            class="w-14 h-14 cursor-pointer mt-2"
            @click="add"
          />
          <span class="ml-4 cursor-pointer add-box" @click="add">新增</span>
        </div>
      </span>
    </HrtDialog>
  </div>
</template>
<script setup lang="ts">
import addImg from '@/assets/imgs/callCenter/add.png';
import { patientList, updateApi } from '@/api/addressBook';
import bus from '@/lib/bus';
import { debounce } from 'lodash-es';
import { HrtDialog } from '@hrt/components';
import useGlobal from '@/store/module/useGlobal';
const useGlobalInfo = useGlobal();
import PhoneCall from '@/components/PhoneCall/index.vue';

import { inject } from 'vue';
const isTelephone = inject('isTelephone');
const freeOptions = [
  {
    value: '本人',
    label: '本人',
  },
  {
    value: '配偶',
    label: '配偶',
  },
  {
    value: '父母',
    label: '父母',
  },
  {
    value: '子女',
    label: '子女',
  },
  {
    value: '孙儿女',
    label: '孙儿女',
  },
  {
    value: '其他',
    label: '其他',
  },
];

interface bookInfo {
  name: string;
  relation: null | string;
  phoneNumber: string;
  isEdit: boolean;
  isNewAdd: boolean;
  addressBookId?: number | null;
}
const tableData = ref<bookInfo[]>([]);
// 获取数据
const getList = () => {
  patientList({ userId: useGlobalInfo.userId }).then((res: any) => {
    let arr = ref<bookInfo[]>([]);
    if (res.code === 'E000000' && res.data && res.data.addressBookList.length) {
      res.data.addressBookList.forEach(
        (item: {
          name: any;
          relation: any;
          phone: any;
          addressBookId: number | null;
        }) => {
          arr.value.push({
            name: item.name,
            relation: item.relation || '本人',
            phoneNumber: item.phone,
            addressBookId: item.addressBookId,
            isEdit: false,
            isNewAdd: false,
          });
        }
      );
    }
    tableData.value = arr.value;
  });
};

bus.on('get-address-books', () => {
  getList();
});

// 编辑
const handleEdit = (item: { isEdit: boolean }) => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    item.isEdit = true;
  }
};

// 删除
const handleDelete = (item: any, index: number) => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    ElMessageBox.confirm('确定要删除当前联系人吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        tableData.value.splice(index, 1);
        addBook();
      })
      .catch(() => {});
  }
};

// 确定
const sure = (item: {
  name: any;
  phoneNumber: any;
  relation: any;
  isEdit: boolean;
}) => {
  // let regex = /^1[3-9]\d{9}$/;
  if (!item.name || !item.phoneNumber || !item.relation) {
    ElMessage({
      message: '请填写完整！',
      type: 'warning',
    });
  } else if (!Number(item.phoneNumber)) {
    // || !regex.test(item.phoneNumber)
    ElMessage({
      showClose: true,
      message: '请输入正确的手机号！',
      type: 'error',
    });
  } else {
    addBook();
  }
};

// 新增api
interface addInfo {
  name: string;
  phone: string;
  relation: null | string;
  addressBookId: number | null;
}
const addressBookList = ref<addInfo[]>([]);
const addBook = debounce(() => {
  tableData.value.forEach((item, index) => {
    if (index) {
      addressBookList.value.push({
        name: item.name,
        phone: item.phoneNumber,
        relation: item.relation,
        addressBookId: item.addressBookId || null,
      });
    }
  });

  updateApi({
    userId: useGlobalInfo.userId,
    addressBookList: addressBookList.value,
  }).then(res => {
    if (res.code === 'E000000') {
      ElMessage({
        message: '操作成功！',
        type: 'success',
      });
      getList();
    } else {
      ElMessage({
        showClose: true,
        message: '操作失败！',
        type: 'error',
      });
    }
  });
}, 200);

// 取消
const cancel = (item: { isNewAdd: any }, index: number) => {
  //   如果是自己新增的点击取消就删除当前新增的数据
  if (item.isNewAdd) {
    tableData.value.splice(index, 1);
  } else {
    // 如果是数据库查出来的数据点击取消，就恢复到之前
    getList();
  }
};

// 新增
const add = () => {
  const flag = tableData.value.some(item => item.isEdit);
  if (flag) {
    ElMessage({
      message: '请先保存编辑中的联系人信息！',
      type: 'warning',
    });
  } else {
    tableData.value.push({
      name: '',
      relation: '',
      phoneNumber: '',
      isEdit: true,
      isNewAdd: true,
    });
  }
};
</script>
<style scoped lang="less">
:deep(.telephone-box) {
  .el-dialog__header {
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 700;
    color: #101b25;
    border-bottom: 2px solid #e9e8eb;
    margin-right: 0;
  }
  .el-dialog__headerbtn {
    top: 0;
    right: -6px;
  }
  .el-dialog__body {
    padding: 16px 24px;
    .add-box {
      font-size: 14px;
      color: #2e6be6;
    }
    .edit-box {
      color: #2e6be6;
    }
    .delete-box {
      color: #e63746;
    }
    .sure-box {
      background: #2e6be6;
      border-radius: 2px;
      color: #ffffff;
    }
    .cancel-box {
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
  }
}
</style>
