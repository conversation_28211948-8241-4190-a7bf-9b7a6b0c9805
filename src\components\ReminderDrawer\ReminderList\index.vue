<template>
  <div class="list-container">
    <div v-for="item in reminderList" :key="item" v-infinite-scroll="load">
      <div class="label">
        <ShowImg :type="item.type" />
        <span>{{ getType(item.type) }}</span>
        <span class="count">({{ item.children.length }})</span>
      </div>
      <div class="content-box">
        <ReminderCard
          v-for="toDoItem in item.children"
          :key="toDoItem.backlogId"
          ref="reminderCardRef"
          :to-do-obj="toDoItem"
          @get-operation-reminder="handleReminderCard"
          @handle-todo="handleTodo"
          @delete-todo="deleteTodo"
        />
      </div>
    </div>
    <!-- 相关待办弹窗组件 -->
    <Dialog
      v-if="dialogVisible"
      :width="width"
      :dialog-visible="dialogVisible"
      @close-dialog="closeDialog"
    />
    <!-- 处理自定义待办 -->
    <SecondaryConfirmation
      :custom-type="customType"
      :custom-title="customTitle"
      @close="closeCustom"
    />
  </div>
</template>

<script lang="ts" setup>
import ReminderCard from './ReminderCard.vue';
import Dialog from '@/pages/Workbench/Right/components/PatientTodo/components/Dialog.vue';
import SecondaryConfirmation from '@/pages/Workbench/Right/components/PatientTodo/components/SecondaryConfirmation.vue';
import ShowImg from '@/pages/Workbench/Right/components/PatientTodo/components/ShowImg.vue';
import bus from '@/lib/bus';
import useTodo from '@/store/module/useTodo';
import { useReminder } from '@/store/module/useReminder';
import store from '@/store';
import dayjs from 'dayjs';
import { getFollowSymptomDetail } from '@/api/followup';
import useDoubt from '@/store/module/useDoubt';
import {
  getType,
  getRiskType,
  riskTabTitleMap,
} from '@/pages/Workbench/Right/components/PatientTodo';
import { IApiPatientReportListData } from '@/interface/type';
import {
  IComponentType,
  IRecordSourceType,
} from '@/store/module/useComponentsTabAction';
import useTabs from '@/store/module/useTabs';

let useTodoInfo = useTodo();
const tabStore = useTabs();
const tabAction = store.useComponentsTabAction();
const emit = defineEmits(['delayReminder', 'loadMore', 'close']);

const reminderStore = useReminder();
const doubtStore = useDoubt();

const reminderList = computed(() => reminderStore.reminderList);

const reminderCardRef = ref(null);

const closeAllPopover = () => {
  if (reminderCardRef.value) {
    reminderCardRef.value.forEach(cardRef => {
      cardRef.closePopover();
    });
  }
};

const handleReminderCard = data => {
  emit('delayReminder', data);
};

let currentToDoItem = ref({});

const handleTodo = (item: any) => {
  setTimeout(() => {
    currentToDoItem.value = item;
    if ([1, 18].includes(item.type)) {
      let obj = {
        sourceId: item.sourceId,
        checkTime: item.content.slice(0, 10),
        actionType: !item.sourceId ? 'add' : 'view',
        sourceType: 3,
        name: '入组',
      };
      toTab(obj);
    } else if (item.type === 2) {
      useTodoInfo.setStatus(3);
      init(item);
    } else if (item.type === 3) {
      useTodoInfo.setStatus(4);
      init(item);
    } else if (item.type === 4) {
      tabAction.setAction({
        componentType: 4,
        name: riskTabTitleMap[getRiskType(item.content)],
        mode: 'new',
        mainTabCode: 2,
        data: {
          sourceId: item.sourceId,
          sourceType: 2,
          riskType: getRiskType(item.content),
        },
      });
      bus.emit('open-component-tab');
    } else if ([5, 15].includes(item.type)) {
      getFollowSymptomDetail({ followUpId: item.sourceId }).then(res => {
        let { date } = res;
        const followUpData = {
          followUpDate: date,
          followUpId: item.sourceId,
          sourceId: item.sourceId,
          sourceType: 1,
          key: item.sourceId,
        };
        tabAction.setAction({
          componentType: 6,
          name: dayjs(date).format('YYYY-MM-DD') + ' 症状随访',
          data: followUpData,
        });
        bus.emit('open-component-tab');
      });
    } else if (item.type === 9) {
      useTodoInfo.setStatus(9);
      init(item);
    } else if (item.type === 6) {
      useTodoInfo.setStatus(6);
      init(item);
    } else if (item.type === 7) {
      useTodoInfo.setStatus(7);
      init(item);
    } else if (item.type === 8) {
      useTodoInfo.setStatus(8);
      init(item);
    } else if (item.type === 10) {
      customType.value = true;
      customTitle.value = '完成';
      useTodoInfo.setStatus(10);
      init(item);
    } else if (item.type === 11) {
      useTodoInfo.setStatus(11);
      init(item);
    } else if (item.type === 12) {
      emit('delayReminder', { type: 12 });
    } else if (item.type === 13) {
      // 复查结论填写
      tabAction.setAction({
        componentType: 2,
        name: dayjs(item.time).format('YYYY-MM-DD') + ' 复查',
        mode: 'new',
        data: {
          id: item.sourceId,
          recordActionType: {
            actionType: 'view',
            sourceType: 2,
          },
        },
      });
      bus.emit('open-component-tab');
    } else if (item.type === 14) {
      // 临床事件跟踪 打开新建事件弹窗
      tabStore.clearCache();
      const group = 'patient_info';
      const curTab = tabStore.getCurrentPatientTabs(group);
      if (curTab) {
        const curId = curTab?.[1]?.id ?? '';
        if (curId) {
          tabStore.mainActiveTab = 2;
          tabStore.patientActiveTabMap[group] = curTab?.[1]?.id ?? '';
        }
      }
      setTimeout(() => {
        bus.emit('open-clinical-event');
      });
    } else if (item.type === 16) {
      // 量表随访提醒
      useTodoInfo.setStatus(999);
      init(item);
    } else if (item.type === 17) {
      doubtStore.backlogId = item.backlogId;
      doubtStore.doubtCheck(item.sourceId);
      // 访视质疑
      emit('close');
    } else if (item.type === 19) {
      // 科研入组宣教
      useTodoInfo.setStatus(999);
      init(item);
    }
  });
};

const deleteTodo = (item: any) => {
  useTodoInfo.setTodoInfo(item);
  customType.value = true;
  customTitle.value = '删除';
};
let init = (item: any) => {
  useTodoInfo.setTodoInfo(item);
  if (item.type !== 10) dialogVisible.value = true;
  if (item.type === 5) {
    width.value = '26%';
  } else {
    width.value = '21%';
  }
};

const toTab = (item: IApiPatientReportListData) => {
  const { sourceId, sourceType, checkTime, actionType } = item;
  const curSourceType = [0, 1, 2, 3].includes(sourceType!) ? sourceType! : -1;
  tabAction.setAction({
    componentType: curSourceType as IComponentType,
    name: `${checkTime || ''} ${item.name}`,
    data: {
      id: sourceId,
      recordActionType: {
        actionType,
        sourceType: curSourceType as IRecordSourceType,
      },
    },
  });
  bus.emit('open-component-tab');
};

const dialogVisible = ref<boolean>(false);
// 弹窗的宽度
let width = ref<string>('32%');

const closeDialog = () => {
  dialogVisible.value = false;
  emit('delayReminder', currentToDoItem.value);
};

let customType = ref<boolean>(false);

let customTitle = ref<string>('');

let closeCustom = value => {
  customType.value = false;
  if (value === 1) {
    emit('delayReminder', currentToDoItem.value);
  }
};

const load = () => {
  emit('loadMore');
};

defineExpose({
  closeAllPopover,
});
</script>

<style scoped lang="less">
.list-container {
  box-sizing: border-box;
  background-color: white;
  padding: 16px;
  .label {
    display: flex;
    align-items: center;
    img {
      width: 16px;
      height: 16px;
    }
    span {
      font-weight: bold;
      font-size: 16px;
      color: #15233f;
      margin-left: 4px;
    }
    .count {
      color: #2e6be6;
      margin-left: 12px;
    }
  }
  .content-box {
    padding: 12px 0;
  }
}
</style>
