import { IChats } from '@/store/module/useUserList';
import { CHAT_ROLE_MAP } from '@/constant/chat';
import { escapeHtml, escapeHilightChatRecords } from '@/utils';

export const getChatText = (chat?: IChats, escape = true) => {
  const chatType = chat?.chatType?.toLowerCase() ?? '';
  const senderUserName = chat?.sendUserName;
  const sendUserType = chat?.sendUserType;
  const curRole = (window as any)._currentRole;
  let content = '';
  if (['image', 'picture'].includes(chatType)) content = '[图片]';
  if (chatType === 'audio') content = '[语音]';
  if (chatType === 'video') content = '[视频]';
  if (chatType === 'custom') content = '[自定义]';
  if (chatType === 'notification') content = '[通知]';
  if (!content) {
    content = chat?.chatDetail ?? '';
  }
  if (escape) {
    content = escapeHtml(content);
  } else {
    content = escapeHilightChatRecords(content);
  }
  const _sendUserName =
    CHAT_ROLE_MAP[sendUserType as keyof typeof CHAT_ROLE_MAP] === curRole
      ? '我'
      : senderUserName;
  return _sendUserName ? _sendUserName + ': ' + content : content;
};
export const getAvatorName = (patientName: string = '') => {
  if (patientName) {
    if (patientName.codePointAt(0)! > 0xffff) {
      return patientName.slice(0, 2);
    }
    return patientName.slice(0, 1);
  }
  return '';
};

export const getHilightStr = (
  str: string,
  key: string = '',
  color = '#E63746'
) => {
  if (key === '') return str;
  try {
    const _key = key.replace(/[\\]/g, '\\$&');
    return str?.replace(new RegExp(_key), val => {
      return `<span style="color:${color}">${val}</span>`;
    });
  } catch (error) {}
};
