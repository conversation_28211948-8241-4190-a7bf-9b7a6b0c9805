import { getOcrAllLatestKeyword } from '@/api/ocr';
import { useQuery } from '@tanstack/vue-query';

/** 缓存时间:1天 */
const CACHE_TIME = 1000 * 60 * 60 * 24;

/**
 * 获取表单JSON Schema
 */
export const useFormJSONSchema = () => {
  return useQuery({
    queryKey: ['ocrAllLatestKeyword'],
    queryFn: () => getOcrAllLatestKeyword({}),
    gcTime: CACHE_TIME,
    staleTime: CACHE_TIME,
    retry: 5,
  });
};
