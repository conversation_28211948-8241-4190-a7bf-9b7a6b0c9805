// 时间快捷选择
export const shortcuts = [
  {
    text: '上周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '上个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近3个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 时间戳转为年月日时分秒
export function timestampToDateTime(timestamp: string | number | Date) {
  if (!timestamp) return '--';

  // 创建一个新的Date对象
  const date = new Date(timestamp);

  // 获取年、月、日、时、分、秒
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份是从0开始的，所以要加1
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  // 为了确保月份、日期、小时、分钟和秒都是两位数，我们使用padStart方法
  const paddedMonth = month.toString().padStart(2, '0');
  const paddedDay = day.toString().padStart(2, '0');
  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');
  const paddedSeconds = seconds.toString().padStart(2, '0');

  // 返回日期和时间字符串
  return `${year}-${paddedMonth}-${paddedDay} ${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
}

// 弹窗提醒
export const dialogTip = (msg: string, type = '') => {
  return ElMessage({
    message: msg,
    type: type || 'warning',
    showClose: true,
  } as any);
};

//判断订单是否生效
export function isOrderEffect(time: number | string) {
  const current = new Date().getTime();
  return current > Number(time);
}

// 将时间戳转为年月日
export function timestampToDate(
  timestamp: string | number | Date,
  symbol = '-'
) {
  if (!timestamp) return '--';

  const date = new Date(timestamp); // JavaScript Date对象需要的毫秒级时间戳
  const year = date.getFullYear();
  let month: any = date.getMonth() + 1; // getMonth()返回的月份从0开始，所以要+1
  let day: any = date.getDate();

  // 如果月份或日期小于10，前面补0
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;

  const dateStr = year + symbol + month + symbol + day;
  return dateStr;
}

export const medicineTimeList = [
  {
    value: 1,
    label: '早上',
  },
  {
    value: 2,
    label: '中午',
  },
  {
    value: 8,
    label: '下午',
  },
  {
    value: 3,
    label: '晚上',
  },
  {
    value: 4,
    label: '早中晚',
  },
  {
    value: 5,
    label: '早晚',
  },
  {
    value: 6,
    label: '午晚',
  },
  {
    value: 7,
    label: '早午',
  },
];

//处理药品规格
export function getDrugSpecStr(obj: {
  ingredients: any;
  contentUnit: any;
  packageNum: string;
  unit: string;
  packageUnit: string;
}) {
  const ingredients = obj.ingredients + obj.contentUnit;
  const packageContent = obj.packageNum ? '*' + obj.packageNum + obj.unit : '';
  const packageUnit = obj.packageUnit ? '/' + obj.packageUnit : '';
  return ingredients + packageContent + packageUnit;
}

// 获取统计次数
export function getStatisticsNum(array: any[], value = '') {
  if (!array?.length) return 0;
  let totle = 0;
  array.forEach(item => {
    if (value) {
      totle += item[value];
    } else {
      totle += item;
    }
  });

  return totle;
}

export function getFractionalLength(arr: any[]) {
  const cloneArr = arr
    .filter(item => item)
    .map(item => String(parseFloat(item)));
  return cloneArr.length === 0
    ? [0]
    : cloneArr.map(item => {
        const splitItem = item.split('.');
        return splitItem.length > 1 ? splitItem[1].length : 0;
      });
}

export function mathOperation(argArr: any[], type: any, startValue?: number) {
  // type 1 加法， 2 减法， 3 乘法，4 除法,减法和除法需要传（被减数、被除数） startValue
  // 除法 mathOperation([5],4,10) => 10 / 5 => 2
  let result;

  const pointLength = getFractionalLength(argArr);
  const m = Math.pow(
    10,
    pointLength.reduce((total, num) => total + num, 0)
  ); // 结果为10^x，10的次方

  const cloneArgArr = argArr
    .filter((item: any) => item)
    .map((item: string) => parseFloat(item));
  switch (type) {
    case 1:
      result =
        cloneArgArr.reduce(
          (total: number, num: number) => total + parseInt(String(num * m)),
          0
        ) / m;
      break;
    case 2:
      if (!startValue) return;
      result =
        cloneArgArr.reduce(
          (total, num) => total - parseInt(String(num * m)),
          startValue * m
        ) / m;
      break;
    case 3:
      result =
        cloneArgArr.reduce((total: number, num: number) => total * num * m, 1) /
        Math.pow(m, cloneArgArr.length);
      break;
    case 4:
      if (!startValue) return;
      result = cloneArgArr.reduce(
        (total: number, num: number) => (num ? total / (num * m) : 0),
        startValue * m
      );
      break;
  }
  return result;
}
