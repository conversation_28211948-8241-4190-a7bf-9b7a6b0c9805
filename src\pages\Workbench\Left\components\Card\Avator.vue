<template>
  <div class="avator shrink-0">
    <img v-if="teamType === 1" :src="doctorImg" />
    <img v-if="teamType === 4" :src="groupImg" />
    <span v-if="teamType !== 1 && teamType !== 4">{{ name }}</span>
  </div>
</template>

<script setup lang="ts">
import groupImg from '@/assets/imgs/chat/group.png';
import doctorImg from '@/assets/imgs/chat/doctor.png';
interface IProps {
  teamType?: number;
  name?: string;
}
defineProps<IProps>();
defineOptions({
  name: '<PERSON><PERSON>',
});
</script>

<style scoped lang="less">
.avator {
  width: 46px;
  height: 46px;
  border-radius: 100%;
  font-size: 20px;
  font-weight: bold;
  color: #2e6be6;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid #efefef;
}
</style>
