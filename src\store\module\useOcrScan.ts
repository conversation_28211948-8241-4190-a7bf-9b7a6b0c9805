import STORE_NAMES from '@/constant/storeNames';
import { defineStore } from 'pinia';
import { IApiOcrPhotoStatusItem } from '@/interface/type';
import {
  startOcrScanReq,
  getMsgImageStatus,
  getOcrImgPoint,
  getRectifiedImgUrl,
  reOcrScanReq,
  typedOcrScanReq,
  getOcrTextCoords,
} from '@/api/ocr';
import { ElMessage } from 'element-plus';
import { debounce } from 'lodash-es';
import { OcrResultDataType, IAiResultData } from '@/features/OcrViewer/type';

interface TypeImgInfo {
  url: string | null;
  type: string;
  showStatus?: boolean;
  //0删除 1 新增
}
interface TypeGlobalImgInfo {
  currentImgUrl: TypeImgInfo;
  imgList: { [key: string]: TypeImgInfo[] };
  currrentImgIndex: number;
  imgStatusMap: { [key: string]: { value: number; labelName?: string } };
}
export interface UseOcrScanState {
  //当前扫描任务id
  taskId: number | null;
  //图片旋转角度 0为不旋转， 1为顺时针90度， 2为顺时针180度， 3为顺时针270度。
  rotation: number;
  //0 直接扫描 1 重新扫描 2.切换类型扫描
  scanType: number;
  annexType: number;
  isReIdentify: boolean | null;
  ocrResultData: OcrResultDataType | null;
  aiResultData: IAiResultData;
  timer?: number;
  loadingStatus: boolean;
  hasContent: boolean;
  globalImgInfo: TypeGlobalImgInfo;
  cropperImgInfo: {
    imageEnhancedUrl: string | null;
    showDialogCropper: boolean;
    cropperLoading: boolean;
    getEnhancedUrlLoading: boolean;
    showEnhancedDialog: boolean;
    taskId?: number;
    pointData?: number[][];
    corners: number[][];
  };
  saveLoading: boolean;
  // ocr识别的图片文本区域
  ocrTextCoords: any[];
  detaleImgUpdateMedicalRecord: boolean;
}

const ocrFncConfigMap = {
  0: startOcrScanReq,
  1: reOcrScanReq,
  2: typedOcrScanReq,
};

export const useOcrScan = defineStore(STORE_NAMES.OCR_IMG_SCAN, {
  state: (): UseOcrScanState => ({
    taskId: null,
    //图片旋转角度
    rotation: 0,
    //扫描类型
    scanType: 0,
    //附件类型
    annexType: 1,
    //是否发起重新识别
    isReIdentify: false,
    //ai返回结构化数据
    aiResultData: {},
    //ocr数据
    ocrResultData: null,
    //重新发起扫描计时器
    timer: undefined,
    //扫描进度状态
    loadingStatus: false,
    //是否有内容
    hasContent: false,
    globalImgInfo: {
      currentImgUrl: {
        url: '',
        type: '',
      },
      imgList: {},
      currrentImgIndex: 0,
      imgStatusMap: {},
    },
    cropperImgInfo: {
      imageEnhancedUrl: '',
      showDialogCropper: false,
      cropperLoading: false,
      showEnhancedDialog: false,
      getEnhancedUrlLoading: false,
      corners: [],
    },
    saveLoading: false,
    ocrTextCoords: [],
    // 删除图片是否更新病历组件
    detaleImgUpdateMedicalRecord: true,
  }),
  actions: {
    //获取图片角度
    getImgPoints(rotation: number) {
      this.cropperImgInfo.cropperLoading = true;
      getOcrImgPoint({
        url: this.globalImgInfo.currentImgUrl.url as string,
        rotation,
      })
        .then(res => {
          this.cropperImgInfo.taskId = res.taskId;
          this.cropperImgInfo.pointData = res.corners;
          this.cropperImgInfo.showDialogCropper = true;
          this.cropperImgInfo.cropperLoading = false;
        })
        .catch(err => {
          this.cropperImgInfo.cropperLoading = false;
          if (!err.code) {
            ElMessage({
              message: '打开失败:请求异常',
              type: 'warning',
            });
          }
        });
    },
    //初始化图片角度信息
    initCropperImgInfo() {
      this.cropperImgInfo.pointData = [];
      this.cropperImgInfo.showDialogCropper = false;
      this.cropperImgInfo.cropperLoading = false;
      this.cropperImgInfo.showEnhancedDialog = false;
      this.cropperImgInfo.getEnhancedUrlLoading = false;
      // this.cropperImgInfo.corners = [];
    },
    //赋值图片类型和坐标
    initAnnexTypeAndCorners(corners: number[][]) {
      this.cropperImgInfo.corners = corners;
    },
    //切换附件类型重新扫描
    changeFileType(value: number) {
      this.scanType = 2;
      this.annexType = value;
      this.isReIdentify = true;
      this.startOcrScan();
    },
    //根据图片角度获取增强图
    getImageEnhancedUrl() {
      const params = {
        url: this.globalImgInfo.currentImgUrl.url as string,
        corners: this.cropperImgInfo.corners,
      };
      this.cropperImgInfo.getEnhancedUrlLoading = true;

      getRectifiedImgUrl(params)
        .then(res => {
          if (res.url) {
            this.cropperImgInfo.imageEnhancedUrl = res.url as string;
            this.cropperImgInfo.getEnhancedUrlLoading = false;
            this.cropperImgInfo.showEnhancedDialog = true;
          } else {
            ElMessage({
              message: '增强图获取失败,请重新框选!',
              type: 'warning',
            });
          }
        })
        .catch(() => {
          this.cropperImgInfo.getEnhancedUrlLoading = false;
        });
    },
    startOcrScan() {
      this.loadingStatus = true;
      this.ocrTextCoords = [];
      const params = this.getScanParams();
      ocrFncConfigMap[this.scanType](params)
        .then(data => {
          if (data) {
            //已识别
            if (data.ocrTaskStatus === 1) {
              //识别成功
              if (data.resultStatus === 1) {
                this.hasContent = true;
                this.initCropperImgInfo();
                this.annexType = data.photoType as number;
                this.aiResultData = data.aiParseData ?? {};
                this.ocrResultData = { originalText: data?.aiRawData ?? '' };
                this.taskId = data.taskId ?? null;
                this.cropperImgInfo.imageEnhancedUrl =
                  data.enhancedUrl as string;
                // 获取ocr图片文本区域
                getOcrTextCoords({
                  taskId: this.taskId,
                  url: this.cropperImgInfo.imageEnhancedUrl,
                })
                  .then(ocrTextCoordsResult => {
                    const newOcrTextCoords = [];

                    (ocrTextCoordsResult?.items || []).forEach(item => {
                      newOcrTextCoords.push(...item.tokens);
                    });
                    this.ocrTextCoords = newOcrTextCoords;
                  })
                  .finally(() => {
                    this.loadingStatus = false;
                  });
              }
              //识别失败
              if (data.resultStatus === 0) {
                ElMessage({
                  message: '识别失败!',
                  type: 'warning',
                });
                //识别失败也需要展开
                this.loadingStatus = false;
                this.hasContent = true;
                this.cropperImgInfo.imageEnhancedUrl = data.enhancedUrl ?? null;
                this.initCropperImgInfo();
                this.aiResultData = data.aiParseData ?? {};
              }
              window.clearTimeout(this.timer);
            } else {
              //未识别
              this.timer = setTimeout(() => {
                this.isReIdentify = null;
                this.startOcrScan();
              }, 2000);
            }
          } else {
            this.loadingStatus = false;
            ElMessage({
              message: '识别失败!',
              type: 'warning',
            });
          }
        })
        .catch(() => {
          this.loadingStatus = false;
          this.isReIdentify = false;
          clearTimeout(this.timer);
        });
    },
    getScanParams() {
      let params;
      if (this.scanType === 0) {
        params = {
          url: this.globalImgInfo.currentImgUrl.url as string,
          rotation: this.rotation,
        };
      }
      if (this.scanType === 1) {
        params = {
          url: this.globalImgInfo.currentImgUrl.url as string,
          rotation: this.rotation,
          corners: this.cropperImgInfo.corners,
        };
      }
      if (this.scanType === 2) {
        params = {
          annexType: this.annexType,
          content: this.ocrResultData?.originalText ?? '',
          taskId: this.taskId,
        };
      }
      return params;
    },
    updatedSymptomsText(text: string, symptomType: number) {
      if (this.annexType === 1) {
        if (symptomType === 1) {
          this.ocrResultData!.mainSuit = text;
        }
        if (symptomType === 2) {
          this.ocrResultData!.personalHistory = text;
        }
        if (symptomType === 3) {
          this.ocrResultData!.familyHistory = text;
        }
        if (symptomType === 4) {
          this.ocrResultData!.pastHistory = text;
        }
        if (symptomType === 5) {
          this.ocrResultData!.medicalHistory = text;
        }
      }
      if (this.annexType === 2) {
        if (symptomType === 4) {
          this.ocrResultData!.clinicalDiagnosis = text;
        }
      }
      if (this.annexType === 4) {
        if (symptomType === 1) {
          this.ocrResultData!.mainSuit = text;
        }
        if (symptomType === 4) {
          this.ocrResultData!.diagnosis = text;
        }
        if (symptomType === 5) {
          this.ocrResultData!.medicalHistory = text;
        }
      }
    },
    //全局图片信息，当前浏览图片，图片状态，图片切换
    updatedCurrentImgUrl(urlInfo: TypeImgInfo) {
      this.globalImgInfo.currentImgUrl.url = urlInfo.url;
      //指定当前图片的下标
      if (urlInfo.url) {
        this.globalImgInfo.currentImgUrl.type = urlInfo.type;
        const index = this.globalImgInfo.imgList[urlInfo.type].findIndex(
          item => urlInfo.url === item.url
        );
        this.globalImgInfo.currrentImgIndex = index !== -1 ? index : 0;
      }
    },
    //更新全局图片列表
    updatedGlobalImgList(urlInfo: TypeImgInfo) {
      //新增
      if (urlInfo.url) {
        if (!this.globalImgInfo.imgList[urlInfo.type]) {
          this.globalImgInfo.imgList[urlInfo.type] = [];
          if (
            !this.globalImgInfo.imgList[urlInfo.type].filter(
              item => item.url === urlInfo.url
            ).length
          ) {
            this.globalImgInfo.imgList[urlInfo.type].push(urlInfo);
          }
        } else {
          if (
            !this.globalImgInfo.imgList[urlInfo.type].filter(
              item => item.url === urlInfo.url
            ).length
          ) {
            this.globalImgInfo.imgList[urlInfo.type].push(urlInfo);
          }
        }
      }
      if (urlInfo.showStatus) {
        this.getMsgListStatus();
      }
    },
    //删除全局图片列表
    deletePicFromPreviewList(groupType, groupIndex) {
      const type = groupType ?? '1';
      this.globalImgInfo.imgList[type].splice(groupIndex, 1);
    },
    //切换图片
    changeAnotherImg(index: number) {
      const nextIndex = this.globalImgInfo.currrentImgIndex + index;
      if (nextIndex === -1) {
        ElMessage({
          message: '已经是第一张了',
          type: 'warning',
        });
      } else if (
        nextIndex ===
        this.globalImgInfo.imgList[this.globalImgInfo.currentImgUrl.type].length
      ) {
        ElMessage({
          message: '已经是最后一张了',
          type: 'warning',
        });
      } else {
        this.globalImgInfo.currrentImgIndex = nextIndex;
        this.globalImgInfo.currentImgUrl.url =
          this.globalImgInfo.imgList[this.globalImgInfo.currentImgUrl.type][
            nextIndex
          ].url;
      }
    },
    //获取图片状态
    getMsgListStatus: debounce(function (this: any) {
      const showStatusUrlArr: string[] = [];
      Object.values(this.globalImgInfo.imgList)
        .flat()
        .forEach((item: any) => {
          if (item.showStatus) {
            showStatusUrlArr.push(item.url);
          }
        });
      const arr = this.getPicStatusGroup(showStatusUrlArr, 100);
      for (let i = 0; i < arr.length; i++) {
        this.getMsgImageStatusReq(arr[i]);
      }
    }, 300),
    getMsgImageStatusReq(urls: string[]) {
      getMsgImageStatus({ urls }).then(data => {
        if (Array.isArray(data)) {
          data.forEach((item: IApiOcrPhotoStatusItem) => {
            //识别中
            if (item.ocrStatus === 0 && item.url) {
              this.globalImgInfo.imgStatusMap[item.url] = {
                value: 0,
                labelName: '',
              };
            }
            //已识别存在两种情况,成功和失败
            if (item.ocrStatus === 1) {
              //识别失败
              if (item.resultStatus === 0 && item.url) {
                this.globalImgInfo.imgStatusMap[item.url] = {
                  value: 1,
                  labelName: '',
                };
              }
              //识别成功
              if (item.resultStatus === 1 && item.url) {
                this.globalImgInfo.imgStatusMap[item.url] = {
                  value: item.photoType! + 1,
                  labelName: '',
                };
                if (item.photoType === 3) {
                  this.globalImgInfo.imgStatusMap[item.url].labelName =
                    item.labelName ?? '';
                }
              }
            }
            if (item.ocrStatus === 2 && item.url) {
              this.globalImgInfo.imgStatusMap[item.url] = {
                value: 10,
                labelName: '',
              };
            }
          });
        }
      });
    },
    getPicStatusGroup(arr: string[], num: number) {
      const newArr = [...arr]; // 因为splice会改变原数组，要深拷贝一下
      const list = [];
      for (let i = 0; i < newArr.length; ) {
        list.push(newArr.splice(i, num));
      }
      return list;
    },
    resetOcrState() {
      this.annexType = 1;
      this.isReIdentify = false;
      this.aiResultData = {};
      this.ocrResultData = null;
      this.timer = undefined;
      this.loadingStatus = false;
      this.hasContent = false;
      this.cropperImgInfo.corners = [];
      this.cropperImgInfo.imageEnhancedUrl = null;
    },
    updateMedicalRecord(flag: boolean) {
      this.detaleImgUpdateMedicalRecord = flag;
    },
  },
});
