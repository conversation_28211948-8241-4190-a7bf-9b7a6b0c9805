import { cloneDeep, isNil, some } from 'lodash-es';
import { updateIndex } from '@/api/indicatorsReport';

// 表单操作类型
export type ActionType = 'create' | 'edit' | 'delete';

/** 新增指标项目 */
export interface IAddIndicator {
  // 类型 时间｜数字输入
  type: 'date' | 'input-number' | 'BMI' | 'Balance' | 'select';
  // name
  name: string;
  // 值对应的key
  key: string;
  // 单位
  unit?: string;
  // BMI
  BMI?: {
    heightKey: string;
    weightKey: string;
  };
  balance?: {
    waterVolumeKey: string;
    urineOutputKey: string;
  };
  // select选项
  options?: { label: string; value: string | number }[];
  // 默认值
  defaultVal?: string | number;
}

export interface IAddIndicatorCommonParams {
  templateName: string;
  patientId: number;
  uid: number;
  roleType: number;
}

/** 可新增手动新增指标 指标类型  */
export const newIndicTypes = [
  'BLOOD_PRESSURE',
  'HEART_RATE',
  'BLOOD_SUGAR',
  'WEIGHT',
  'INTAKE_OUTPUT',
];

/** 可调整阈值指标 指标类型  */
export const thresholdIndicTypes = [
  'BLOOD_PRESSURE',
  'HEART_RATE',
  'BLOOD_SUGAR',
];

export default function useUpdateIndicator() {
  const addIndicRef = ref();
  const showAddIndicDialog = ref(false);

  const indicConfigMap: Record<string, IAddIndicator[]> = {
    BLOOD_PRESSURE: [
      { type: 'date', key: 'recordingTime', name: '选择时间' },
      {
        type: 'input-number',
        key: 'highPressure',
        unit: 'mmHg',
        name: '收缩压',
      },
      {
        type: 'input-number',
        key: 'lowPressure',
        unit: 'mmHg',
        name: '舒张压',
      },
    ],
    HEART_RATE: [
      { type: 'date', key: 'recordingTime', name: '选择时间' },
      {
        type: 'input-number',
        key: 'indexValue',
        unit: '次/min',
        name: '标准值',
      },
    ],
    BLOOD_SUGAR: [
      { type: 'date', key: 'recordingTime', name: '选择时间' },
      {
        type: 'select',
        key: 'sugarType',
        name: '血糖类型',
        options: [
          { value: 3, label: '随机' },
          { value: 2, label: '餐后2小时' },
          { value: 1, label: '空腹' },
        ],
        defaultVal: 3,
      },
      {
        type: 'input-number',
        key: 'indexValue',
        unit: 'mmol/L',
        name: '标准值',
      },
    ],
    WEIGHT: [
      { type: 'date', key: 'recordingTime', name: '选择时间' },
      { type: 'input-number', key: 'height', unit: 'cm', name: '身高' },
      { type: 'input-number', key: 'weight', unit: 'Kg', name: '体重' },
      {
        type: 'BMI',
        key: 'bmi',
        BMI: { heightKey: 'height', weightKey: 'weight' },
        name: 'BMI',
      },
    ],
    INTAKE_OUTPUT: [
      { type: 'date', key: 'recordingTime', name: '选择时间' },
      { type: 'input-number', key: 'waterVolume', unit: 'ml', name: '饮水量' },
      { type: 'input-number', key: 'urineOutput', unit: 'ml', name: '尿量' },
      {
        type: 'Balance',
        key: 'balance',
        balance: {
          waterVolumeKey: 'waterVolume',
          urineOutputKey: 'urineOutput',
        },
        name: '平衡',
      },
    ],
  };

  const updateIndicLoading = ref(false);

  async function addIndicatorSubmit(
    params: IAddIndicatorCommonParams,
    callback?: () => void
  ) {
    const data: any = formatCurrentIndic(
      cloneDeep(addIndicRef.value.data),
      params.templateName
    );
    const isValid = !some(data, val => isNil(val));
    if (!isValid) {
      return ElMessage.error('值不能为空！');
    }
    try {
      updateIndicLoading.value = true;
      await updateIndex({
        ...data,
        ...params,
      });
      ElMessage.success('操作成功！');
      callback && callback();
    } finally {
      updateIndicLoading.value = false;
    }
  }

  function formatCurrentIndic(item: any, templateName: string) {
    let data: any = {};
    switch (templateName) {
      case 'BLOOD_PRESSURE':
        data = {
          highPressure: !isNil(item.highPressure)
            ? Number(item.highPressure)
            : undefined,
          lowPressure: !isNil(item.lowPressure)
            ? Number(item.lowPressure)
            : undefined,
        };
        break;
      case 'HEART_RATE':
        data = {
          indexValue: !isNil(item.indexValue)
            ? Number(item.indexValue)
            : undefined,
        };
        break;
      case 'BLOOD_SUGAR':
        data = {
          indexValue: !isNil(item.indexValue)
            ? Number(item.indexValue)
            : undefined,
          sugarType: item.sugarType,
        };
        break;
      case 'WEIGHT':
        data = {
          height: !isNil(item.height) ? Number(item.height) : undefined,
          weight: !isNil(item.weight) ? Number(item.weight) : undefined,
        };
        break;
      case 'INTAKE_OUTPUT':
        data = {
          waterVolume: !isNil(item.waterVolume)
            ? Number(item.waterVolume)
            : undefined,
          urineOutput: !isNil(item.urineOutput)
            ? Number(item.urineOutput)
            : undefined,
        };
        break;
    }
    data.recordingTime = item.recordingTime || undefined;
    if (item.id) data.id = item.id;
    return data;
  }

  // 当前所编辑的table item
  const currentEditIndicator = ref({});

  function editorIndicator(item: any, templateName: string) {
    if (templateName === 'HEART_RATE') item.indexValue = item.heartRate;
    if (templateName === 'BLOOD_SUGAR') item.sugarType = item.bloodSugarType;
    currentEditIndicator.value = formatCurrentIndic(item, templateName);
    showAddIndicDialog.value = true;
  }

  return {
    updateIndicLoading,
    addIndicRef,
    showAddIndicDialog,
    indicConfigMap,
    editorIndicator,
    addIndicatorSubmit,
    currentEditIndicator,
  };
}
