<template>
  <div class="body">
    <Swiper
      class="my-swiper"
      @swiper="initSwiperRef"
      @slide-next-transition-end="slideNextTransitionEnd"
      @slide-prev-transition-end="slidePrevTransitionEnd"
    >
      <SwiperSlide
        v-for="(item, index) in state.currentCalenderWeekList"
        :key="index"
      >
        <slot name="week-content" :weekdays="item">
          <div
            v-for="(k, j) in item"
            :key="j"
            class="week-block"
            @click="upDateCheckedDate(k.sendDate)"
          >
            <div class="week-title">{{ k.week }}</div>
            <div class="date-block">
              <div class="date-number">
                <div
                  :class="state.checkedDate === k.sendDate ? 'is-selected' : ''"
                  class="normal"
                >
                  {{ k.showDate }}
                </div>
              </div>
              <div class="date-number">
                <div
                  :class="[
                    'cla-box',
                    !countDate[dayjs(k.sendDate).format('YYYY-MM-DD')]
                      ? 'no-value'
                      : '',
                  ]"
                >
                  {{ countDate[dayjs(k.sendDate).format('YYYY-MM-DD')] }}
                </div>
              </div>
            </div>
          </div>
        </slot>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import { cloneDeep } from 'lodash-es';
import { CalenderWeekState, CalenderWeekItem } from './type';
import { computed } from 'vue';
import dayjs from 'dayjs';

interface Props {
  initDate: Date | null;
  countDate?: {
    [key: string]: number;
  };
}

const props = defineProps<Props>();

const emit = defineEmits([
  'update:initDate',
  'getSunday',
  'changeCurrentWeekData',
  'chooseDate',
]);

const state = reactive<CalenderWeekState>({
  currentCalenderWeekList: [],
  checkedDate: '',
  currentFirstDate: null,
  headLabelDate: '',
  swiperRef: null,
});

const headLabelDataStr = computed(() => {
  if (state.headLabelDate) {
    const date = new Date(state.headLabelDate);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    return `${year} 年 ${month} 月`;
  } else {
    return '---- 年 -- 月';
  }
});

const initCalender = (date: Date = new Date()) => {
  const now = setDate(date);
  const pre = setDate(addDate(state.currentFirstDate as Date, -7));
  const next = setDate(addDate(state.currentFirstDate as Date, 14));
  state.currentCalenderWeekList = [pre, now, next];
};
/*根据每周第一天生成本周数据*/
const setDate = (date: Date) => {
  const week = date.getDay();
  date = addDate(date, week * -1);
  state.currentFirstDate = new Date(date);
  const list = [];
  for (let i = 0; i < 7; i++) {
    list.push(formatDate(i === 0 ? addDate(date, 0) : addDate(date, 1)));
  }
  return list as CalenderWeekItem[];
};
/*获取每周第一天*/
const addDate = (date: Date, n: number) => {
  date.setDate(date.getDate() + n);
  return date;
};
const setNowDate = nowDate => {
  const year = nowDate.getFullYear();
  const month = nowDate.getMonth() + 1;
  const date = nowDate.getDate();
  return `${year}-${month}-${date}`;
};
const initHeadLabelDate = (date: Date) => {
  state.headLabelDate = setNowDate(date);
};
const initCheckedDate = (date: Date) => {
  state.checkedDate = setNowDate(date);
};
/*生成每周数据*/
const formatDate = (nowDate: Date) => {
  const year = nowDate.getFullYear();
  const month = nowDate.getMonth() + 1;
  const date = nowDate.getDate();
  const week = ['日', '一', '二', '三', '四', '五', '六'][nowDate.getDay()];
  return {
    week,
    showDate: date < 10 ? '0' + date : String(date),
    listDate: `${month}月${date}日 ${week}`,
    sendDate: `${year}-${month}-${date}`,
    now: nowDate.toLocaleDateString() === new Date().toLocaleDateString(),
  } as CalenderWeekItem;
};
/*更新当前选中时间*/
const upDateCheckedDate = (sendDate: string) => {
  state.checkedDate = sendDate;
  emit('chooseDate', new Date(state.checkedDate));
};
const initSwiperRef = swiper => {
  state.swiperRef = swiper;
  state.swiperRef.slideTo(1, 0, false);
};
const slideNextTransitionEnd = () => {
  state.swiperRef.slideTo(1, 0, false);
  const pre = setDate(addDate(state.currentFirstDate as Date, -7));
  const now = setDate(addDate(state.currentFirstDate as Date, 7));
  const next = setDate(addDate(state.currentFirstDate as Date, 7));
  state.currentCalenderWeekList = [pre, now, next];
  setTimeout(() => {
    state.headLabelDate = state.currentCalenderWeekList[1][0].sendDate;
  }, 20);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const slidePrevTransitionEnd = () => {
  state.swiperRef.slideTo(1, 0, false);
  const pre = setDate(addDate(state.currentFirstDate as Date, -21));
  const now = setDate(addDate(state.currentFirstDate as Date, 7));
  const next = setDate(addDate(state.currentFirstDate as Date, 7));
  state.currentCalenderWeekList = [pre, now, next];
  setTimeout(() => {
    state.headLabelDate = state.currentCalenderWeekList[1][0].sendDate;
  }, 20);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const prevMonth = () => {
  const date = new Date(state.headLabelDate);
  date.setMonth(date.getMonth() - 1);
  date.setDate(1); // 确保设置为每月的第一天
  state.headLabelDate = setNowDate(date);
  initCalender(date);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const nextMonth = () => {
  const date = new Date(state.headLabelDate);
  date.setMonth(date.getMonth() + 1);
  date.setDate(1); // 确保设置为每月的第一天
  state.headLabelDate = setNowDate(date);
  initCalender(date);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const prevYear = () => {
  const date = new Date(state.headLabelDate);
  date.setFullYear(date.getFullYear() - 1);
  date.setDate(1); // 确保设置为每月的第一天
  state.headLabelDate = setNowDate(date);
  initCalender(date);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const nextYear = () => {
  const date = new Date(state.headLabelDate);
  date.setFullYear(date.getFullYear() + 1);
  date.setDate(1); // 确保设置为每月的第一天
  state.headLabelDate = setNowDate(date);
  initCalender(date);
  emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
};
const slideNext = () => {
  state.swiperRef.slideNext();
};
const slidePrev = () => {
  state.swiperRef.slidePrev();
};
defineExpose({
  prevMonth,
  nextMonth,
  prevYear,
  nextYear,
  slideNext,
  slidePrev,
  headLabelDataStr,
});
onMounted(() => {
  initHeadLabelDate(props.initDate ? cloneDeep(props.initDate) : new Date());
  initCheckedDate(props.initDate ? cloneDeep(props.initDate) : new Date());
  initCalender(props.initDate ? cloneDeep(props.initDate) : new Date());
});
watch(
  () => state.checkedDate,
  () => {
    emit('update:initDate', new Date(state.checkedDate));
    emit('getSunday', new Date(state.currentCalenderWeekList[1][0].sendDate));
    // emit('changeCurrentWeekData', state.currentCalenderWeekList[1]);
  },
  {
    deep: true,
  }
);
watch(
  () => props.initDate,
  () => {
    initHeadLabelDate(props.initDate ? cloneDeep(props.initDate) : new Date());
    initCheckedDate(props.initDate ? cloneDeep(props.initDate) : new Date());
    initCalender(props.initDate ? cloneDeep(props.initDate) : new Date());
  }
);
</script>

<style scoped lang="less">
.body {
  padding: 0 2px;
  .date-day {
    display: flex;
    justify-content: space-between;
  }
}
.swiper-slide {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  //box-sizing: border-box;
  .week-block {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    color: #939cae;
    font-size: 14px;
    .week-title {
      padding-bottom: 16px;
    }
    .date-block {
      flex: 1;
      padding: 8px;
      background-color: #f7f8fa;
      box-sizing: border-box;
      .date-number {
        display: flex;
        justify-content: flex-end;
        .normal {
          width: 26px;
          height: 26px;
          border-radius: 50%;
          text-align: center;
          font-size: 16px;
          color: #3a4762;
        }
        .is-selected {
          background-color: #0a73e4;
          color: #fff;
        }
        .cla-box {
          min-width: 24px;
          min-height: 24px;
          background-color: #e6eeff;
          text-align: center;
          margin-top: 4px;
          color: #2e6be6;
          font-size: 16px;
        }
        .no-value {
          background-color: #f7f8fa;
        }
      }
    }
    .date-block:not(:first-child) {
      border-left: 2px solid #fff;
      border-right: 2px solid #fff;
    }
    .date-block:not(:last-child) {
      border-left: 2px solid #fff;
      border-right: 2px solid #fff;
    }
    .date-block:last-child {
      border-left: 2px solid #fff;
    }
    .date-block:first-child {
      border-right: 2px solid #fff;
    }
  }
}
</style>
