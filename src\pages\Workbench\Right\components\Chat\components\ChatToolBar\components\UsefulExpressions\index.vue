<template>
  <div>
    <Dialog :visible="dialogVisible" @close="close">
      <template #header>
        <img
          src="@/assets/imgs/chat/expressions.png"
          alt=""
          class="expressionsImg"
        />
        常用语
      </template>
      <div class="content">
        <div class="searchBox">
          <el-checkbox
            v-model="onlyMine"
            label="仅显示个人常用"
            size="large"
            @change="checkboxChange"
          />
          <el-input
            v-model.trim="keywords"
            class="phraseInput"
            :suffix-icon="Search"
            placeholder="请输入关键字模糊检索"
            @input="searchChange"
          />
        </div>
        <div class="phraseList">
          <el-table
            :data="phraseTableData"
            height="300"
            stripe
            @row-click="rowClick"
          >
            <el-table-column type="index" width="80" label="序号">
              <template #default="scope">
                <span>
                  {{
                    (currentPage - 1) * pageSize + (scope.$index + 1) > 9
                      ? (currentPage - 1) * pageSize + (scope.$index + 1)
                      : '0' +
                        ((currentPage - 1) * pageSize + (scope.$index + 1))
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类别" width="120">
              <template #default="scope">
                <span>{{ scope.row.type === 1 ? '通用' : '个人' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="speechcraftContent"
              label="常用语"
              show-overflow-tooltip
            >
              <template #default="scope">
                <span>{{ scope.row.speechcraftContent }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[10, 15, 20, 30]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import Dialog from '../Dialog/index.vue';
import { getSpeechCraftList } from '@/api/chat';
import { debounce } from 'lodash-es';
interface IProps {
  expressionsVisible: boolean;
}
defineOptions({
  name: 'UsefulExpression',
});
const props = defineProps<IProps>();
const emits = defineEmits(['close', 'change-text']);
const dialogVisible = ref(false);
const keywords = ref('');
const onlyMine = ref(false);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);
const phraseTableData = ref<any[]>([]);

const close = () => {
  onlyMine.value = false;
  pageSize.value = 10;
  currentPage.value = 1;
  keywords.value = '';
  emits('close');
};
const searchChange = debounce(() => {
  currentPage.value = 1;
  getExpressionList();
}, 100);
const checkboxChange = () => {
  currentPage.value = 1;
  getExpressionList();
};
const rowClick = (row: any) => {
  emits('change-text', { val: row.speechcraftContent, type: 'expression' });
};
const getExpressionList = async () => {
  const params = {
    keyword: keywords.value,
    onlyMine: onlyMine.value ? 2 : 1,
    pageSize: pageSize.value,
    page: currentPage.value,
  };
  const res = await getSpeechCraftList(params);
  phraseTableData.value = res.speechcraftList ?? [];
  total.value = res.totals ?? 0;
};
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getExpressionList();
};
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getExpressionList();
};
watch(
  () => props.expressionsVisible,
  val => {
    dialogVisible.value = val;
    if (val) {
      getExpressionList();
    }
  }
);
</script>
<style scoped lang="less">
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
.searchBox {
  height: 32px;
  display: flex;
  align-items: center;
  .phraseValue {
    width: 120px;
  }
  .phraseInput {
    width: 240px;
    margin-left: 8px;
  }
}
</style>
