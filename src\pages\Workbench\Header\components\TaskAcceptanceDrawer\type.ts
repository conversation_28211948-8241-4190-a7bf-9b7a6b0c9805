// 任务类型
export interface taskType {
  label: string;
  value: number;
  type?: number[];
}

// 任务事项头部搜索条件
export interface taskSearchType {
  taskType: number;
  taskStatus: number;
  completionTime: number;
}

// 任务列表
interface IPatientInfo {
  patientAge: number;
  patientId: number;
  patientName: string;
  patientSex: number;
}
export interface ITaskList {
  taskId: number;
  patientInfo: IPatientInfo;
  taskType: number;
  taskStatus?: number;
  statusTime: number;
  sourceId: number;
  taskMethod: number;
  imageCount: number;
  createTime: number;
  sourceTime: number;
  generatedName: string;
  taskSection?: string;
}

// 头部查询条数传给后端的参数
export interface ITaskSearch {
  taskMethod?: null | number;
  taskStatus?: null | number;
  completeStartTime?: number;
  completeEndTime?: number;
  pageNumber?: number;
  pageSize?: number;
}

// 驳回任务 | 撤销任务
export interface IRejectTaskApi {
  taskId: number;
}

// 病历任务查询
export interface ICaseTaskApi {
  sourceId: number;
  taskType: number;
  taskMethod: number;
  taskId?: number;
}

// 生成转录任务
export interface ITranscriptionTaskApi {
  patientId: number;
  patientName: string;
  sourceId: number;
  taskType: number;
  taskMethod: number;
  sourceType: number;
}
