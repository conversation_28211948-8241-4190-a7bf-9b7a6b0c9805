<template>
  <BaseTable
    ref="baseTableRef"
    :columns="columns"
    :request-api="getIndexFromQuery"
    :data-callback="formatDataCallbackVal"
    :init-param="reqParam"
    :request-auto="false"
    :reload-with-init-param="reloadWithInitParam"
    max-height="2000"
  />
</template>

<script setup lang="tsx">
import BaseTable from '@/components/BaseTable';
import { ColumnProps } from '@/components/BaseTable/type';
import { getIndexFromQuery } from '@/api/indicatorsReport';
import { PropType } from 'vue';
import { ActionType } from '../hooks/useUpdateIndicator';
import { IApiPatientReportListData } from '@/interface/type';
import bus from '@/lib/bus';
import { formatTimeTemplate, getBMI } from '@/utils';

const props = defineProps({
  reqParam: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  indicActiveTab: {
    type: String,
    default: '',
  },
});

const emits = defineEmits<{
  (e: 'handleAction', info: any): void;
}>();

const baseTableRef = ref();

// 指标值单位
const indexValueUnit = ref('');

const formatDataCallbackVal = (data: any) => {
  const { dataList = [], totals, unit = '' } = data || {};
  indexValueUnit.value = unit;
  return { data: dataList, records: totals };
};

const reloadWithInitParam = computed(() => {
  return props.indicActiveTab === 'tablePanel' && !!props.reqParam.startTime;
});

const treatmentMethodMap: Record<string, string> = {
  1: '电话沟通',
  2: '在线沟通',
  3: '待观察',
  4: '观察',
  5: '调药',
  6: '门诊',
  7: '住院',
};

const bloodSugarTypeMap: Record<string, string> = {
  1: '空腹',
  2: '餐后2小时血糖',
  3: '随机血糖',
};

function getColumns(checkType: number) {
  let type;
  if (checkType === 46) {
    type = '2';
  } else if (checkType === 11) {
    type = '4';
  } else if (checkType === 12) {
    type = '5';
  } else {
    type = [44, 45, 10].includes(checkType) ? '1' : '3';
  }

  const map: Record<string, ColumnProps<any>[]> = {
    1: [
      {
        prop: 'highPressure',
        label: '收缩压(mmHg)',
        width: 80,
        hidden: checkType !== 44,
        render: scope => {
          return (
            <span class={scope.row.errorLevel > 0 ? 'text-danger' : ''}>
              {scope.row.highPressure}
            </span>
          );
        },
      },
      {
        prop: 'lowPressure',
        label: '舒张压(mmHg)',
        width: 80,
        hidden: checkType !== 44,
        render: scope => {
          return (
            <span class={scope.row.errorLevel > 0 ? 'text-danger' : ''}>
              {scope.row.lowPressure}
            </span>
          );
        },
      },
      {
        prop: 'heartRate',
        label: '心率(次/分钟)',
        width: 120,
        hidden: checkType !== 45,
        render: scope => {
          return (
            <span class={scope.row.errorLevel > 0 ? 'text-danger' : ''}>
              {scope.row.heartRate || '--'}
            </span>
          );
        },
      },
      {
        prop: 'bloodSugarType',
        label: '类型',
        width: 150,
        hidden: checkType !== 10,
        render: scope => {
          return (
            (scope.row.bloodSugarType &&
              bloodSugarTypeMap[scope.row.bloodSugarType]) ||
            '--'
          );
        },
      },
      {
        prop: 'giu',
        label: '血糖(mmol/L)',
        width: 100,
        hidden: checkType !== 10,
        render: scope => {
          return (
            <span class={scope.row.errorLevel > 0 ? 'text-danger' : ''}>
              {scope.row.giu || '--'}
            </span>
          );
        },
      },
      {
        prop: 'handlerResult',
        label: '处理结果',
        width: 100,
        render: scope => {
          return (
            (scope.row.handlerResult &&
              treatmentMethodMap[scope.row.handlerResult]) ||
            '--'
          );
        },
      },
      {
        prop: 'handler',
        label: '处理人',
        width: 90,
      },
      {
        prop: 'handleTime',
        label: '处理时间',
        width: 180,
        render: scope => {
          return formatTimeTemplate(scope.row.handleTime) || '--';
        },
      },
      {
        prop: 'operation',
        label: '操作',
        fixed: 'right',
        minWidth: 110,
        render: scope => {
          return (
            scope.row.isDelete && (
              <>
                <el-button
                  type="primary"
                  link
                  onClick={() => handleAction('edit', scope.row)}
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  link
                  onClick={() => handleAction('delete', scope.row)}
                >
                  删除
                </el-button>
              </>
            )
          );
        },
      },
    ],
    2: [
      {
        prop: 'height',
        label: '身高(cm)',
        minWidth: 100,
      },
      {
        prop: 'weight',
        label: '体重(kg)',
        minWidth: 100,
        render: scope => {
          return (
            <span class={scope.row.errorLevel > 0 ? 'text-danger' : ''}>
              {scope.row.weight || '--'}
            </span>
          );
        },
      },
      {
        prop: 'bmi',
        label: 'BMI',
        minWidth: 80,
        render: scope => {
          return <span>{getBMI(scope.row.height, scope.row.weight)}</span>;
        },
      },
      {
        prop: 'operation',
        label: '操作',
        fixed: 'right',
        minWidth: 110,
        render: scope => {
          return (
            scope.row.isDelete && (
              <>
                <el-button
                  type="primary"
                  link
                  onClick={() => handleAction('edit', scope.row)}
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  link
                  onClick={() => handleAction('delete', scope.row)}
                >
                  删除
                </el-button>
              </>
            )
          );
        },
      },
    ],
    3: [
      {
        prop: 'indexValue',
        label: '指标值',
        headerRender: () => {
          const unit = indexValueUnit.value;
          return <span>指标值{unit ? `(${unit})` : ''}</span>;
        },
        minWidth: 200,
      },
      {
        prop: 'reportTime',
        label: '报告日期',
        minWidth: 180,
        render: scope => {
          return formatTimeTemplate(scope.row.reportTime) || '--';
        },
      },
    ],
    4: [
      {
        prop: 'urineOutput',
        label: '尿量(ml)',
        minWidth: 100,
      },
      {
        prop: 'waterVolume',
        label: '饮水量(ml)',
        minWidth: 100,
      },
      {
        prop: 'diff',
        label: '差值',
        minWidth: 100,
        render: scope => {
          return (
            <span>
              {scope.row.diff > 0
                ? scope.row.diff + ' (正平衡)'
                : scope.row.diff < 0
                  ? scope.row.diff + ' (负平衡)'
                  : ''}
            </span>
          );
        },
      },
      {
        prop: 'operation',
        label: '操作',
        fixed: 'right',
        minWidth: 110,
        render: scope => {
          return (
            <el-button
              type="primary"
              link
              onClick={() => handleAction('edit', scope.row)}
            >
              编辑
            </el-button>
          );
        },
      },
    ],
    5: [
      {
        prop: 'content',
        label: '射血分数(%)',
        minWidth: 100,
      },
    ],
  };
  return map[type];
}

// 表格配置项
const columns = computed<ColumnProps<any>[]>(() => {
  const checkType = props.reqParam.checkType;
  if (!checkType) return [];
  const list: ColumnProps<any>[] = [
    { prop: 'dataSource', label: '来源', width: 90 },
    {
      prop: 'recordingTime',
      label: '记录时间',
      width: 180,
      render: scope => {
        return formatTimeTemplate(scope.row.recordingTime) || '--';
      },
    },
    ...getColumns(checkType),
  ];

  return list.filter(
    item => item.hidden === false || item.hidden === undefined
  );
});

const handleAction = (
  actionType: ActionType,
  item: IApiPatientReportListData
) => {
  emits('handleAction', { actionType, item });
};

onMounted(() => {
  bus.on('refresh-indicators-analysis-table', () =>
    baseTableRef.value?.getTableList()
  );
});
onBeforeUnmount(() => {
  bus.off('refresh-indicators-analysis-table');
});

watch(
  () => props.indicActiveTab,
  () => {
    if (reloadWithInitParam.value) baseTableRef.value?.getTableList();
  }
);
</script>
