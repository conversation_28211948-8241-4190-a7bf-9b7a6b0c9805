/**
 * 驼峰转下划线
 * @param obj 对象
 * @returns 转换后的对象
 */
export const camelToUnderline = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(item => camelToUnderline(item)) as unknown as T;
  }

  return Object.keys(obj).reduce((acc, key) => {
    const newKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
    const value = obj[key as keyof T];

    acc[newKey as keyof T] =
      typeof value === 'object' && value !== null
        ? camelToUnderline(value)
        : value;

    return acc;
  }, {} as T);
};

/**
 * 下划线转小驼峰
 * @param obj 对象
 * @returns 转换后的对象
 */
export const underlineToCamel = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(item => underlineToCamel(item)) as unknown as T;
  }

  return Object.keys(obj).reduce((acc, key) => {
    // 处理下划线后的字母转为大写（保留首字母小写）
    const newKey = key.replace(/_(\w)/g, (_, c) => c.toUpperCase());
    const value = obj[key as keyof T];

    acc[newKey as keyof T] =
      typeof value === 'object' && value !== null
        ? underlineToCamel(value)
        : value;

    return acc;
  }, {} as T);
};
