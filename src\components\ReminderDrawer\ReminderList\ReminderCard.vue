<template>
  <div
    :class="{
      'card-container': true,
      'deal-style': toDoObj.status === 2 || getIsDueTime(toDoObj.overdueTime),
    }"
    @click="handleClick"
  >
    <div class="info-row">
      <div class="info-item">
        <div class="info-label">患者信息:</div>
        <div class="info-content">
          {{ toDoObj.patientName }} | {{ toDoObj.groupName }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">责任人:</div>
        <div class="info-content">{{ toDoObj.headName }}</div>
      </div>
    </div>
    <div class="info-row">
      <div class="info-item">
        <div class="info-label">提醒时间:</div>
        <div class="info-content">
          {{
            toDoObj.remindTime
              ? dayjs(toDoObj.remindTime).format('YYYY-MM-DD  HH:mm')
              : '--'
          }}
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">待办期限:</div>
        <div class="info-content">
          {{ dayjs(toDoObj.term).format('YYYY-MM-DD  HH:mm') }}
        </div>
      </div>
    </div>
    <div class="info-row">
      <div class="info-item">
        <div class="info-label">超期时间:</div>
        <div class="info-content">
          {{
            toDoObj.overdueTime
              ? dayjs(toDoObj.overdueTime).format('YYYY-MM-DD  HH:mm')
              : '--'
          }}
        </div>
      </div>
      <div v-if="toDoObj.type === 10" class="info-item">
        <div class="info-label">同步患者:</div>
        <div class="info-content">
          <span>
            {{ toDoObj.mqPatient ? '同步' : '未同步' }}
          </span>
        </div>
      </div>
    </div>
    <div class="info-row">
      <div class="info-item">
        <div class="info-label min-w-78">待办说明:</div>
        <div class="info-content">
          {{ toDoObj.content }}
        </div>
      </div>
    </div>
    <div v-if="toDoObj.status !== 2" class="btn-group">
      <div class="left">
        <div v-if="toDoObj.type === 10" class="delete-btn" @click="deleteTodo">
          删除
        </div>
      </div>
      <div class="right">
        <div
          v-if="
            getTime(toDoObj.term).includes('已逾期') &&
            !getIsDueTime(toDoObj.overdueTime)
          "
          class="delay-text text-btn"
        >
          <span>
            {{ getTime(toDoObj.term) }}
          </span>
        </div>
        <div
          v-if="!getTime(toDoObj.term).includes('已逾期') && toDoObj.remindTime"
          class="remind-text text-btn"
        >
          （{{ dayjs(toDoObj.remindTime).format('YYYY-MM-DD HH:mm') }} 提醒）
        </div>
        <el-popover
          :visible="visible"
          placement="left"
          :width="330"
          trigger="click"
        >
          <div>
            <div class="popover-top">
              <div class="label">提醒时间:</div>
              <el-date-picker
                v-model="delayDate"
                :teleported="false"
                type="datetime"
                :clearable="false"
                :disabled-date="disabledDateFn"
                placeholder="请选择日期"
              />
            </div>
            <div class="popover-bottom">
              <div class="btn cancel" @click="visible = false">取消</div>
              <div class="btn confirm" @click="confirmDelayDate">确定</div>
            </div>
          </div>
          <template #reference>
            <div
              v-if="toDoObj !== 2 && !getIsDueTime(toDoObj.overdueTime)"
              class="cancel-btn btn"
              @click="visible = true"
            >
              稍后提醒
            </div>
          </template>
        </el-popover>

        <div
          v-if="toDoObj !== 2 && !getIsDueTime(toDoObj.overdueTime)"
          class="confirm-btn btn"
          @click="handleToDo"
        >
          处理
        </div>
      </div>
    </div>
    <div v-if="getIsDueTime(toDoObj.overdueTime)" class="tag">已超期</div>
    <div v-if="toDoObj.status === 2" class="tag">已处理</div>
  </div>
</template>

<script lang="ts" setup>
import {
  getTime,
  todoType,
} from '@/pages/Workbench/Right/components/PatientTodo/index.ts';
import { delayTodoApi } from '@/api/todo';
import { dialogTip } from '@/pages/Workbench/Right/components/PatientTodo/index.ts';
import dayjs from 'dayjs';
import { ref } from 'vue';
import store from '@/store';

const globalStore = store.useGlobal();

const delayDate = ref('');

const visible = ref(false);

interface Props {
  toDoObj: any;
}

const props = defineProps<Props>();

const emit = defineEmits(['getOperationReminder', 'handleTodo', 'deleteTodo']);

// 限制日期选择时间
const disabledDateFn = (time: { getTime: () => number }) => {
  if (time.getTime() < Date.now() - 8.64e7) {
    return true;
  } else {
    return false;
  }
};

const operationReminderInfo = reactive({
  delayDate: null,
});

const closePopover = () => {
  visible.value = false;
};

const confirmDelayDate = () => {
  if (!delayDate.value) return (visible.value = false);
  if (dayjs(delayDate.value).valueOf() > props.toDoObj.overdueTime) {
    return dialogTip(
      '延期时间不能超过超期时间，超期时间为：' +
        dayjs(props.toDoObj.overdueTime).format('YYYY-MM-DD  HH:mm')
    );
  }
  operationReminderInfo.delayDate = delayDate.value;
  delayTodoApi({
    backlogId: props.toDoObj.backlogId,
    type: todoType[props.toDoObj.type - 1],
    remindTime: dayjs(operationReminderInfo.delayDate).valueOf(),
  })
    .then((res: any) => {
      if (res.code === 'E000000') {
        emit('getOperationReminder', {
          backlogId: props.toDoObj.backlogId,
          type: todoType[props.toDoObj.type - 1],
          remindTime: dayjs(operationReminderInfo.delayDate).valueOf(),
        });
        dialogTip('待办延期成功！', 'success');
      } else {
        dialogTip('待办延期失败：' + res.message, 'error');
      }
    })
    .finally(() => {
      visible.value = false;
    });
};

const getIsDueTime = overdueTime => {
  if (overdueTime) {
    return dayjs().valueOf() > overdueTime;
  } else {
    return false;
  }
};

const handleToDo = () => {
  emit('handleTodo', props.toDoObj);
};

const deleteTodo = () => {
  emit('deleteTodo', props.toDoObj);
};
const handleClick = () => {
  globalStore.setUserId(props.toDoObj.patientId);
};

defineExpose({
  closePopover,
});
</script>

<style scoped lang="less">
.card-container {
  position: relative;
  box-sizing: border-box;
  padding: 16px;
  background-color: #f7f8fa;
  margin-bottom: 8px;
  .info-row {
    font-size: 14px;
    display: flex;
    .info-item {
      flex: 1;
      display: flex;
      .info-label {
        width: 78px;
        color: #7a8599;
      }
      .info-content {
        color: #3a4762;
      }
    }
  }
  .info-row:not(:last-child) {
    margin-bottom: 8px;
  }
  .btn-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      .delete-btn {
        color: #e63746;
        cursor: pointer;
      }
    }
    .right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .text-btn {
        margin: 0 4px;
        cursor: default;
      }
      .delay-text {
        color: #e63746;
      }
      .remind-text {
        color: #2e6be6;
      }
      .btn {
        box-sizing: border-box;
        padding: 0 8px;
        margin: 0 4px;
        min-width: 68px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .cancel-btn {
        color: #3a4762;
        background: #e8eaed;
        border-radius: 2px;
        border: 1px solid #dcdee0;
      }
      .confirm-btn {
        color: #ffffff;
        background: #2e6be6;
        border-radius: 2px;
      }
    }
  }
  .tag {
    box-sizing: border-box;
    color: #7a8599;
    padding: 4px 6px;
    background: #e1e5ed;
    border-radius: 2px 0px 0px 2px;
    position: absolute;
    right: 0;
    top: 8px;
  }
}
.deal-style::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(247, 248, 250, 0.5);
}
.popover-top {
  display: flex;
  align-items: center;
  .label {
    margin-right: 8px;
    font-size: 14px;
    color: #323233;
  }
}
.popover-bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  .btn {
    box-sizing: border-box;
    padding: 1px 8px;
    cursor: pointer;
    margin: 0 8px;
  }
  .cancel {
    color: #3a4762;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
  }
  .confirm {
    color: #ffffff;
    background: #2e6be6;
    border-radius: 2px;
  }
}
</style>
