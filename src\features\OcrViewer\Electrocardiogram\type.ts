export interface TypeCheckHeadParams {
  type?: number;
  patientHistoryId?: number | null;
  checkTime?: string;
  sourceType?: number;
  sourceId?: number | null;
}

// 使用模板字符串模板类型声明对象
export type TypeRecordReqMap = {
  [key: number]: (data: any) => Promise<unknown>;
};

export interface TypeTimeOptions {
  patientHistoryId: number;
  name: string;
}

export interface TypeCheckItem {
  checkType: number;
  indexType: number;
  name: string;
  disable?: boolean;
}

export interface TypeTableCheckItem {
  checkType?: number;
  indexType?: number;
  indexName: string;
  checkName: string;
  isEdit: boolean;
  rows: number;
  content: string | null | number;
}

export interface TypeAddCheckItem {
  indexName: string;
  content: string | null;
}
