<template>
  <el-dialog
    :model-value="props.visible"
    :width="props.width ?? 400"
    :modal="false"
    :align-center="true"
    :close-on-click-modal="false"
    class="hrt-custom-dialog"
    v-bind="attrs"
    :draggable="draggable"
    @close="onClose"
  >
    <!-- 如果存在header插槽，则使用header插槽 -->
    <template v-if="slots.header" #header>
      <div class="header-title">
        <slot name="header"></slot>
      </div>
    </template>
    <template v-else-if="title" #header="{ titleId, titleClass }">
      <div class="header-title">
        <h4 :id="titleId" :class="titleClass">{{ title }}</h4>
      </div>
    </template>
    <!-- 这里是弹窗内容插槽，超出弹窗默认最大高度（页面高度70%）将被滚动-->
    <slot></slot>
    <template #footer>
      <!-- 这里是末尾的元素插槽，比如提交取消按钮-->
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
  draggable?: boolean;
  width?: number | string;
  title?: string;
}>();

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}>();

const attrs = useAttrs();
const slots = useSlots();

const onClose = () => {
  emits('update:visible', false);
  emits('close');
};
</script>

<style lang="less">
.hrt-custom-dialog {
  max-width: 80%;
  max-height: 90%;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  padding: 0;
  box-shadow: 0 2px 24px 0 rgba(200, 201, 204, 0.5);
  .el-dialog__header {
    padding: 0;
    .header-title {
      height: 48px;
      padding: 0 16px;
      border-bottom: 1px solid #e9e8eb;
      font-size: 16px;
      font-weight: bold;
      color: #101b25;
      display: flex;
      align-items: center;
      justify-content: space-between;
      h4 {
        font-size: 16px;
      }
    }
    .el-dialog__headerbtn {
      height: 48px;
      .el-icon {
        font-size: 20px;
      }
    }
  }
  .el-dialog__body {
    padding: 0 0 16px 0;
    flex: 1;
    overflow-y: overlay;
  }
  .el-dialog__footer {
    padding: 0;
    margin: 0;
  }
}
</style>
