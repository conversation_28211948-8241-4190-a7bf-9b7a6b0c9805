<template>
  <OccurrenceOfIllness
    :key="renderKey"
    ref="surgicalInformation"
    :source-type="sourceType"
    :resource-id="sourceId"
    :cards="[FormCategory.DIAGNOSE_REPORT]"
    :additional-data="additionalData"
    :mode="sourceId ? 'view' : 'create'"
    :show-header="false"
  />
</template>
<script setup lang="ts">
import { getDiagnosisReportDetails } from '@/api/disease';
import { idsTransform } from '@/components/DiseaseSelector/util';
import { FormCategory, SourceType, SourceTypeValues } from '@/constant';
import { OccurrenceOfIllness } from '@/features';
import { IApiCaseHistorySearchDiagnosisReportItem } from '@/interface/type';
import { formatTimeTemplate } from '@/utils';
import { cloneDeep, every, find } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';
defineOptions({ name: 'Inspection' });
import bus from '@/lib/bus';

export type ActionType = 'add' | 'view' | 'edit';
interface IDataItem extends Required<IApiCaseHistorySearchDiagnosisReportItem> {
  actionType: 'add' | 'view' | 'edit' | 'delete';
}

const globalStore = useGlobal();
const {
  actionType = 'add',
  sourceId = 0,
  sourceType = SourceType.HOSPITAL,
  renderKey = 0,
} = defineProps<{
  actionType: ActionType;
  sourceId: number;
  sourceType: SourceTypeValues;
  renderKey: number;
}>();

const emits = defineEmits<{
  (e: 'on-status-change', data: ActionType): void;
  (e: 'update-total', data: number): void;
  (e: 'update-source-id', id: number): void;
  (e: 'get-data-list', data: any): void;
}>();

// 是否点击过新增报告单
const alreadyAddReport = ref(false);

const dataList = ref<IDataItem[]>([]);

const orginDataList = ref<IDataItem[]>([]);

/** 提交时额外携带的参数 */
const additionalData = computed(() => ({
  all: {
    patient_id: globalStore.userId,
    source_type: 2,
  },
}));
// 获取详情
const getDetail = async (sourceId: number) => {
  const res = await getDiagnosisReportDetails({
    sourceId,
    sourceType,
  });
  res?.forEach(item => {
    item.modifyTime = formatTimeTemplate(item.modifyTime)!;
  });
  const list = cloneDeep(dataList.value);
  orginDataList.value = cloneDeep(res) as IDataItem[];
  if (!list?.length) {
    dataList.value = res?.map(item => ({
      ...item,
      actionType: alreadyAddReport.value ? 'add' : actionType,
    }));
    return;
  }

  dataList.value = res?.map(item => {
    const { reportId, modifyTime, checkTime } = item;
    const listItem = find(
      list,
      obj => reportId === obj.reportId && modifyTime === obj.modifyTime
    );
    return (
      listItem ?? {
        ...item,
        actionType: checkTime ? 'view' : 'add',
      }
    );
  });
};

const dealReviewConclusionParams = () => {
  let dealedCheckIndex = orginDataList.value.filter(
    item => item.checkIndex && item.checkIndex.length
  );
  let indexList = dealedCheckIndex.map(item => {
    return {
      reportName: item.reportName,
      indexTermId: item.indexTermId,
      checkIndex: item.checkIndex,
    };
  });
  let conclusionIndexTermId = [60, 61, 67];
  let ecgConclusion = orginDataList.value.filter(item =>
    conclusionIndexTermId.includes(item.indexTermId)
  );
  let ecgList = ecgConclusion.map(item => {
    return {
      ecgName: item.reportName,
      conclusion: handleAssembleExamineLise(item.conclusions).map(
        item => item.text
      ),
    };
  });
  return { indexList, ecgList };
};

const handleAssembleExamineLise = (list: any[]) => {
  if (!list?.length) return [];
  const { showNameList } = idsTransform(
    list.map(conclusion => conclusion.conclusionId),
    list.reduce((pre, cur) => {
      const { conclusionId, name, remark, pid } = cur;
      return {
        ...pre,
        [conclusionId]: {
          ...cur,
          pId: pid,
          diseaseId: conclusionId,
          diseaseName: `${name}${remark ? '-' + remark : ''}`,
        },
      };
    }, {})
  );
  return showNameList;
};

const surgicalInformationRef = useTemplateRef('surgicalInformation');
const updata = () => {
  surgicalInformationRef.value?.refresh?.();
};

onUnmounted(() => {
  bus.off('batch-ocr-success-refresh-data', updata);
});

defineExpose({
  dealReviewConclusionParams,
});

onMounted(() => {
  if (sourceId) getDetail(Number(sourceId));
  bus.on('batch-ocr-success-refresh-data', updata);
});
watch(
  () => renderKey,
  () => {
    if (sourceId) getDetail(Number(sourceId));
  }
);

watch(
  dataList,
  list => {
    let cAcType = actionType;
    const len = list?.length || 0;
    if (!len || every(list, { actionType: 'view' })) {
      cAcType = 'view';
    } else {
      cAcType = 'edit';
    }
    emits('on-status-change', cAcType);
    emits('update-total', len);
    emits('get-data-list', dataList.value);
  },
  {
    deep: true,
  }
);
</script>

<style scoped lang="less">
:deep(.check-list) {
  .header {
    display: none;
  }
  .content {
    max-height: 440px;
  }
}
.title {
  padding: 0 26px;
  height: 44px;
  line-height: 44px;
  background: #f7f8fa;
  position: relative;

  &::after {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--color-primary);
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
