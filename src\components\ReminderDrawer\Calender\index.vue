<template>
  <el-calendar ref="calendar" v-model="calenderDate">
    <template #header="{ date }">
      <el-icon :size="16" class="db-arrow" @click="selectDate('prev-year')">
        <i-ep-DArrowLeft />
      </el-icon>
      <el-icon :size="16" @click="selectDate('prev-month')">
        <i-ep-ArrowLeft />
      </el-icon>
      <span class="date-title">{{ date }}</span>
      <el-icon :size="16" @click="selectDate('next-month')">
        <i-ep-ArrowRight />
      </el-icon>
      <el-icon :size="16" class="db-arrow" @click="selectDate('next-year')">
        <i-ep-DArrowRight />
      </el-icon>
    </template>
    <template #date-cell="{ data }">
      <div class="cell-container" @click.stop="handleCheckDate(data)">
        <div class="date-number">
          <div
            :class="data.day === checkedDate ? 'is-selected' : ''"
            class="normal"
          >
            {{ data.day.split('-').slice(2).join('-') }}
          </div>
        </div>
        <div class="date-number">
          <div
            :class="['cla-box', !calenderTagMap[data.day] ? 'no-value' : '']"
          >
            {{ calenderTagMap[data.day] }}
          </div>
        </div>
      </div>
    </template>
  </el-calendar>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

import { useReminder } from '@/store/module/useReminder';

const reminderStore = useReminder();

interface Props {
  date: Date | null;
  initDate?: Date;
}
const props = defineProps<Props>();

const emit = defineEmits([
  'update:date',
  'changeCheckedDate',
  'getMonthDate',
  'update:initDate',
]);

const calenderTagMap = computed(() => reminderStore.calenderTagMap);

const calendar = ref();

const checkedDate = ref('');

const selectDate = async (val: string) => {
  calendar.value.selectDate(val);
  emit('getMonthDate', calenderDate.value);
  emit('update:initDate', calenderDate.value);
};

const calenderDate = ref<Date | null>(new Date());

const handleCheckDate = data => {
  checkedDate.value = data.day;
  // emit('update:date', new Date(checkedDate.value));
  emit('changeCheckedDate', new Date(checkedDate.value));
};

const initCalenderMonthPage = () => {
  if (props.date?.getMonth() === props.initDate?.getMonth()) {
    calenderDate.value = props.date;
  } else {
    if (props.initDate) calenderDate.value = props.initDate;
  }
  checkedDate.value = dayjs(props.date).format('YYYY-MM-DD');
};

watch(
  () => checkedDate.value,
  () => {
    emit('update:date', new Date(checkedDate.value));
  },
  {
    deep: true,
  }
);
// watch(
//   () => props.date,
//   async () => {
//     initCalenderMonthPage();
//   },
//   {
//     deep: true,
//   }
// );

onMounted(() => {
  initCalenderMonthPage();
});
</script>

<style scoped lang="less">
:deep(.el-calendar__header) {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: none;
  padding-top: 14px;
  padding-bottom: 0;
  .date-title {
    font-weight: bold;
    color: #3a4762;
    padding: 0 42px;
  }
  .el-icon {
    cursor: pointer;
    width: 2rem;
  }
}
:deep(.el-calendar__body) {
  padding: 0;
  .el-calendar-table thead th {
    color: #939cae;
    font-size: 14px;
  }
  .el-calendar-table tbody .el-calendar-table__row td {
    border-color: #fff;
    border-width: 4px;
  }
  .el-calendar-table tbody .el-calendar-table__row .prev {
    background-color: #fff;
    .cla-box {
      display: none;
    }
  }
  .el-calendar-table tbody .el-calendar-table__row .next {
    background-color: #fff;
    .cla-box {
      display: none;
    }
  }
  .el-calendar-table tbody .el-calendar-table__row .current {
    background-color: #f7f8fa;
    .cla-box {
      min-width: 24px;
      min-height: 24px;
      background-color: #e6eeff;
      text-align: center;
      margin-top: 4px;
      color: #2e6be6;
    }
    .no-value {
      background-color: #f7f8fa;
      opacity: 0;
    }
  }
  .el-calendar-table tbody .el-calendar-table__row td .el-calendar-day {
    min-height: 64px;
    height: fit-content;
  }
  .el-calendar-table tbody .el-calendar-table__row td .el-calendar-day {
    min-height: 64px;
    height: fit-content;
  }
}
.cell-container {
  width: 100%;
  height: 100%;
  .date-number {
    display: flex;
    justify-content: flex-end;
    .normal {
      width: 26px;
      height: 26px;
      border-radius: 50%;
      text-align: center;
      font-size: 16px;
      color: #3a4762;
    }
    .is-selected {
      color: #ffffff;
      background-color: #0a73e4;
    }
    .cla-number {
    }
  }
}
</style>
