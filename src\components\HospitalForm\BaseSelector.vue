<template>
  <div>
    <SymptomInput
      v-if="!hideInput"
      ref="symptomInputRef"
      :content="historyDiseaseContent"
      @update:content="updateContent"
      @get-text-to-ocr="getTextToOcr"
    />
    <div class="box-border pb-8">
      <StructuredSelector
        ref="selectorRef"
        :title="title"
        :disease-list="list"
        :disease-data="diseaseSelectorData"
        @on-change="getDiseaseStrs"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SymptomInput from './components/SymptomInput.vue';
import StructuredSelector from '@/components/StructuredSelector/index.vue';
import {
  IDiseaseData,
  IDiseaseItem,
  IShowNameList,
} from '@/components/StructuredSelector/type';
import { BaseProps, IStructItem } from './type';
import { ocrTextToData } from '@/api/ocr';
import { keyBy } from 'lodash-es';

export type ICategory =
  | 'chief_complaint' // 主诉
  | 'personal_history' // 个人史
  | 'family_disease_history' // 家庭史
  | 'past_disease_history' // 既往史
  | 'physique_data' // 体格数据
  | 'discharge_diagnosis'; // 出院诊断

interface Props extends BaseProps {
  selectedData?: IStructItem[] | null;
  list: IDiseaseItem[];
  title?: string;
  hideInput?: boolean;
  /** 文本内容 */
  textContent?: string;
  category?: ICategory;
  ocrReq?: (sentense: string) => Promise<any>;
}

const props = withDefaults(defineProps<Props>(), {
  selectedData: null,
  textContent: '',
});

const emit = defineEmits(['onChange']);

const selectorRef = shallowRef();
const symptomInputRef = ref<InstanceType<typeof SymptomInput> | null>(null);
const historyDiseaseContent = ref<string>('');
/** 选中的疾病数据 */
const diseaseData = ref<IStructItem[]>([]);
const diseaseInfoRes = ref<IShowNameList>({
  ids: [],
  selectedPath: [],
  showNameList: [],
});
const diseaseSelectorData = computed<IDiseaseData>(() => {
  let diseaseIds: number[] = [];
  diseaseData.value?.forEach(v => {
    if (v.id) {
      diseaseIds.push(v.id);
    }
  });
  diseaseIds = [...new Set(diseaseIds)];
  return { diseaseIds: diseaseIds };
});
const updateContent = value => {
  historyDiseaseContent.value = value;
  dispatchDiseaseParams();
};

const getDiseaseStrs = (info: IShowNameList) => {
  diseaseInfoRes.value = info;
  dispatchDiseaseParams();
};

const dispatchDiseaseParams = () => {
  const ids = diseaseInfoRes.value.ids;
  const valueStr = diseaseInfoRes.value.showNameList.map(v => v.text);
  const flatPath = diseaseInfoRes.value.selectedPath.flat();
  const flatPathMap = keyBy(flatPath, 'id');
  const value = ids.map(id => ({
    id,
    key: flatPathMap[id].text,
    value: flatPathMap[id].text,
  }));

  const obj = {
    ids,
    resultTreeData: diseaseInfoRes.value.resultTreeData ?? [],
    showNameList: diseaseInfoRes.value.showNameList,
    content: historyDiseaseContent.value,
    value,
    valueStr,
  };
  emit('onChange', obj);
};

const getTextToOcr = (sentence: string) => {
  let params = {
    sentence,
    sessionName: props.category,
  };
  ocrTextToData(params)
    .then(data => {
      if (data.resultStatus === 1) {
        const result = JSON.parse(data.aiParseData);
        const ocrData = result?.sentenceList?.[0]?.session_out ?? [];
        console.log('$debug: session_out', ocrData);
        const baseIds = ocrData.map(v => v.id);
        const pathIds = [...new Set(ocrData.map(v => v.path).flat())].filter(
          v => v !== undefined
        );
        const ids = pathIds?.length ? pathIds : baseIds;
        const res = selectorRef.value?.idsTransform(ids);
        const flatData = res.selectedPath?.flat();
        diseaseData.value = flatData.map(v => ({
          id: v.id,
          value: v.text,
        }));
      } else {
        ElMessage({
          message: '识别失败,请重新识别!',
          type: 'error',
        });
      }
      symptomInputRef.value?.closeLoading();
    })
    .catch(err => {
      ElMessage({
        message: `识别失败,${err.message}!`,
        type: 'error',
      });
      symptomInputRef.value?.closeLoading();
    });
};

const initDiseaseData = () => {
  diseaseData.value = props.selectedData ?? [];
  historyDiseaseContent.value = props.textContent ?? '';
};
defineExpose({
  getDiseaseInfo: () => diseaseInfoRes.value,
});
watch(
  () => props.selectedData,
  val => {
    diseaseData.value = val ?? [];
  },
  { deep: true, immediate: true }
);
watch(
  () => props.textContent,
  val => {
    historyDiseaseContent.value = val ?? '';
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  initDiseaseData();
});
defineOptions({
  name: 'Diagnose',
});
</script>

<style scoped lang="less"></style>
