<template>
  <div class="h-270 bg-gray-200 todo-box mb-8">
    <div class="todo-header flex items-center justify-between">
      <div class="header-left flex items-center">
        <span class="text-base header-title">患者待办</span>
        <span class="todo-number text-base ml-8 mr-11">({{ totleList }})</span>
        <img
          :src="addImg"
          alt=""
          class="w-14 h-14 cursor-pointer mt-2"
          @click="addToDo"
        />
      </div>
      <div class="header-middle">
        <el-checkbox
          v-model="useTodoInfo.lookMySelf"
          label="仅看我的"
          size="large"
          class="look-me"
          @change="lookMyself"
        />
      </div>
      <div
        class="header-right flex items-center cursor-pointer text-sm"
        @click="queryAllToDo"
      >
        待办列表
        <el-icon size="12px">
          <i-ep-arrow-right color="#2E6BE6" />
        </el-icon>
      </div>
    </div>
    <ul class="todo-main">
      <li
        v-for="(item, index) in todoList"
        :key="index + item.backlogId"
        class="item-todo mt-8"
      >
        <div v-if="item.type" class="have-data">
          <div class="top flex items-center justify-between">
            <div class="top-left flex items-center">
              <ShowImg :type="item.type" />
              <span class="top-title ml-8">
                {{ getType(item.type) }}
                <span
                  v-if="getTime(item.term).includes('已逾期')"
                  class="overdue"
                >
                  {{ getTime(item.term) }}
                </span>
                <span
                  v-if="
                    getTime(item.term).includes('今天') ||
                    getTime(item.term).includes('明天')
                  "
                  class="time-num"
                >
                  {{ getTime(item.term) }}
                </span>
              </span>
            </div>
            <div class="top-right flex items-center">
              <div
                v-if="item.type === 10"
                class="detele cursor-pointer mr-16"
                @click="detele(item)"
              >
                删除
              </div>
              <div class="postpone cursor-pointer" @click="postpone(item)">
                稍后提醒
              </div>
              <div
                v-if="item.type !== 12"
                class="handle cursor-pointer ml-16"
                @click="handleToDo(item)"
              >
                处理
              </div>
            </div>
          </div>
          <div class="bottom flex items-center mt-4 justify-between">
            <div class="ml-23 bottom-content">
              <Text>{{ item.content }}</Text>
            </div>
            <div class="content-right">
              <span>
                {{
                  convertDateTime(
                    item.type === 10 ? item.remindTime : item.term
                  ).integrityTime
                }}
              </span>
              <span class="ml-24">{{ item.headName }}</span>
            </div>
          </div>
        </div>
        <div v-else class="flex items-center justify-center h-40 no-todo">
          暂无待办
        </div>
      </li>
    </ul>
  </div>

  <!-- 相关待办弹窗组件 -->
  <Dialog
    v-if="dialogVisible"
    :width="width"
    :dialog-visible="dialogVisible"
    @close-dialog="closeDialog"
  />

  <!-- 待办列表 -->
  <ToDoList :show-todo-list="showTodoList" @todo-commit="todoCommit" />

  <!-- 处理自定义待办 -->
  <SecondaryConfirmation
    :custom-type="customType"
    :custom-title="customTitle"
    @close="closeCustom"
  />
</template>

<script lang="ts" setup>
import addImg from '@/assets/imgs/callCenter/add.png';
import Dialog from './components/Dialog.vue';
import ToDoList from './components/ToDoList.vue';
import SecondaryConfirmation from './components/SecondaryConfirmation.vue';
import Text from '@/components/Text/index.vue';
import { todoListApi } from '@/api/todo';
import {
  getUserRoles,
  getType,
  convertDateTime,
  getTime,
  getRiskType,
  riskTabTitleMap,
} from './index';
import bus from '@/lib/bus';
import useTodo from '@/store/module/useTodo';
import store from '@/store';
import { IApiPatientReportListData } from '@/interface/type';
import ShowImg from './components/ShowImg.vue';
import dayjs from 'dayjs';
import { getFollowSymptomDetail } from '@/api/followup';
import useUserStore from '@/store/module/useUserStore';
import useDoubt from '@/store/module/useDoubt';
import useTabs from '@/store/module/useTabs';

let useTodoInfo = useTodo();
const tabStore = useTabs();
const useGlobalInfo = store.useGlobal();
const tabAction = store.useComponentsTabAction();
const userStore = useUserStore();
const doubtStore = useDoubt();

let queryInfo = ref({
  pageNumber: 1,
  pageSize: 10,
  headId: userStore.accountId,
  headRole: getUserRoles(),
  onlyMine: Number(useTodoInfo.lookMySelf),
  patientId: useGlobalInfo.userId,
});

watch(
  () => useGlobalInfo.userId,
  () => {
    if (useGlobalInfo.userId) {
      queryInfo.value.patientId = useGlobalInfo.userId;
      queryInfo.value.pageNumber = 1;
      todoList.value = [];
      getTodoList();
    }
  }
);

onMounted(() => {
  if (useGlobalInfo.userId) {
    getTodoList();
  }
  bus.on('updete-todo-list', () => {
    getTodoList();
  });
});

// 仅看我的
let lookMyself = () => {
  queryInfo.value.onlyMine = Number(useTodoInfo.lookMySelf);
  queryInfo.value.pageNumber = 1;
  todoList.value = [];
  if (useGlobalInfo.userId) getTodoList();
};

// 获取列表
let totleList = ref<number>(0);
let getTodoList = () => {
  queryInfo.value.onlyMine = Number(useTodoInfo.lookMySelf);
  todoListApi(queryInfo.value).then((res: any) => {
    let { code, data } = res;
    if (code === 'E000000' && data && data.backlogResponseList) {
      totleList.value = data.totals;
      todoList.value = data.backlogResponseList;
      if (data.backlogResponseList.length < 3) {
        for (let i = 0; i < 4 - todoList.value.length; i++) {
          todoList.value.push({
            backlogId: 0,
            content: '',
            headId: 0,
            headRole: 0,
            patientId: 0,
            headName: '',
            imageUrl: '',
            overdueTime: '',
            term: '',
            type: 0,
          });
        }
      } else {
        todoList.value = data.backlogResponseList.filter(
          (item: any, index: number) => index < 3
        );
      }
    } else {
      todoList.value = [];
      totleList.value = 0;
      for (let i = 0; i < 3; i++) {
        todoList.value.push({
          backlogId: 0,
          content: '',
          headId: 0,
          headRole: 0,
          patientId: 0,
          headName: '',
          imageUrl: '',
          overdueTime: '',
          term: '',
          type: 0,
        });
      }
    }
  });
};

const dialogVisible = ref<boolean>(false);
// 弹窗的宽度
let width = ref<string>('32%');
// 关闭弹窗
let closeDialog = () => {
  dialogVisible.value = false;
  if (showTodoList.value) {
    bus.emit('updete-todo-list');
  } else {
    queryInfo.value.pageNumber = 1;
    todoList.value = [];
    getTodoList();
  }
};

// 查看所有的待办
let showTodoList = ref<boolean>(false);
let queryAllToDo = () => {
  if (useGlobalInfo.userId) showTodoList.value = true;
};

let todoCommit = () => {
  // 1-新增待办
  let status = useTodoInfo.status;
  if (status === 0) {
    showTodoList.value = false;
    queryInfo.value.pageNumber = 1;
    todoList.value = [];
    getTodoList();
  }
  if (status === 1) {
    addToDo();
  }
  if (status === 2) {
    postpone(useTodoInfo.todoInfo);
  }
  if (
    status === 3 ||
    status === 4 ||
    status === 9 ||
    status === 5 ||
    status === 6 ||
    status === 7 ||
    status === 8 ||
    status === 11 ||
    status === 999
  ) {
    handleToDo(useTodoInfo.todoInfo);
  }
};

// 待办列表
// 待办类型(1患者入组、2入组宣教、3复查提醒、4风险处理、5症状异常处理、6调药跟踪、7门诊跟踪、8住院跟踪、9随访提醒、10监测提醒、11自定义待办),
interface info {
  backlogId: number;
  content: string;
  headId: number;
  headRole: number;
  patientId: number;
  headName: string;
  imageUrl: string;
  overdueTime: string;
  term: string;
  type: number;
  remindTime?: any;
}
let todoList = ref<info[]>([]);

// 删除
let customType = ref<boolean>(false);
let customTitle = ref<string>('');
let detele = (item: info) => {
  useTodoInfo.setTodoInfo(item);
  customType.value = true;
  customTitle.value = '删除';
};
let closeCustom = () => {
  customType.value = false;
  queryInfo.value.pageNumber = 1;
  todoList.value = [];
  getTodoList();
};
// 延期
let postpone = (item: info) => {
  dialogVisible.value = true;
  useTodoInfo.setStatus(2);
  width.value = '21%';
  useTodoInfo.setTodoInfo(item);
};
// 处理
let handleToDo = (item: any) => {
  console.log('$debug: item', item);
  if ([1, 18].includes(item.type)) {
    let obj = {
      sourceId: item.sourceId,
      checkTime: item.content.slice(0, 10),
      actionType: !item.sourceId ? 'add' : 'view',
      sourceType: 3,
      name: '入组',
    };
    showTodoList.value = false;
    toTab(obj);
  } else if (item.type === 2) {
    useTodoInfo.setStatus(3);
    init(item);
  } else if (item.type === 3) {
    useTodoInfo.setStatus(4);
    init(item);
  } else if (item.type === 4) {
    tabAction.setAction({
      componentType: 4,
      name: riskTabTitleMap[getRiskType(item.content)],
      mainTabCode: 2,
      mode: 'new',
      data: {
        sourceId: item.sourceId,
        sourceType: 2,
        riskType: getRiskType(item.content),
      },
    });
    bus.emit('open-component-tab');
    showTodoList.value = false;
  } else if (item.type === 5) {
    getFollowSymptomDetail({ followUpId: item.sourceId }).then(res => {
      let { date } = res;
      const followUpData = {
        followUpDate: date,
        followUpId: item.sourceId,
        sourceId: item.sourceId,
        sourceType: 1,
        key: item.sourceId,
      };
      tabAction.setAction({
        componentType: 6,
        name: dayjs(date).format('YYYY-MM-DD') + ' 症状随访',
        data: followUpData,
      });
      bus.emit('open-component-tab');
      showTodoList.value = false;
    });
  } else if (item.type === 9) {
    useTodoInfo.setStatus(9);
    init(item);
  } else if (item.type === 6) {
    useTodoInfo.setStatus(6);
    init(item);
  } else if (item.type === 7) {
    useTodoInfo.setStatus(7);
    init(item);
  } else if (item.type === 8) {
    useTodoInfo.setStatus(8);
    init(item);
  } else if (item.type === 10) {
    customType.value = true;
    customTitle.value = '完成';
    useTodoInfo.setStatus(10);
    init(item);
  } else if (item.type === 11) {
    useTodoInfo.setStatus(11);
    init(item);
  } else if (item.type === 12) {
  } else if (item.type === 13) {
    // 复查结论填写
    tabAction.setAction({
      componentType: 2,
      name: dayjs(item.time).format('YYYY-MM-DD') + ' 复查',
      mode: 'new',
      data: {
        id: item.sourceId,
        recordActionType: {
          actionType: 'view',
          sourceType: 2,
        },
      },
    });
    bus.emit('open-component-tab');
    showTodoList.value = false;
  } else if ([14, 15].includes(item.type)) {
    // 临床事件跟踪、临床事件核实 打开新建事件弹窗
    tabStore.clearCache();
    const group = 'patient_info';
    const curTab = tabStore.getCurrentPatientTabs(group);
    if (curTab) {
      const curId = curTab?.[1]?.id ?? '';
      if (curId) {
        tabStore.mainActiveTab = 2;
        tabStore.patientActiveTabMap[group] = curTab?.[1]?.id ?? '';
      }
    }
    setTimeout(() => {
      bus.emit('open-clinical-event');
    });
    showTodoList.value = false;
  } else if (item.type === 16) {
    // 量表随访提醒
    useTodoInfo.setStatus(999);
    init(item);
  } else if (item.type === 17) {
    doubtStore.backlogId = item.backlogId;
    doubtStore.doubtCheck(item.sourceId).then(() => {
      showTodoList.value = false;
    });
    // 访视质疑
  } else if (item.type === 19) {
    // 科研入组宣教
    useTodoInfo.setStatus(999);
    init(item);
  }
};
let init = (item: any) => {
  useTodoInfo.setTodoInfo(item);
  if (item.type !== 10) dialogVisible.value = true;
  if (item.type === 5) {
    width.value = '26%';
  } else {
    width.value = '21%';
  }
};

// 新增待办
let addToDo = () => {
  if (useGlobalInfo.userId) {
    dialogVisible.value = true;
    useTodoInfo.setStatus(1);
    width.value = '32%';
  }
};

const toTab = (item: IApiPatientReportListData) => {
  const { sourceId, sourceType, checkTime, actionType } = item;
  const curSourceType = [0, 1, 2, 3].includes(sourceType!) ? sourceType! : -1;
  tabAction.setAction({
    componentType: curSourceType,
    name: `${checkTime || ''} ${item.name}`,
    data: {
      id: sourceId,
      recordActionType: {
        actionType,
        sourceType: curSourceType,
      },
    },
  });
  bus.emit('open-component-tab');
};
</script>
<style scoped lang="less">
.todo-box {
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  border-radius: 0px 0px 6px 6px;
  background: #ffffff;
  padding: 16px;
  box-sizing: border-box;
  .todo-header {
    .header-left {
      .header-title {
        font-weight: 700;
        color: #101b25;
      }
      .todo-number {
        font-weight: 700;
        color: #e63746;
      }
    }
    .header-middle {
      :deep(.look-me) {
        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #3a4762;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
          background: #2e6be6;
          border-color: #2e6be6;
        }
      }
    }
    .header-right {
      color: #2e6be6;
    }
  }
  .todo-main {
    .item-todo {
      padding: 10px 12px;
      background: #f7f8fa;
      border-radius: 4px;
      height: 60px;
      .no-todo {
        font-size: 14px;
        color: #bac8d4;
      }
      .have-data {
        .top {
          .top-left {
            .top-title {
              font-size: 14px;
              font-weight: 700;
              color: #3a4762;
              .overdue {
                color: #e63746;
              }
              .time-num {
                color: #e37221;
              }
            }
          }
          .top-right {
            font-size: 14px;
            .detele {
              color: #e63746;
            }
            .postpone {
              color: #e37221;
            }
            .handle {
              color: #2e6be6;
            }
          }
        }
        .bottom {
          font-size: 14px;
          color: #7a8599;
          .bottom-content {
            white-space: nowrap; /* 防止换行 */
            overflow: hidden; /* 隐藏溢出部分 */
            text-overflow: ellipsis; /* 显示省略号 */
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
