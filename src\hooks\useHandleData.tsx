import { WarningFilled } from '@element-plus/icons-vue';

/**
 * @description 操作【单条数据信息、单次行为】 (二次确认【变更、删除等】)
 * @param {Function} api 操作数据接口的api方法 (必传)
 * @param {Object} params 携带的操作数据参数 {id,params} (必传)
 * @param {String} message 提示信息 (必传)
 * @param {String} description 描述信息
 * @returns {Promise}
 */
export const useHandleData = (
  api: (params: any) => Promise<any>,
  params: any = {},
  message: string,
  description?: string,
  loadingText?: string
) => {
  return new Promise((resolve, reject) => {
    ElMessageBox({
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      message: renderElMessage(message, description),
      showCancelButton: true,
      customClass: 're-confirm-el-message-box',
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = loadingText ?? '';

          await api(params);
          done();

          // if (!res) return reject(false);

          ElMessage.success('操作成功!');

          resolve(true);

          setTimeout(() => {
            instance.confirmButtonLoading = false;
          }, 300);
        } else {
          done();
        }
      },
    });
  });
};
export const useConfirmMessagebox = ({
  message,
  description,
}: {
  message: string;
  description?: string;
}) => {
  return new Promise(resolve => {
    ElMessageBox({
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      message: renderElMessage(message, description),
      showCancelButton: true,
      customClass: 're-confirm-el-message-box',
      beforeClose: async (action, _, done) => {
        done();
        if (action === 'confirm') {
          resolve(true);
        } else {
          resolve(false);
        }
      },
    });
  });
};
export function renderElMessage(
  message: string,
  description?: string,
  hideWarningIcon?: boolean
) {
  return (
    <>
      <div
        style={{
          padding: '14px 10px 0',
          display: 'flex',
          alignItems: 'flex-start',
        }}
      >
        {!hideWarningIcon && (
          <WarningFilled
            style={{
              color: '#e37221',
              width: '20px',
              margin: '2px 6px 0 0',
              flexShrink: 0,
            }}
          />
        )}
        <div>
          <div
            style={{
              color: '#111',
              fontSize: '16px',
              fontWeight: 500,
              paddingBottom: '6px',
              wordBreak: 'break-all',
            }}
          >
            {message}
          </div>
          {description && (
            <div style={{ color: '#7a8599', wordBreak: 'break-all' }}>
              {description}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
