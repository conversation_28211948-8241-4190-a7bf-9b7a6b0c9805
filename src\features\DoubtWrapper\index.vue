<template>
  <div class="doubt-item w-full" :class="{ 'doubt-active': highlight }">
    <span ref="referenceRef"></span>
    <div
      v-if="highlight"
      class="btn"
      :style="{ top: btnTop }"
      :class="{ 'btn-bottom': shouldMoveToBottom }"
      @click="submitHandler"
    >
      <img :src="doubtImg" />
      定向提交
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
import doubtImg from '@/assets/icons/doubt.png';
import { FormCategory } from '@/constant';
import useDoubt from '@/store/module/useDoubt';
import useGlobal from '@/store/module/useGlobal';

interface IProps {
  unitKey: string;
  disabled: boolean;
  data?: Record<string, any>;
  getData?: () => any;
}
const props = defineProps<IProps>();

const referenceRef = shallowRef();
const shouldMoveToBottom = ref(false);
const btnTop = ref();
const doubtStore = useDoubt();
const globalStore = useGlobal();

const currentKey = computed(() => props.unitKey.split('||')[1]);
const highlight = computed(() => {
  const unitType = doubtStore.unitData?.unitType?.toLowerCase();
  const unitCategory = doubtStore.unitData?.unitCategory?.toLowerCase();
  const curKey =
    unitCategory === FormCategory.DIAGNOSE_REPORT
      ? (doubtStore.unitData?.healthCenterKey ?? '') + unitType
      : unitType;
  return (
    globalStore.isDoubting && !props.disabled && currentKey.value === curKey
  );
});

const positionChangeData: any = inject('position-change', null);
const outerPositionChangeData: any = inject('outer-position-change', null);

const getReferenceTop = () => {
  const rect = referenceRef.value?.getBoundingClientRect();
  btnTop.value = rect?.top + 'px';
};

const submitHandler = () => {
  const params = props.data || props.getData?.();
  doubtStore.submitHandler(params);
};

watch(
  [() => positionChangeData.value, () => outerPositionChangeData.value],
  () => {
    if (highlight.value) {
      getReferenceTop();
    }
  }
);
defineOptions({
  name: 'DoubtWrapper',
});
</script>

<style scoped lang="less">
.doubt-item {
}
.doubt-active {
  background: #e6eeff;
  :deep(*) {
    background-color: #e6eeff !important;
  }
  :deep(.main) {
    color: #2e6be6 !important;
  }
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 99;
  left: 300px;
  width: 90px;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
  border-radius: 2px;
  color: #fff;
  text-align: center;
  font-size: 14px;
  background: #2e6be6 !important;
  > img {
    width: 14px;
    background: #2e6be6 !important;
    margin-right: 4px;
    height: 14px;
  }
}
</style>
