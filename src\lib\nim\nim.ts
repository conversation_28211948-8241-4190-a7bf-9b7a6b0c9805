import { transTime, buildSessionMsg, history } from './nim_util';
import { mergeMsgshandler } from './cache';
import defaultIcon from '@/assets/imgs/chat/defalut-drug.png';
import normalIcon from '@/assets/imgs/chat/normal.png';
import { debounce } from 'lodash-es';
declare const SDK: any;

export const imgIcons = {
  defaultIcon,
  normalIcon,
};

// 获取历史消息单次数量限制
export const MSG_LIMIT = 30;
// 消息定位获取消息数量总数限制 / 2,
export const FIND_MSG_LIMIT = 20;
// 聊天记录单次请求数限制
export const RECORD_LIMIT = 10;
let _nim: any = null;
let currentRole = '';
let currentUserName = '';
// 实例初始化重试次数,
let retryTimeLimit = 10;
interface IHistoryMsgParameters {
  teamId: string;
  msgTypes?: ('text' | 'image' | 'audio' | 'video')[];
  callback: (data: any) => void;
  readCallback?: (msg: any, val: boolean) => void;
  lastMsg?: {
    lastMsgId: string;
    endTime: number;
  };
  limit?: number;
  igoreFailed?: boolean;
}
export const setUserRoleInfo = () => {
  const userRoles = JSON.parse(localStorage.getItem('userRoles') ?? '[]');
  currentRole = userRoles?.[0];
  (window as any)._currentRole = currentRole;
  currentUserName = localStorage.getItem('userName') ?? '';
};
export const initNimSdk = (options: any) => {
  try {
    const {
      onMsg,
      onSessions,
      onUpdateSession,
      onTeamMsgReceipt,
      onTeams,
      setReady,
    } = options;
    setUserRoleInfo();
    const creatTimer = setTimeout(
      () => {
        if (retryTimeLimit) {
          console.log('NIM: 初始化异常，重新初始化' + retryTimeLimit);
          initNimSdk(options);
          retryTimeLimit--;
        }
      },
      (15 - retryTimeLimit) * 1000
    );
    const startTime = Date.now();
    console.log('NIM: 开始创建实例');
    const imAccid = localStorage.getItem('imAccid');
    const imToken = localStorage.getItem('imToken');
    if (!imAccid || !imToken) {
      console.log('NIM: imAccid 或 imToken 不存在！');
      return;
    }
    (window as any).nim = _nim = SDK.NIM.getInstance({
      appKey: import.meta.env.VITE_APP_nimAppKey,
      account: imAccid,
      token: imToken,
      syncSessionUnread: true,
      syncTeams: true,
      onconnect() {
        console.log('NIM: 连接成功');
        console.log('NIM: 创建实例耗时：', (Date.now() - startTime) / 1000);
        setReady(true);
        setUserRoleInfo();
        clearTimeout(creatTimer);
      },
      onwillreconnect() {
        console.log('NIM: 即将重连');
      },
      ondisconnect() {
        console.log('NIM: 断开连接');
      },
      onerror(error: any) {
        console.log('NIM: 发生错误：', error);
      },
      onloginportschange(loginPorts: any) {
        console.log('NIM: 当前登录帐号在其它端的状态发生改变了:', loginPorts);
      },
      onsyncdone() {
        console.log('NIM: 同步完成');
        clearTimeout(creatTimer);
      },
      onmsg: onMsg,
      onsessions: onSessions,
      onupdatesession: onUpdateSession,
      onTeamMsgReceipt: onTeamMsgReceipt,
      onteams: onTeams,
    });
  } catch (error: any) {
    ElMessage.error('IM 初始化错误: ' + error.message);
  }
};
export const rebuildSessionInfo = ({ msg, teamMap, curSession }: any) => {
  const info = {
    scene: msg.scene,
    account: msg.target,
    target: msg.scene + '-' + msg.target,
    time: transTime(msg.time),
    timestap: msg.time,
    nick: teamMap[msg.target].name,
    text: buildSessionMsg(msg),
    avatar: teamMap[msg.target].memberNum == 2 ? defaultIcon : normalIcon,
    unread: curSession?.unread ?? 0,
    idServer: msg.idServer,
    idClient: msg.idClient,
    status: msg.status,
    memberNum: teamMap[msg.target].memberNum,
  };
  return info;
};
export const sessionListHandler = (
  sessions: any,
  teamMap: any,
  callback: (data: any) => void
) => {
  const filterSessions = sessions.filter((session: any) => {
    const target = session.lastMsg.target;
    return !teamMap[target] || teamMap[target].name.indexOf('糖网医助-') === -1;
  });
  for (let i = 0; i < filterSessions.length; i++) {
    const curSession = filterSessions[i];
    const teamId = curSession.lastMsg.target;
    if (teamMap[teamId]) {
      const msg = sessions[i].lastMsg;
      const info = rebuildSessionInfo({ msg, teamMap, curSession });
      callback(info);
    } else {
      const teamId = filterSessions[i].lastMsg.target;
      const msg = filterSessions[i].lastMsg;
      const index = i;
      (window as any).nim.getTeam({
        teamId: teamId,
        done: (error: any, obj: any) => {
          if (obj && obj.valid && obj.validToCurrentUser) {
            const info = rebuildSessionInfo({
              msg,
              teamMap,
              curSession: filterSessions[index],
            });
            callback(info);
          }
        },
      });
    }
  }
};
export const updateSessisonHandler = ({ msg, teamMap }: any, callback: any) => {
  if (!teamMap[msg.target]) {
    callback();
  } else {
    const info = rebuildSessionInfo({ msg, teamMap });
    callback(info);
  }
};
export const getTteammsgReadAccounts = (
  msg: any,
  readCallback: (id: string, val: boolean) => void
) => {
  (window as any).nim.getTeamMsgReadAccounts({
    teamMsgReceipt: {
      teamId: msg.to,
      idServer: msg.idServer,
    },
    done: (error: any, _: any, content: any) => {
      if (!error) {
        const userIsRead = content.readAccounts.some(
          (v: string) => v.indexOf('user@') != -1
        );
        readCallback?.(msg, userIsRead);
      }
    },
  });
};
export const findTargetMsgs = ({
  teamId,
  callback,
  readCallback,
  idServer,
  time,
}: any) => {
  const _msgs: any = [];
  (window as any).nim.getHistoryMsgs({
    scene: 'team',
    to: teamId,
    endTime: time,
    lastMsgId: idServer,
    limit: FIND_MSG_LIMIT,
    done: (err: any, data: any) => {
      if (err) {
        console.log('$debug: 消息定位查找出错', err);
      } else {
        _msgs.push(...data.msgs);
        const lastMsg = _msgs.length
          ? _msgs[0]
          : {
              idServer,
              time: time - 1,
            };
        (window as any).nim.getHistoryMsgs({
          scene: 'team',
          to: teamId,
          beginTime: lastMsg.time,
          lastMsgId: lastMsg.idServer,
          reverse: true,
          limit: FIND_MSG_LIMIT,
          done: (err: any, data: any) => {
            if (!err) {
              _msgs.unshift(...data.msgs.reverse());
            }
            msgsDoneHandler({ msgs: _msgs, callback, readCallback });
          },
        });
      }
    },
  });
};
export const msgsDoneHandler = ({
  msgs,
  callback,
  readCallback,
  igoreFailed,
}: any) => {
  if (msgs.length) {
    for (let i = 0; i < msgs.length; i++) {
      // 获取已读未读
      if (msgs[i].needMsgReceipt) {
        if (readCallback) {
          getTteammsgReadAccounts(msgs[i], readCallback);
        }
      }
    }
    const _historyMsg = history(msgs);
    const result = igoreFailed ? _historyMsg : mergeMsgshandler(_historyMsg);
    callback(result);
  } else {
    callback([]);
  }
};
export const getHistoryMsgs = ({
  teamId,
  callback,
  lastMsg,
  msgTypes,
  limit,
  readCallback,
  igoreFailed,
}: IHistoryMsgParameters) => {
  (window as any).nim.getHistoryMsgs({
    scene: 'team',
    to: teamId,
    msgTypes,
    endTime: lastMsg?.endTime ? lastMsg.endTime : new Date().getTime(),
    lastMsgId: lastMsg?.lastMsgId ? lastMsg.lastMsgId : '',
    limit: limit || MSG_LIMIT,
    done: (err: any, data: any) => {
      if (err) {
        callback([]);
      } else {
        msgsDoneHandler({
          msgs: data.msgs,
          callback,
          readCallback,
          igoreFailed,
        });
      }
    },
  });
};
export const getMoreHistoryMsgHandler = (params: IHistoryMsgParameters) => {
  getHistoryMsgs(params);
};
export const getHistoryMsgHandler = (params: IHistoryMsgParameters) => {
  getHistoryMsgs(params);
};

export const getCustomData = () => {
  const data = {
    senderName: currentUserName,
    senderRole: currentRole,
  };
  return data;
};
export const getBaseSend = (options: any, callback: any) => {
  const msgInfo: any = {
    scene: 'team',
    to: options.to,
    idClient: options.idClient,
    resend: options.resend,
    needMsgReceipt: true,
    done: callback,
    custom: JSON.stringify(getCustomData()),
  };
  const env = import.meta.env.VITE_APP_nimEnv;
  if (env) msgInfo.env = env;
  return msgInfo;
};
export const sendCustomMsg = (options: any, callback: any) => {
  _nim.sendCustomMsg({
    ...getBaseSend(options, callback),
    id: options.id,
    content: JSON.stringify(options.content),
    done: callback,
  });
};
export const sendTextMsg = (options: any, callback: any) => {
  _nim.sendText({
    ...getBaseSend(options, callback),
    text: options.text,
  });
};
export const sendBlobMsg = (options: any, callback: any) => {
  _nim.sendFile({
    ...getBaseSend(options, callback),
    type: options.type,
    blob: options.blob,
  });
};
export const sendFileMsg = (options: any, callback: any) => {
  _nim.sendFile({
    ...getBaseSend(options, callback),
    type: 'image',
    file: options.file,
  });
};
export const layzyFn = debounce(call => {
  call();
}, 1000);
