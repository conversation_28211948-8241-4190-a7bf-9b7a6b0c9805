<template>
  <div class="w-full">
    <DrugTable
      v-if="type === 'drug'"
      :params-data="value"
      :readonly="mode === FormMode.VIEW"
      @change="drugFormChangeHandler"
    />
    <template v-else>
      <BaseTable
        v-if="visibleColumns?.length"
        :data="tabelFormData"
        :pagination="false"
      >
        <el-table-column
          v-for="(column, colIndex) in visibleColumns"
          :key="column.key"
          :prop="column.key"
          :label="column.value"
          :width="column.uiOptions?.columnWidth"
        >
          <template #default="scope">
            <div v-if="column.uiMethod === 'index'">
              {{ scope.$index + 1 }}
            </div>
            <el-select
              v-if="column.uiMethod === 'select'"
              :model-value="scope.row[column.key]"
              :placeholder="column.uiOptions?.placeholder ?? '请选择'"
              filterable
              :style="{ width: column.uiOptions?.selectWidth ?? '100px' }"
              class="normal-select"
              @change="val => changeHandler(val, scope.$index, column.key)"
            >
              <el-option
                v-for="(item, index) in column.uiOptions?.selectOptions"
                :key="
                  column.uiOptions?.valueKey
                    ? item[column.uiOptions?.valueKey]
                    : index
                "
                :label="
                  column.uiOptions?.labelKey
                    ? item[column.uiOptions?.labelKey]
                    : item
                "
                :value="
                  column.uiOptions?.valueKey
                    ? item[column.uiOptions?.valueKey]
                    : item
                "
              />
            </el-select>
            <TextInput
              v-if="scope.row[colIndex]?.uiMethod === 'input'"
              :mode="props.mode"
              :min="scope.row[colIndex]?.uiRules?.maxlength"
              :placeholder="column.uiOptions?.placeholder ?? '请输入'"
              :value="finalData?.[scope.$index]?.[column.key]"
              @change="val => changeHandler(val, scope.$index, column.key)"
            />
            <!-- TODO：InputNumber的其他属性需要填充-->
            <InputNumber
              v-if="scope.row[colIndex]?.uiMethod === 'inputNumber'"
              :model-value="scope.row[colIndex].value"
              :max="scope.row[colIndex]?.uiRules?.max"
              :min="scope.row[colIndex]?.uiRules?.min"
              :step="scope.row[colIndex]?.uiRules?.step"
              :precision="scope.row[colIndex]?.uiRules?.precision"
              :control="scope.row[colIndex]?.uiOptions?.control"
              :value="finalData?.[scope.$index]?.[column.key]"
              :placeholder="column.uiOptions?.placeholder ?? '请输入'"
              :mode="props.mode"
              @change="val => changeHandler(val, scope.$index, column.key)"
            />
            <div
              v-if="scope.row[colIndex]?.uiMethod === 'text'"
              class="normal-select"
            >
              {{ scope.row[colIndex].value }}
            </div>
          </template>
        </el-table-column>
      </BaseTable>
    </template>
  </div>
</template>

<script setup lang="ts">
import BaseTable from '@/components/BaseTable';
import InputNumber from '@/components/FormItem/InputNumber.vue';
import TextInput from '@/components/FormItem/TextInput.vue';
import { FormMode } from '@/constant';
import { ElOption } from 'element-plus';
import { groupBy, isEmpty, isEqual, keyBy } from 'lodash-es';
import { Column, ItemItem } from '../FormList/types';
import DrugTable from './DrugTable/index.vue';
import { Base } from './type';

interface IProps extends Base {
  columns: Column[];
  items?: ItemItem[];
  type?: 'drug';
  placeholder?: string;
  /** UI配置 */
  uiOptions?: Record<string, boolean | string>;
}

const tabelFormData = ref<any[]>([{}]);
const props = defineProps<IProps>();
const emit = defineEmits(['change']);

const finalData = ref<Record<string, any>[]>([]);

// 过滤不需要的列,然后根据rowOrder排序
const visibleColumns = computed(() =>
  props.columns
    ?.filter(v => !v?.uiDisable)
    .sort((a, b) => a?.rowOrder - b?.rowOrder)
);

const addEmptyRow = () => {
  tabelFormData.value.push({});
};

// 表格数据变化时，更新表单数据
const changeHandler = (val: any, rowIndex: number, key: string) => {
  // 如果值没有变化，不更新
  if (finalData.value[rowIndex][key] !== val) {
    finalData.value[rowIndex][key] = val;
    drugFormChangeHandler(finalData.value);
  }
};

const drugFormChangeHandler = (val: Record<string, any>[]) => {
  emit('change', val);
};

defineExpose({
  addEmptyRow,
});

watch(
  finalData,
  () => {
    emit('change', finalData.value);
  },
  { deep: true }
);

const getRowData = () => {
  const rows = Object.values(groupBy(props.items, 'rowId')).map(row =>
    row.sort((a, b) => (a?.rowOrder ?? 0) - (b?.rowOrder ?? 0))
  );
  const data: Record<string, any>[] = [];
  rows.forEach(row => {
    const rowData = {};
    row.forEach((item, colIndex) => {
      const col = props.columns?.[colIndex];
      // 如果是第一列，使用key作为列名
      const colKey = colIndex === 0 ? 'key' : col?.key;
      // 如果是第一列，或者uiMethod不是text，初始化行的数据
      if (colIndex === 0 || item['uiMethod'] !== 'text') {
        rowData[colKey] = item['uiMethod'] === 'text' ? item['key'] : undefined;
      }
    });
    data.push(rowData);
  });
  return data;
};
watch(
  () => props.value,
  () => {
    // 表单数据为空时，并且列配置不为空时，初始化表格数据
    if (
      isEmpty(props.value) &&
      !isEmpty(props.items) &&
      props.type !== 'drug'
    ) {
      finalData.value = getRowData();
    } else {
      // 判断两次数据不为空且值不一样时，更新表单数据
      if (!isEqual(props.value, finalData.value)) {
        if (props.type !== 'drug') {
          const initValue = getRowData();
          const res = props.value || [];
          const resMap = keyBy(res, 'key');
          for (const v of initValue) {
            if (resMap[v.key]) {
              Object.assign(v, resMap[v.key]);
            }
          }
          finalData.value = initValue;
        } else {
          finalData.value = props.value ?? [];
        }
      }
    }
  },
  { immediate: true }
);

watch(
  [() => props.items, () => props.columns],
  () => {
    tabelFormData.value = Object.values(groupBy(props.items, 'rowId')).map(
      row => row.sort((a, b) => (a?.rowOrder ?? 0) - (b?.rowOrder ?? 0))
    );
  },
  { immediate: true }
);

defineOptions({
  name: 'TableForm',
});
</script>

<style scoped lang="less">
.render-item {
  display: flex;
  justify-content: space-between;

  span {
    flex: 1;
    text-align: left;
  }

  span:not(:first-child) {
    margin-left: 20px;
  }
}
</style>
