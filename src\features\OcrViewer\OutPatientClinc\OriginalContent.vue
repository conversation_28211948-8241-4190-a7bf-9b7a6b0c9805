<template>
  <div
    ref="mainTextBox"
    class="main box-border overflow-y-auto overflow-x-hidden relative"
  >
    <div class="text-label box-border p-12 font-bold h-1/10">原文内容</div>
    <div class="main-content">
      <div
        id="text-container"
        class="text-content box-border overflow-auto h-9/10"
        v-html="originalText"
      ></div>
    </div>
    <!-- 标签选择 -->
    <div
      v-if="tagInfo.visible"
      class="tag-box"
      :style="{ top: tagInfo.top + 'px', left: tagInfo.left + 'px' }"
    >
      <div
        v-for="i in tags"
        :key="i.eventValue"
        class="tag-name"
        @click="clickTag"
      >
        {{ i.tagName }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useOcrScan } from '@/store/module/useOcrScan';
import { TypeSelectList } from './type';
import { useClipboard } from '@vueuse/core';

const ocrStore = useOcrScan();
const { copy } = useClipboard();
defineOptions({
  name: 'OriginalContent',
});
const originalText = ref<string>('');
const tagListMap = reactive<TypeSelectList>({
  1: [
    {
      tagName: '复制到主诉',
      eventValue: 1,
    },
    {
      tagName: '复制到现病史',
      eventValue: 5,
    },
    {
      tagName: '复制到个认史',
      eventValue: 2,
    },
    {
      tagName: '复制到家族史',
      eventValue: 3,
    },
    {
      tagName: '复制到既往史',
      eventValue: 4,
    },
  ],
  2: [
    {
      tagName: '复制到临床诊断',
      eventValue: 4,
    },
  ],
  4: [
    {
      tagName: '复制到主诉',
      eventValue: 1,
    },
    {
      tagName: '复制到现病史',
      eventValue: 5,
    },
    {
      tagName: '复制到诊断',
      eventValue: 4,
    },
  ],
});
interface TagItem {
  tagName: string;
  eventValue: number;
}
let tagList = reactive<TagItem[]>([
  {
    tagName: '复制到主诉/现病史',
    eventValue: 1,
  },
  {
    tagName: '复制到个认史',
    eventValue: 2,
  },
  {
    tagName: '复制到家族史',
    eventValue: 3,
  },
  {
    tagName: '复制到既往史',
    eventValue: 4,
  },
]);

// v3.4.6 版本过渡方案
const tags = [
  {
    tagName: '复制文本',
    eventValue: 0,
  },
];

onMounted(() => {
  //根据不同附件类型展示不同的标签
  originalText.value = ocrStore?.ocrResultData?.originalText ?? '';
  // tagList = tagListMap[ocrStore.annexType];
});

interface TagInfo {
  visible: boolean;
  top: number;
  left: number;
}
interface SelectedText {
  start: number;
  end: number;
  content: string;
}

const tagInfo = ref<TagInfo>({
  visible: false,
  top: 0,
  left: 0,
});

const selectedText = reactive<SelectedText>({
  start: 0,
  end: 0,
  content: '',
});
const mainTextBox = ref<HTMLDivElement | null>(null);
//容器常量,用于判断浮窗位置
const TAG_WIDTH = 150;

const TATOL_PADDING = 32;

onMounted(() => {
  if (
    ocrStore.annexType !== 1 &&
    ocrStore.annexType !== 2 &&
    ocrStore.annexType !== 4
  )
    return;

  const el = document.getElementById('text-container');
  //鼠标抬起
  el?.addEventListener(
    'mouseup',
    async e => {
      const text = window?.getSelection()?.toString() || '';
      await nextTick();
      let widthDom = window
        .getComputedStyle(mainTextBox.value as HTMLDivElement)
        .getPropertyValue('width');
      if (text.length > 0) {
        const left =
          e.offsetX + TAG_WIDTH > parseInt(widthDom) + TATOL_PADDING
            ? parseInt(widthDom) - TAG_WIDTH
            : e.offsetX;
        tagInfo.value = {
          visible: true,
          top: e.offsetY + 30,
          left: left,
        };
        getSelectedTextData();
      } else {
        tagInfo.value.visible = false;
      }
    },
    {
      passive: false,
    }
  );
});

//获取选取的文字数据
const getSelectedTextData = (): void => {
  const select = window?.getSelection();
  selectedText.content = select?.toString() ?? '';
};
//取消文字选中
const cancelTextSelected = (): void => {
  const select = window?.getSelection();
  select?.removeAllRanges();
};
//tag点击事件
const clickTag = async () => {
  tagInfo.value.visible = false;
  // ocrStore.updatedSymptomsText(selectedText.content, eventValue);
  try {
    await copy(selectedText.content || '');
    ElMessage.success('成功复制 ');
    cancelTextSelected();
  } catch {
    ElMessage.error('复制失败，请手动复制');
  }
};
</script>
<style scoped lang="less">
.main {
  height: 100%;
  box-sizing: border-box;
  .text-label {
    font-size: 14px;
    color: #111111;
    background: #fff;
    position: sticky;
    top: 0;
  }
  .main-content {
    padding-bottom: 100px;
    background-color: #f7f8fa;
    height: 100%;
    .text-content {
      font-size: 14px;
      font-weight: 400;
      color: #203549;
      line-height: 20px;
      //padding: 0 12px 100px 12px;
      padding: 16px;
      background-color: #f7f8fa;
      white-space: pre-wrap;
    }
  }
}
.tag-box {
  position: absolute;
  z-index: 9999999;
  background: #fff;
  width: 150px;
  border-radius: 2px;
  box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.9);
  box-sizing: border-box;
  padding: 6px 0;
  .tag-name {
    box-sizing: border-box;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 400;
    color: #203549;
  }
  .tag-name:hover {
    background-color: #ecf4fc;
    user-select: none;
  }
}
</style>
