<template>
  <Dialog
    v-model:visible="dialogVisible"
    :width="600"
    title="定向保存"
    @close="closeDialog"
  >
    <div v-loading="loading" class="content">
      <div class="item">
        <div class="label"><i>*</i>类型：</div>
        <div class="value">
          <el-radio-group v-model="type">
            <el-radio
              v-for="item in types"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
          <div class="help">确认要存入的记录类型。</div>
        </div>
      </div>
      <div v-if="type !== 3" class="item">
        <div class="label"><i>*</i>保存位置：</div>
        <div class="value">
          <el-radio-group v-model="position">
            <el-radio :label="1">存入已有记录</el-radio>
            <el-radio :label="2">创建新纪录</el-radio>
          </el-radio-group>
          <div class="help">
            <span>存入已有{{ curTypeName }}记录</span>
            <span>创建并保存新的自定义{{ curTypeName }}</span>
          </div>
        </div>
      </div>
      <div v-if="position === 1" class="item picker">
        <div class="label"><i>*</i>选择记录：</div>
        <div class="value">
          <el-select
            v-model="record"
            filterable
            placeholder="请选择记录"
            style="width: 240px"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="help">选择存入已有记录位置</div>
        </div>
      </div>
      <div v-else class="item picker">
        <div class="label"><i>*</i>{{ curTypeName }}日期：</div>
        <div class="value">
          <el-date-picker
            v-model="date"
            type="date"
            placeholder="请选择时间"
            size="default"
            value-format="x"
            :disabled-date="disabledDate"
            :clearable="false"
          />
          <div class="help">{{ dateHelpMap[type] }}</div>
        </div>
      </div>
      <div class="item btns">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import Dialog from '@/components/Dialog/index.vue';
import useComponentsTabAction from '@/store/module/useComponentsTabAction';
import { IAction } from '@/store/module/useComponentsTabAction';
import bus from '@/lib/bus';
import useGlobal from '@/store/module/useGlobal';
import { getDiagnosisAccessory } from '@/api/chat';
import { accessoryUpload } from '@/api/attchment';
import dayjs from 'dayjs';
import { IApiCaseHistoryDiagnosisAccessory } from '@/interface/type';
import { keyBy } from 'lodash-es';
import { getUuid } from '@/utils';
type ITabParams = IAction;
interface IProps {
  visible: boolean;
  urls: string[];
}
const props = defineProps<IProps>();
const dateHelpMap = {
  1: '选择新建门诊记录的日期。',
  0: '选择新建住院记录的入院日期。',
  2: '选择新建自定复查的日期。',
};
const types = [
  { value: 2, label: '复查' },
  { value: 3, label: '入组' },
  { value: 0, label: '住院' },
  { value: 1, label: '门诊' },
];
const typeMap = keyBy(types, 'value');
const loading = ref(false);
const options = ref<any>([]);
const tabAction = useComponentsTabAction();
const globalStore = useGlobal();
const type = ref(2);
const position = ref(2);
const record = ref('');
const date = ref('');
const dialogVisible = ref(props.visible);

const emit = defineEmits(['update:visible']);

// componentType 0住院；1门诊；2复查；3入组
const openTab = (params: ITabParams) => {
  const { componentType, name, key, data } = params;
  tabAction.setAction({
    componentType,
    name,
    key,
    mode: 'reuse',
    data,
  });
  bus.emit('open-component-tab');
};
const disabledDate = (time: { getTime: () => number }) => {
  if (type.value === 2) {
    return false;
    // if (time.getTime() < Date.now() - 86400000) return true;
  } else {
    if (time.getTime() > Date.now()) return true;
  }
  return false;
};
const closeDialog = () => {
  emit('update:visible', false);
};
const curTypeName = computed(
  () => types.find(v => v.value === type.value)?.label
);
const save = () => {
  if (position.value === 1) {
    if (!record.value) {
      ElMessage.error('请选择记录！');
      return;
    }
  }
  if (position.value === 2) {
    if (!date.value) {
      ElMessage.error('请选择时间！');
      return;
    }
  }
  const params: any = {
    sourceType: type.value,
    url: props.urls,
    patientId: globalStore.userId,
  };
  if (position.value === 1) {
    if (type.value === 3) {
      if (record.value.length > 30) {
        const item = options.value.find(v => v.value === record.value);
        params.sourceId = null;
        params.inTime = item.time;
      } else {
        params.sourceId = record.value;
      }
    } else {
      params.sourceId = record.value;
    }
  } else {
    params.inTime = date.value;
  }
  loading.value = true;
  accessoryUpload(params)
    .then(res => {
      openTabBefore(res.patientHistoryId!, params);
      closeDialog();
    })
    .finally(() => {
      loading.value = false;
    });
};
const openTabBefore = (id: number, data: any) => {
  const params: any = {
    componentType: type.value,
    data: {
      id,
      from: 'directSave',
      recordActionType: {
        actionType: 'view',
        sourceType: type,
      },
    },
  };
  const getDate = (time: number) => {
    return dayjs(time).format('YYYY-MM-DD');
  };
  const getTabName = specialName => {
    return type.value === 2 ? specialName : typeMap[type.value].label;
  };
  if (data.sourceId) {
    const curItem = options.value.find(v => v.value === data.sourceId);
    params.name = getDate(curItem.time) + ' ' + getTabName(curItem.name);
  } else {
    params.name = getDate(data.inTime) + ' ' + getTabName('自定义复查');
  }
  openTab(params);
  bus.emit('refresh-attachment', id);
  bus.emit('updata-review-list');
};
const buildOptions = (list: IApiCaseHistoryDiagnosisAccessory) => {
  const res = list.map(v => {
    return {
      ...v,
      value: type.value === 3 ? v.businessId || getUuid() : v.businessId,
      label: dayjs(v.time).format('YYYY-MM-DD') + ' ' + v.name + ' ' + v.status,
    };
  });
  return res;
};
const getOptions = async () => {
  try {
    loading.value = true;
    const params = { type: type.value, patientId: globalStore.userId! };
    const res = await getDiagnosisAccessory(params);
    options.value = buildOptions(res);
  } finally {
    loading.value = false;
  }
};
const reset = () => {
  date.value = '';
  record.value = '';
  options.value = [];
};
const resetInitData = () => {
  reset();
  type.value = 2;
  position.value = 2;
};
watch(type, val => {
  if (val === 3) {
    position.value = 1;
  } else {
    position.value = 2;
  }
  reset();
  getOptions();
});
watch(
  () => props.visible,
  val => {
    if (val) {
      resetInitData();
      getOptions();
    }
    dialogVisible.value = val;
  }
);
defineOptions({
  name: 'DirectionalSave',
});
</script>

<style scoped lang="less">
.content {
  padding: 24px;
  height: 335px;
}
.item {
  display: flex;
  margin-bottom: 24px;
  .label {
    width: 120px;
    color: #101b25;
    font-weight: bold;
    text-align: right;
    padding-top: 5px;
    margin-right: 12px;
  }
  i {
    color: #e63746;
    margin-right: 2px;
  }
}
.help {
  color: #7a8599;
  > span:first-child {
    display: inline-block;
    min-width: 140px;
  }
}
.picker .help {
  margin-top: 6px;
}
.btns {
  padding-left: 130px;
  > button {
    width: 70px;
  }
}
</style>
