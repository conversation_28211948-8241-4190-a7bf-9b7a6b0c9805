<template>
  <el-dialog
    :model-value="visible"
    modal-class="structuredSelectorDialog"
    :title="attrs.title || '请选择'"
    top="50px"
    draggable
    :append-to-body="true"
    :width="width"
    @close="onClose"
  >
    <CheckList
      ref="checkListRef"
      v-bind="attrs"
      @on-close="onClose"
      @on-change="changeHandler"
    />
    <div class="footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="confirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import CheckList from './CheckList.vue';
defineOptions({
  name: 'DialogCheckList',
  inheritAttrs: false,
});
const emit = defineEmits(['close', 'onChange']);
const attrs = useAttrs() as typeof CheckList.props;
const checkListRef = shallowRef();
const innerData = ref<any>({});
withDefaults(defineProps<{ visible: boolean; width: string }>(), {
  width: () => '800px',
});

const changeHandler = data => {
  innerData.value = data;
};
const confirm = () => {
  if (!isEmpty(innerData.value)) {
    emit('onChange', innerData.value);
  }
  emit('close');
};
const onClose = () => {
  checkListRef.value.reset();
  emit('close');
};
</script>

<style lang="less">
.structuredSelectorDialog {
  .el-dialog {
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    .el-dialog__header {
      padding-bottom: 8px;
      border-bottom: 0.5px solid #c0c4cc;
    }
    .el-dialog__title {
      font-size: 16px;
    }
    .el-dialog__body {
      text-align: left;
      padding: 0;
      height: 100% !important;
    }
  }
}
.footer {
  padding-top: 12px;
  height: 52px;
  display: flex;
  justify-content: flex-end;
}
</style>
