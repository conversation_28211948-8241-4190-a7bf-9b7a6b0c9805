<template>
  <HrtDialog
    v-model="props.chooseSurgeryVisible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :modal="false"
    width="1160"
    size="extraLarge"
    draggable
    class="operation-dialog"
    @open="openDialog"
    @close="choseDialog"
  >
    <template #header="{ titleId, titleClass }">
      <div :id="titleId" :class="titleClass">
        <div class="label">选择手术</div>
      </div>
    </template>
    <div class="content">
      <div class="main-content">
        <div class="left">
          <div class="left-content">
            <div
              v-for="(tab, index) in surgeryTypelist"
              :key="index"
              :class="{ 'tab-item': true, active: activeIndex === index }"
              @click="activeIndex = index"
            >
              {{ tab.operationName }}
            </div>
          </div>
        </div>
        <div class="right">
          <div class="right-content">
            <div
              v-if="checkOperationId(surgeryTypelist[activeIndex].operationId)"
              class="pci-box"
            >
              <div class="type-box">
                <div class="type-label">冠脉分布</div>
                <div class="single-check">
                  <div
                    v-for="pciType in pciTypes"
                    :key="pciType.value"
                    class="radio-box"
                  >
                    <el-radio
                      v-model="surgeryTypelist[activeIndex].operationType"
                      :label="pciType.value"
                    >
                      {{ pciType.name }}
                    </el-radio>
                  </div>
                </div>
                <template v-if="surgeryTypelist[activeIndex].operationType">
                  <div class="table-head">
                    <div class="col1">
                      节段选择（已选择{{ choosedSegmentNum }}条数据）
                    </div>
                    <div class="col2">狭窄程度</div>
                    <div class="col2">支架个数</div>
                    <div class="col3">球囊</div>
                    <div class="col3">旋磨</div>
                    <div class="col3">抽吸</div>
                  </div>
                  <div class="surgery-item-box">
                    <div
                      v-for="surgery in surgeryTypelist[activeIndex].children"
                      :key="surgery.segmentId"
                      :class="{
                        'surgery-item': true,
                        'surgery-item-active': surgery.check,
                      }"
                    >
                      <div class="col1">
                        <el-checkbox
                          v-model="surgery.check"
                          :true-label="1"
                          :false-label="''"
                        >
                          {{ surgery.location }}.{{ surgery.segmentName }}({{
                            surgery.alias
                          }})
                        </el-checkbox>
                      </div>
                      <div class="col2">
                        <el-select
                          v-if="surgery.check === 1"
                          v-model="surgery.degree"
                          clearable
                          filterable
                          style="width: 120px"
                          placeholder="请选择"
                          :disabled="surgery.check !== 1"
                        >
                          <el-option
                            v-for="item in degreeList"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                          />
                        </el-select>
                        <div v-else class="disabled-box">
                          <span>
                            {{
                              degreeList.find(v => v.value === surgery.degree)
                                ?.name
                            }}
                          </span>
                          <span>
                            <el-icon>
                              <i-ep-arrow-down />
                            </el-icon>
                          </span>
                        </div>
                      </div>
                      <div class="col2">
                        <el-select
                          v-if="surgery.check === 1"
                          v-model="surgery.support"
                          clearable
                          placeholder="请选择"
                          style="width: 120px"
                          :disabled="surgery.check !== 1"
                        >
                          <el-option
                            v-for="item in supportList"
                            :key="item"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <div v-else class="disabled-box">
                          <span>{{ surgery.support }}</span>
                          <span>
                            <el-icon>
                              <i-ep-arrow-down />
                            </el-icon>
                          </span>
                        </div>
                      </div>
                      <div class="col3 pl-20">
                        <el-checkbox
                          v-model="surgery.balloon"
                          :disabled="surgery.check !== 1"
                          :true-label="1"
                          :false-label="0"
                        />
                      </div>
                      <div class="col3">
                        <el-checkbox
                          v-model="surgery.turnery"
                          :disabled="surgery.check !== 1"
                          :true-label="1"
                          :false-label="0"
                        />
                      </div>
                      <div class="col3">
                        <el-checkbox
                          v-model="surgery.suction"
                          :disabled="surgery.check !== 1"
                          :true-label="1"
                          :false-label="0"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </div>
              <div
                v-if="surgeryTypelist[activeIndex].operationType"
                class="type-box"
              >
                <div class="type-label">病变选择</div>
                <div
                  v-for="diseaseChangeType in surgeryTypelist[activeIndex]
                    .lesion"
                  :key="diseaseChangeType.type"
                  class="check-group-box"
                >
                  <div class="check-label">{{ diseaseChangeType.name }}：</div>
                  <el-radio-group
                    v-if="diseaseChangeType.renderType === 'radio'"
                    v-model="diseaseChangeType.lesionId"
                  >
                    <div class="check-box">
                      <div
                        v-for="disease in diseaseChangeType.children"
                        :key="disease.value"
                        class="check-item"
                      >
                        <el-radio
                          :label="disease.value"
                          @click="
                            () =>
                              radioClickHandler(
                                diseaseChangeType,
                                'lesionId',
                                disease.value
                              )
                          "
                        >
                          {{ disease.name }}
                        </el-radio>
                      </div>
                    </div>
                  </el-radio-group>
                  <div v-else-if="diseaseChangeType.renderType === 'input'">
                    input
                  </div>
                  <el-checkbox-group
                    v-else
                    v-model="diseaseChangeType.lesionId"
                  >
                    <div class="check-box">
                      <div
                        v-for="disease in diseaseChangeType.children"
                        :key="disease.value"
                        class="check-item"
                      >
                        <el-checkbox :label="disease.value">
                          {{ disease.name }}
                        </el-checkbox>
                      </div>
                    </div>
                  </el-checkbox-group>
                </div>
              </div>
              <div
                v-if="surgeryTypelist[activeIndex].operationType"
                class="type-box"
              >
                <div class="type-label w-90">指导方式</div>
                <div class="single-check">
                  <div
                    v-for="guide in guideList"
                    :key="guide.value"
                    class="radio-box"
                  >
                    <el-radio
                      v-model="surgeryTypelist[activeIndex].guide"
                      :label="guide.value"
                    >
                      {{ guide.name }}
                    </el-radio>
                  </div>
                </div>
                <div class="type-label mt-16">TIMI指数</div>
                <div class="single-check mt-8">
                  <el-input v-model="surgeryTypelist[activeIndex].timi" />
                </div>
                <div class="type-label mt-16">FFR心率储备</div>
                <div class="single-check">
                  <el-input v-model="surgeryTypelist[activeIndex].ffr" />
                </div>
              </div>
            </div>
            <div
              v-if="
                ![1, 2, 3, 20].includes(
                  surgeryTypelist[activeIndex].operationId
                )
              "
              class="pci-box"
            >
              <div class="type-box">
                <div class="type-label">类别选择</div>
                <div class="check-group-box">
                  <div class="check-box">
                    <div
                      v-for="item in surgeryTypelist[activeIndex].children"
                      :key="item.childOperationId"
                      class="check-item"
                    >
                      <el-checkbox
                        v-model="item.check"
                        :label="item.childOperationName"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <CardiacArrhythmia
              v-show="surgeryTypelist[activeIndex]?.operationId === 20"
              ref="CardiacArrhythmiaRef"
              :data="cardiacArrhythmiaData"
            />
          </div>
          <div class="intelligentBtnBox">
            <div class="cancelBtn" @click="choseDialog">取消</div>
            <div class="sureBtn" @click="submit">确定</div>
          </div>
        </div>
      </div>
    </div>
  </HrtDialog>
</template>

<script setup lang="ts">
import { HrtDialog } from '@hrt/components';
import CardiacArrhythmia from './components/CardiacArrhythmia.vue';
import { cloneDeep } from 'lodash-es';
import { querySegmentApi } from '@/api/review';
import { SURGERY_TYPE_LIST, pciTypes } from './config';

const props = defineProps({
  chooseSurgeryVisible: {
    type: Boolean,
    default: false,
  },
  modal: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '临床事件',
  },
  choosedSurgeryInfo: {
    type: Array,
    default: () => [],
  },
});

interface surgerInfo {
  operationName: string;
  operationId: number;
  operationType: string;
  children: any;
  lesion: any;
  guide: string;
  timi?: string;
  ffr?: string;
}

let surgeryTypelist = ref<surgerInfo[]>(
  cloneDeep(SURGERY_TYPE_LIST) as surgerInfo[]
);
let activeIndex = ref(0);
let choosedSegmentNum = ref(0);
const setedActiveIndex = ref(false);

// 使用 watch 函数观察 surgeryTypelist 和 activeIndex 的变化
watch(
  () => surgeryTypelist.value,
  newVal => {
    let res = newVal[activeIndex.value]?.children?.filter(
      (item: { check: any }) => item.check
    );
    choosedSegmentNum.value = res?.length || 0;
  },
  { immediate: true, deep: true }
);

watch(
  () => activeIndex.value,
  newVal => {
    let res = surgeryTypelist.value[newVal]?.children?.filter(
      (item: { check: any }) => item.check
    );
    choosedSegmentNum.value = res?.length || 0;
  },
  { immediate: true, deep: true }
);
const radioClickHandler = (item, key, val) => {
  if (item[key] === val) {
    setTimeout(() => {
      item[key] = '';
    });
  }
};
onMounted(() => {
  queryDegreeList();
});

interface degreeInfo {
  name: string;
  value: number;
}
let degreeList = ref<degreeInfo[]>([]);
let queryDegreeList = () => {
  for (let index = 0; index < 101; index++) {
    degreeList.value.push({
      name: index + '%',
      value: index,
    });
  }
};

let supportList = ref(['0', '1', '2', '3', '大于3']);
let guideList = ref([
  { name: '无', value: 1 },
  { name: 'IVUS', value: 2 },
  { name: 'OCT', value: 3 },
]);
let cardiacArrhythmiaData = ref({});

const emit = defineEmits([
  'closeSurgeryVisible',
  'getSurgeryConclusion',
  'getParams',
]);
let choseDialog = () => {
  setedActiveIndex.value = false;
  emit('closeSurgeryVisible', false);
};

let openDialog = () => {
  surgeryTypelist.value = cloneDeep(SURGERY_TYPE_LIST) as surgerInfo[];
  cardiacArrhythmiaData.value = {};
  getSegmentList();
};

const checkOperationId = id => [1, 2, 3].includes(id);
const CardiacArrhythmiaRef = ref<any>(null);
let submit = () => {
  // 心律失常器械植入 值 === false 时 必填项未填
  const pacemaker = CardiacArrhythmiaRef.value.handleDealPacemaker();
  if (pacemaker === false) return;
  dealSurgeryConlusion(pacemaker);
  dealParamsData(pacemaker);
  choseDialog();
};

//获取截断列表
let getSegmentList = () => {
  querySegmentApi({}).then((res: { data: any[] }) => {
    console.log('$debug: res', res);
    let dealedData = res.data.map((item: any) => {
      return {
        ...item,
        degree: 0,
        support: '0',
        balloon: 0,
        check: '',
      };
    });
    surgeryTypelist.value[0].children = JSON.parse(JSON.stringify(dealedData));
    surgeryTypelist.value[1].children = JSON.parse(JSON.stringify(dealedData));
    surgeryTypelist.value[2].children = JSON.parse(JSON.stringify(dealedData));
    nextTick(() => {
      if (props.choosedSurgeryInfo.length) {
        showBackSurgeryInfo();
      }
    });
  });
};

//处理手术结论
let dealSurgeryConlusion = (pacemaker: any) => {
  let finalArr: string[] = [];
  surgeryTypelist.value.forEach(item => {
    if (checkOperationId(item.operationId)) {
      if (item.operationType) {
        let typeName =
          item.operationType === 1
            ? '右优势型冠脉'
            : item.operationType === 2
              ? '左优势型冠脉'
              : item.operationType === 3
                ? '均衡型'
                : '';

        //处理截断字符串
        let checkedArr = item.children.filter(
          (item: { check: number }) => item.check === 1
        );
        let checkArrStr: string[] = [];
        checkedArr.forEach(
          (item: {
            balloon: number;
            location: any;
            segmentName: any;
            alias: any;
            degree: string;
            support: string;
            turnery: number;
            suction: number;
          }) => {
            let balloonName = item.balloon === 1 ? '，球囊' : '';
            let turneryName = item.turnery === 1 ? '，旋磨' : '';
            let suctionName = item.suction === 1 ? '，抽吸' : '';
            let str =
              item.location +
              item.segmentName +
              item.alias +
              '，' +
              item.degree +
              '%狭窄' +
              '，' +
              '支架' +
              item.support +
              '枚' +
              balloonName +
              turneryName +
              suctionName;

            checkArrStr.push(str);
          }
        );
        //处理病变
        let diseaseChangeStr: string[] = [];
        //存在病变数据
        let diseaseChangeArr = item.lesion.filter(
          (item: { lesionId: string | any[] }) => item.lesionId.length !== 0
        );
        diseaseChangeArr.forEach(
          (diseaseChangeItem: {
            name: any;
            children: any[];
            lesionId: string | any[];
          }) => {
            let diseaseChangName = diseaseChangeItem.name;
            let arr: any[] = [];
            diseaseChangeItem.children.forEach(
              (lesion: { value: any; name: any }) => {
                if (Array.isArray(diseaseChangeItem.lesionId)) {
                  if (diseaseChangeItem.lesionId.includes(lesion.value)) {
                    arr.push(lesion.name);
                  }
                } else {
                  if (diseaseChangeItem.lesionId === lesion.value) {
                    arr.push(lesion.name);
                  }
                }
              }
            );
            let str = diseaseChangName + ': ' + arr.join('，');
            diseaseChangeStr.push(str);
          }
        );

        //处理指导方式
        let choosedGuideArr = guideList.value.filter(
          (guide: { value: any }) => guide.value === item.guide
        );
        let dealMethod = choosedGuideArr.length
          ? '指导方式: ' + choosedGuideArr[0].name
          : '';
        const timiStr = item.timi ? '，TIMI指数:' + item.timi : '';
        const ffrStr = item.ffr ? '，FFR心率储备:' + item.ffr : '';
        let finalStr =
          item.operationName +
          ': ' +
          typeName +
          (checkArrStr.length || diseaseChangeStr.length || dealMethod
            ? '，'
            : '') +
          checkArrStr.join('，') +
          (diseaseChangeStr.length && checkArrStr.length ? '，' : '') +
          diseaseChangeStr.join('，') +
          (dealMethod.length && diseaseChangeStr.length ? '，' : '') +
          dealMethod +
          timiStr +
          ffrStr;
        finalArr.push(finalStr);
      }
    } else if (item.operationId === 20) {
      if (pacemaker) finalArr.push(`${item.operationName}`);
    } else {
      let checkedArr = item.children.filter(
        (childOperation: { check: any }) => childOperation.check
      );
      if (checkedArr.length) {
        let operationName = item.operationName;
        let childNameArr: any[] = [];
        checkedArr.forEach((item: { childOperationName: any }) => {
          childNameArr.push(item.childOperationName);
        });
        let finalStr = operationName + ': ' + childNameArr.join('，');
        finalArr.push(finalStr);
      }
    }
  });
  console.log('$debug: finalArr', finalArr);
  emit('getSurgeryConclusion', finalArr);
};

//处理参数
let dealParamsData = (pacemaker: any) => {
  let surgeryInfo = JSON.parse(JSON.stringify(surgeryTypelist.value));
  let dataParams: {
    operationId: any;
    operationName: any;
    operationType?: any;
    typeName?: string;
    data: any;
    lesion?: never[];
    guide?: any;
    timi?: any;
    ffr?: any;
  }[] = [];
  surgeryInfo.forEach(
    (item: {
      operationId: number;
      operationType: number;
      operationName: any;
      guide: any;
      timi?: any;
      ffr?: any;
      children: {
        filter: (arg0: { (child: any): boolean; (item: any): any }) => never[];
      };
      lesion: any[];
    }) => {
      if (checkOperationId(item.operationId)) {
        //如果operationType存在的话代表特殊类型有值
        if (item.operationType) {
          let paramsObj = {
            //父类手术：手术id
            operationId: item.operationId,
            //父类手术名称
            operationName: item.operationName,
            //手术类型：1 右势 2 左势 3 均衡
            operationType: item.operationType,
            typeName:
              item.operationType === 1
                ? '右优势型冠脉'
                : item.operationType === 2
                  ? '左优势型冠脉'
                  : item.operationType === 3
                    ? '均衡型'
                    : '',
            data: [],
            lesion: [],
            guide: item.guide,
            timi: item.timi,
            ffr: item.ffr,
          };
          paramsObj.data = item.children.filter(
            (child: { check: number }) => child.check === 1
          );
          paramsObj.data.forEach(child => {
            delete child.check;
          });
          //处理病变数据
          let diseaseChangeArr = item.lesion.filter(
            (diseaseChange: { lesionId: string | any[] }) =>
              diseaseChange.lesionId.length !== 0
          );
          diseaseChangeArr.forEach((lesion: { type: any; lesionId: any }) => {
            let lesionObj = {
              //病变类型
              type: lesion.type,
              //病变id
              lesionId: lesion.lesionId,
            };
            paramsObj.lesion.push(lesionObj);
          });
          dataParams.push(paramsObj);
        }
      } else if (item.operationId === 20) {
        if (pacemaker) {
          dataParams.push({
            operationId: item.operationId,
            operationName: item.operationName,
            data: [pacemaker],
          });
        }
      } else {
        let checkedArr = item.children.filter(
          (item: { check: any }) => item.check
        );
        checkedArr.forEach((item: { check: any }) => {
          delete item.check;
        });
        if (checkedArr.length !== 0) {
          let paramsObj = {
            operationId: item.operationId,
            operationName: item.operationName,
            data: checkedArr,
          };
          dataParams.push(paramsObj);
        }
      }
    }
  );
  console.log('$debug: dataParams', dataParams);
  emit('getParams', dataParams);
};

//处理手术回显
let showBackSurgeryInfo = () => {
  surgeryTypelist.value.forEach((oriSurgery, index) => {
    props.choosedSurgeryInfo.forEach(
      (choosedSurgery: {
        operationId: number;
        operationType: any;
        typeName: any;
        guide: any;
        timi: any;
        ffr: any;
        data: any[];
        lesion: any[];
      }) => {
        if (choosedSurgery.operationId === oriSurgery.operationId) {
          if (!setedActiveIndex.value) {
            setedActiveIndex.value = true;
            activeIndex.value = index;
          }
        }
        if (checkOperationId(choosedSurgery.operationId)) {
          if (choosedSurgery.operationId === oriSurgery.operationId) {
            oriSurgery.operationType = choosedSurgery.operationType;
            oriSurgery.typeName = choosedSurgery.typeName;
            oriSurgery.guide = choosedSurgery.guide;
            oriSurgery.timi = choosedSurgery.timi;
            oriSurgery.ffr = choosedSurgery.ffr;
            //处理children信息
            oriSurgery.children.forEach(
              (subOriSurgery: {
                segmentId: any;
                support: any;
                degree: any;
                balloon: any;
                turnery: number;
                suction: number;
                check: number;
              }) => {
                choosedSurgery.data?.forEach(
                  (subChoosedSurgery: {
                    segmentId: any;
                    support: any;
                    degree: any;
                    balloon: any;
                    turnery: number;
                    suction: number;
                  }) => {
                    if (
                      subOriSurgery.segmentId === subChoosedSurgery.segmentId
                    ) {
                      subOriSurgery.support = subChoosedSurgery.support;
                      subOriSurgery.degree = subChoosedSurgery.degree;
                      subOriSurgery.balloon = subChoosedSurgery.balloon;
                      subOriSurgery.turnery = subChoosedSurgery.turnery;
                      subOriSurgery.suction = subChoosedSurgery.suction;
                      subOriSurgery.check = 1;
                    }
                  }
                );
              }
            );

            //处理lesion
            choosedSurgery.lesion.forEach(
              (chooseLesion: { type: any; lesionId: any }) => {
                oriSurgery.lesion.forEach(
                  (oriSurgery: { type: any; lesionId: any }) => {
                    if (chooseLesion.type === oriSurgery.type) {
                      oriSurgery.lesionId = chooseLesion.lesionId;
                      if (
                        [0, 1].includes(chooseLesion.type) &&
                        Array.isArray(chooseLesion.lesionId)
                      ) {
                        // 历史数据处理，完全闭塞和三叉病变如果是旧数据，编辑时候清空
                        oriSurgery.lesionId = '';
                      }
                    }
                  }
                );
              }
            );
          }
        } else if (choosedSurgery.operationId === 20) {
          cardiacArrhythmiaData.value = choosedSurgery.data?.[0];
        } else {
          if (choosedSurgery.operationId === oriSurgery.operationId) {
            let childOperationIds = choosedSurgery.data?.map(
              (chooseItem: { childOperationId: any }) =>
                chooseItem.childOperationId
            );
            oriSurgery.children.forEach(
              (oriItem: { childOperationId: any; check: boolean }) => {
                if (childOperationIds.includes(oriItem.childOperationId)) {
                  oriItem.check = true;
                }
              }
            );
          }
        }
      }
    );
  });
};
</script>
<style lang="less">
.operation-dialog {
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    pointer-events: auto !important;
  }
  display: flex;
  flex-direction: column;
  max-height: 70%;
  .el-dialog__body {
    overflow: auto;
  }
}
.operation-dialog .el-dialog__header {
  /* display: none; */
  margin-left: 16px;
  font-weight: 700;
  font-size: 16px;
  border-bottom: 1px solid #ccc;
}
.operation-dialog .el-dialog__body {
  padding: 16px;
}
:has(> .el-overlay-dialog .operation-dialog) {
  pointer-events: none !important;
}
</style>
<style scoped lang="less">
.el-dialog {
  margin-top: 5vh !important;
  height: 80vh;
  .el-dialog__body {
    text-align: left;
    padding: 0;
    height: 100%;
    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      .head {
        box-sizing: border-box;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e9e8eb;
        .label {
          font-size: 16px;
          font-weight: bold;
          color: #101b25;
        }
        .close-icon {
          font-size: 18px;
          font-weight: bold;
        }
      }
      .main-content {
        box-sizing: border-box;
        display: flex;
        height: 100%;
        .left {
          height: 100%;
          background-color: #fff;
          .left-content {
            width: 140px;
            background-color: #f5f6f8;
            .tab-item {
              box-sizing: border-box;
              padding: 16px 0 16px 24px;
              cursor: pointer;
            }
            .active {
              background-color: #ffffff;
              color: #0a73e4;
            }
          }
        }
        .right {
          flex: 1;
          background-color: #ffffff;
          display: flex;
          flex-direction: column;
          .right-content {
            background-color: #fff;
            flex: 1;
            box-sizing: border-box;
            padding: 24px 16px;
            overflow-y: auto;
            .pci-box {
              .type-box {
                height: 100%;
                margin-bottom: 32px;
                .type-label {
                  font-size: 16px;
                  font-weight: bold;
                  color: #101b25;
                }
                .single-check {
                  margin-top: 16px;
                  display: flex;
                  :deep(.el-input) {
                    width: 240px;
                  }
                }
                .table-head {
                  margin-top: 24px;
                  box-sizing: border-box;
                  padding: 16px;
                  background-color: #dcdee0;
                  border: 1px solid #e8eaec;
                  font-size: 14px;
                  font-weight: bold;
                  color: #203549;
                  display: flex;
                  align-items: center;
                }
                .search-box {
                  margin-top: 16px;
                  .el-input {
                    width: 256px;
                    .el-input__inner {
                      width: 256px;
                      height: 32px;
                      border-radius: 2px;
                      border: 1px solid #dcdee0;
                    }
                    .el-input__icon {
                      line-height: 32px;
                    }
                  }
                }
                .surgery-item-box {
                  margin-top: 16px;
                  height: 60%;
                  overflow-y: auto;
                  .surgery-item {
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    padding: 6px 16px;
                  }
                  .surgery-item-active {
                    background-color: #ecf4fc;
                  }
                }
                .check-group-box {
                  display: flex;
                  margin-top: 24px;
                  .check-label {
                    width: 90px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #101b25;
                  }
                  .check-box {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                    grid-gap: 16px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.disabled-box {
  cursor: not-allowed;
  width: 120px;
  height: 28px;
  display: flex;
  padding: 0 12px;
  justify-content: space-between;
  line-height: 28px;
  background-color: #f5f7fa;
  color: #a8abb2;
  box-shadow: 0 0 0 1px #e4e7ed;
  > span:first-child {
    color: #203549;
  }
}
:deep(.radio-box) {
  margin-right: 16px;
  .el-radio__label {
    font-size: 14px;
    font-weight: 400;
    color: #203549;
  }
  .el-radio__inner {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }

  .el-radio__input.is-checked {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
  }
}
.title {
  font-size: 16px;
  font-family:
    PingFangSC-Medium,
    PingFang SC;
  font-weight: 600;
  color: #101b25;
  border-bottom: 1px solid #e9e8eb;
  padding: 24px 24px 16px 24px;
}
.intelligentBtnBox {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e9e8eb;
  height: 60px;
  .cancelBtn {
    width: 76px;
    height: 32px;
    border-radius: 2px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #606266;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
  }
  .sureBtn {
    width: 76px;
    height: 32px;
    background: #0a73e4;
    border-radius: 2px;
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 16px;
  }
}
:deep(.radio-box) {
  .des-text {
    font-size: 14px;
    font-weight: 400;
    color: #708293;
  }
  .el-radio__inner {
    width: 16px;
    height: 16px;
    // 去掉默认的中心填充
    &::after {
      display: none;
      transition: none;
    }
  }

  .el-radio__input.is-checked {
    .el-radio__inner {
      padding: 2px;
      background-color: #0a73e4;
      background-clip: content-box;
    }
  }
}
:deep(.col1) {
  width: 30%;
  .el-checkbox__label {
    font-size: 14px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #203549;
  }
}
:deep(.col2) {
  width: 20%;
  .el-select {
    .el-input__inner {
      width: 120px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid #dcdee0;
    }
    .el-input__suffix {
      .el-input__icon {
        line-height: 32px;
      }
      .el-icon-arrow-up:before {
        content: '';
      }
    }
  }
}
.col3 {
  width: 13.3%;
}
</style>
