<template>
  <draggable
    ghost-class="ghost"
    chosen-class="chosenClass"
    class="dragwrapper"
    item-key="id"
    animation="300"
    v-bind="$attrs"
  >
    <template #item="{ element, index }">
      <div class="mt-8 mr-8">
        <slot name="item" :item="element" :index="index"></slot>
      </div>
    </template>
  </draggable>
</template>
<script setup lang="ts">
import draggable from 'vuedraggable';
defineOptions({
  name: 'DraggableList',
});
</script>

<style scoped lang="less">
.ghost {
  border: 1px solid #0a73e4;
}
.dragwrapper {
  display: flex;
  flex-wrap: wrap;
}
</style>
