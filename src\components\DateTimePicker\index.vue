<template>
  <div class="date-box">
    <el-date-picker
      v-bind="attrs"
      v-model="date"
      value-format="x"
      placeholder="选择日期"
      @change="chanegeHandler"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DateTimePicker',
  inheritAttrs: false,
});
const props = defineProps({
  value: [String, Number],
});
const emit = defineEmits(['change']);
const attrs = useAttrs();
const date = ref<any>('');

watchEffect(() => {
  date.value = props.value ? props.value : null;
});

const chanegeHandler = (val: any) => {
  emit('change', val);
};
</script>
