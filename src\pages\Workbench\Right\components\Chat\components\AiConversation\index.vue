<template>
  <div v-show="showAiBox" class="ai-chat-reply w-full flex items-center">
    <div class="w-24 h-24 ml-2xs cursor-pointer shrink-0">
      <img
        class="w-full h-full"
        src="@/assets/imgs/chat/chat-img.png"
        alt="chat img"
      />
    </div>

    <div class="px-2xs flex-1 overflow-hidden">
      <transition name="swiper" mode="out-in">
        <div :key="aiSwiperActive">
          <Text>{{ aiStore.activeAiConversation?.outputDialogue }}</Text>
        </div>
      </transition>
    </div>
    <div class="fill-in" @click.stop="handleRespond('CONFIRMED')">填入</div>
    <div class="pack-up mr-2xs" @click.stop="handleRespond('IGNORED')">
      忽略
    </div>
  </div>
</template>
<script setup lang="ts">
import Text from '@/components/Text/index.vue';
import useAIConversation from '@/store/module/useAIConversation';

defineOptions({
  name: 'AiConversation',
});

const emits = defineEmits(['onMessage', 'change-text']);
const aiStore = useAIConversation();
const showAiBox = ref(false);
const aiSwiperActive = ref(0);

/** 填入消息 */
const fillIn = (val: string) => {
  emits('change-text', { val, type: 'expression' });
};

/** 填入or忽略 消息 */
const handleRespond = async (confirmType: 'IGNORED' | 'CONFIRMED') => {
  const { recommendId, outputDialogue } = aiStore.activeAiConversation;
  await aiStore.handleAiRespond({ recommendId, confirmType });
  if (confirmType === 'CONFIRMED') fillIn(outputDialogue);
};

const updateAiBanner = (visible: boolean) => {
  showAiBox.value = visible;
  nextTick(() => emits('onMessage'));
};

watch(
  () => aiStore.activeAiConversation,
  data => {
    if (data.recommendId) {
      if (!showAiBox.value) return updateAiBanner(true);
      aiSwiperActive.value = Math.random();
    } else {
      updateAiBanner(false);
    }
  },
  { deep: true }
);

watch(
  () => aiStore.aiChatMsg,
  msg => {
    if (msg) {
      fillIn(msg);
      aiStore.aiChatMsg = undefined;
    }
  },
  { immediate: true }
);

onMounted(() => {
  aiStore.getLatestConversationData();
});
</script>

<style scoped lang="less">
.ai-chat-reply {
  background: #3a4762;
  font-size: 14px;
  color: #fff;
  height: 40px;
  overflow: hidden;
  .fill-in,
  .pack-up {
    cursor: pointer;
    border-radius: 2px;
    width: 40px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    flex-shrink: 0;
  }
  .fill-in {
    background: #2e6be6;
  }
  .pack-up {
    margin-left: 6px;
    color: #7a8599;
    background: #fff;
  }
}

.swiper-enter-active,
.swiper-leave-active {
  transition: all 0.5s ease;
}
.swiper-enter {
  transform: translateY(30%);
  opacity: 0;
}
.swiper-leave {
  opacity: 1;
}
.swiper-leave-to {
  transform: translateY(-30%);
  opacity: 0;
}
</style>
