<template>
  <!--  sessionStorage.setItem('userAccount', ruleForm.account);-->

  <div class="login">
    <div class="login-image-box">
      <img src="@/assets/imgs/login/login.png" alt="" class="login-image" />
      <img src="@/assets/imgs/welcome.png" alt="" style="display: none" />
      <el-link
        type="danger"
        class="record-number"
        :underline="false"
        href="https://beian.miit.gov.cn/"
        target="_blank"
      >
        {{ getRocordNumber }}
      </el-link>
    </div>
    <div class="right-box" :class="{ 'right-box-active': isForgetPassword }">
      <template v-if="!isForgetPassword">
        <div class="title">欢迎登录</div>
        <el-form
          ref="ruleFormRef"
          :key="loginType"
          :model="ruleForm"
          :rules="loginType === 1 ? rules : unionRules"
          class="demo-ruleForm"
        >
          <el-form-item prop="account">
            <div class="inputBox">
              <img
                v-if="loginType === 1"
                src="@/assets/imgs/login/account.png"
                alt=""
                class="img"
              />
              <img
                v-if="loginType === 2"
                src="@/assets/imgs/login/phone-number.png"
                alt=""
                class="img"
              />
              <el-input
                v-model="ruleForm.account"
                :placeholder="loginType === 1 ? '请输入账号' : '请输入手机号'"
                clearable
                :maxlength="loginType === 1 ? 15 : 11"
                oninput="value = value.replace(/\s+/g,'')"
              />
            </div>
          </el-form-item>
          <el-form-item v-if="loginType === 2" prop="code">
            <div class="inputBox">
              <img
                src="@/assets/imgs/login/verification-code.png"
                class="img"
              />
              <el-input
                v-model="ruleForm.code"
                placeholder="请输入验证码"
                clearable
                oninput="value = value.replace(/\s+/g,'')"
                maxlength="6"
              />
              <el-button
                class="get-code_new"
                :disabled="getCodeDisabled"
                type="text"
                @click="handle"
              >
                {{ codeBtnWord }}
              </el-button>
            </div>
          </el-form-item>
          <el-form-item prop="password">
            <div class="inputBox">
              <img src="@/assets/imgs/login/password.png" alt="" class="img" />
              <el-input type="password" class="hidden" />
              <el-input
                v-model="ruleForm.password"
                placeholder="请输入密码"
                clearable
                oninput="value = value.replace(/\s+/g,'')"
                type="password"
                maxlength="20"
                show-password
              />
            </div>
          </el-form-item>
          <div class="change-login-type">
            <el-checkbox v-if="[1, 2].includes(loginType)" v-model="checked">
              记住密码
            </el-checkbox>
            <div class="forgot-password" @click="forgetPassword">
              忘记密码？
            </div>
          </div>
          <el-form-item label-width="0">
            <el-button
              :loading="loginLoading"
              type="primary"
              class="login-button"
              @click="submitForm(ruleFormRef)"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <ForgetPassword v-if="isForgetPassword" @back-login="backLogin" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import CryptoJS from 'crypto-js';
import ForgetPassword from './components/ForgetPassword.vue';
import { getCode, getLoginType } from '@/api/login';
import useUserStore from '@/store/module/useUserStore';
const userStore = useUserStore();

// 获取备案号
const getRocordNumber = computed(() => {
  return import.meta.env.VITE_APP_RECORD_NUMBER;
});

let getCodeDisabled = ref<boolean>(false);
let codeBtnWord = ref<string>('获取验证码');
const loginLoading = ref<boolean>(false);
// 验证必填项--密码账号登录
interface RuleForm {
  account: string;
  password: string;
  code: string;
}
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  account: '',
  password: '',
  code: '',
});
getLoginType().then((res: any) => {
  const type = Number(res.dictValueId);
  loginType.value = type;
  if (type === 1) {
    getCookie();
  }
});

onUnmounted(() => {
  document.onkeydown = false;
});
const preloadMain = () => {
  import('@/pages/Workbench/index.vue')
    .then(res => {
      console.log('$debug: preloadMain res', res);
    })
    .catch(err => {
      console.log('$debug: preloadMain err', err);
    });
};
onMounted(() => {
  let sendEndTime = localStorage.getItem('startTimeLoginAssistant');
  if (sendEndTime) {
    getCodeDisabled.value = true;
    countDown();
  }
  setTimeout(() => {
    preloadMain();
  }, 100);
  document.onkeydown = e => {
    let key = null;
    if (typeof window.event === 'undefined') {
      key = e.keyCode;
    } else {
      key = window.event.keyCode;
    }
    if (key === 13) {
      submitForm(ruleFormRef.value);
    }
  };
});

// 不修改密码返回登录状态
let backLogin = () => {
  isForgetPassword.value = false;
};

// 是否忘记密码
let isForgetPassword = ref<boolean>(false);

// 是否需要记住密码
let checked = ref<boolean>(false);
// 记录登录方式 1--账号密码登录  3--密码验证码同时
let loginType = ref<number>(1);

// 验证账号密码登录
const rules = reactive<FormRules<RuleForm>>({
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});

// 验证码密码登录
const unionRules = reactive<FormRules<RuleForm>>({
  account: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
});

// 验证使用手机号验证码登录的信息是否正确
let verifyMsg = () => {
  let regex = /^1[3-9]\d{9}$/;
  if (!ruleForm.account) {
    ElMessage({
      showClose: true,
      message: '请输入手机号！',
      type: 'error',
    });
    return false;
  }
  if (!Number(ruleForm.account) || !regex.test(ruleForm.account)) {
    ElMessage({
      showClose: true,
      message: '请输入正确的手机号！',
      type: 'error',
    });
    return false;
  }
  return true;
};
const forgetPassword = () => {
  isForgetPassword.value = true;
};
// 获取验证码/忘记密码
let handle = () => {
  // 获取验证码
  if ([2].includes(loginType.value) && verifyMsg()) {
    countDown();
    getCode({ loginAccount: ruleForm.account, type: 1 }).then(res => {
      if (res.code === 'E000000') {
        ElMessage({
          message: '验证码发送成功,请注意查收！',
          type: 'success',
        });
      } else {
        ElMessage.error(res.message);
      }
    });
  }
};

// 登录
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      // 只有是验证码登录的时候才有如下验证
      if (loginType.value === 2) {
        if (!verifyMsg()) {
          return false;
        }
        // 如果是账号密码登录，需要验证手机号、验证码格式（只能为数字）是否正确
        if (!Number(ruleForm.code)) {
          ElMessage({
            showClose: true,
            message: '验证码格式错误，应为6位数字！',
            type: 'error',
          });
          return false;
        }
        if (verifyMsg()) {
          console.log('验证码登录');
        }
      }
      let params = {
        loginAccount: ruleForm.account,
        password: ruleForm.password,
        type: loginType.value,
        verifyCode: ruleForm.code,
      };
      loginLoading.value = true;
      userStore.login(params).finally(() => {
        loginLoading.value = false;
      });

      // 是否需要记住密码
      if (checked.value) {
        //传入保存天数,账号名，密码3个参数
        setCookie(7, ruleForm.account, ruleForm.password);
      } else {
        //清空Cookie
        clearCookie();
      }
    } else {
      ElMessage.error('请填写完整！');
    }
  });
};

// 获取验证码按钮失效时间
let countDownTime = ref<number>(60);
let timer = ref();
// 发送验证码倒计时逻辑
let countDown = () => {
  let startTime = localStorage.getItem('startTimeLoginAssistant');
  let nowTime = new Date().getTime();
  if (startTime) {
    let surplus = 60 - parseInt((nowTime - startTime) / 1000, 10);
    countDownTime.value = surplus <= 0 ? 0 : surplus;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
  } else {
    countDownTime.value = 60;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
    localStorage.setItem('startTimeLoginAssistant', nowTime);
  }
  timer.value = setInterval(() => {
    countDownTime.value--;
    codeBtnWord.value = `${countDownTime.value}s 后重新发送`;
    getCodeDisabled.value = true;
    if (countDownTime.value <= 0) {
      localStorage.removeItem('startTimeLoginAssistant');
      clearInterval(timer.value);
      countDownTime.value = 60;
      codeBtnWord.value = '获取验证码';
      getCodeDisabled.value = false;
    }
  }, 1000);
};

// 设置cookie
let setCookie = (exdays: number, portId?: string, psw?: string) => {
  // Encrypt，加密账号密码
  let cipherPortId = CryptoJS.AES.encrypt(
    String(portId),
    'secretkey123'
  ).toString();
  let cipherPsw = CryptoJS.AES.encrypt(String(psw), 'secretkey123').toString();

  let exdate = new Date(); //获取时间
  exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); //保存的天数
  //字符串拼接cookie，为什么这里用了==，因为加密后的字符串也有个=号，影响下面getcookie的字符串切割，你也可以使用更炫酷的符号。
  window.document.cookie =
    'currentPortId' +
    '==' +
    cipherPortId +
    ';path=/;expires=' +
    exdate.toUTCString();
  window.document.cookie =
    'password' + '==' + cipherPsw + ';path=/;expires=' + exdate.toUTCString();
};

//读取cookie
let getCookie = () => {
  if (document.cookie.length > 0) {
    let arr = document.cookie.split('; '); //这里显示的格式请根据自己的代码更改
    for (let i = 0; i < arr.length; i++) {
      let arr2 = arr[i].split('=='); //根据==切割
      let bytes = '';
      //判断查找相对应的值
      if (arr2[0] == 'currentPortId') {
        // Decrypt，将解密后的内容赋值给账号
        bytes = CryptoJS.AES.decrypt(arr2[1], 'secretkey123');

        ruleForm.account = bytes.toString(CryptoJS.enc.Utf8);
      } else if (arr2[0] == 'password') {
        // Decrypt，将解密后的内容赋值给密码
        bytes = CryptoJS.AES.decrypt(arr2[1], 'secretkey123');
        ruleForm.password = bytes.toString(CryptoJS.enc.Utf8);
      }
    }
  }
};

// 清除cookie
let clearCookie = () => {
  setCookie(-1, '', '');
};
</script>

<style scoped lang="less">
.login {
  width: 100%;
  height: 100%;
  display: flex;
  .login-image-box {
    width: 64%;
    height: 100%;
    position: relative;
    .login-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .record-number {
      position: absolute;
      right: 48px;
      bottom: 12px;
      color: #2e6be6;
    }
  }
  .right-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    background: #fff;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #203549;
    }
    :deep(.demo-ruleForm) {
      width: 72%;
      .inputBox {
        width: 100%;
        display: flex;
        border-bottom: 2px solid #e9e8eb;
        padding: 8px 0;
        align-items: center;
        .img {
          width: 22px;
          height: 22px;
          margin-left: 14px;
          margin-right: 22px;
        }
        .el-input {
          flex: 1;
        }
        .get-code {
          font-size: 16px;
          color: #2e6be6;
        }
        .get-code_new {
          font-size: 16px;
          color: #2e6be6;
          margin-right: 24px;
        }
      }

      .el-input__wrapper,
      .is-focus {
        box-shadow: none !important;
      }
      .change-login-type {
        margin-top: -10px;
        margin-bottom: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .is-checked {
          .el-checkbox__inner {
            background: #2e6be6;
            border-color: #2e6be6;
          }
          .el-checkbox__label {
            color: #203549;
          }
        }
        .code-login {
          font-size: 14px;
          color: #2e6be6;
          cursor: pointer;
        }
        .forgot-password {
          margin-right: 24px;
        }
      }

      .login-button {
        width: 100%;
        height: 61px;
        background: #2e6be6;
        border-radius: 4px;
        font-size: 24px;
        color: #ffffff;
      }
    }
  }
  .right-box-active {
    background: #f7f8fa;
  }
  .forgot-password {
    font-size: 14px;
    color: #2e6be6;
    margin-right: 24px;
    cursor: pointer;
  }
  .hidden {
    display: none;
  }
}
</style>
