<template>
  <div class="chart">
    <BaseChart type="line" :options="lineOptions" :data-complete="!loading" />
  </div>
</template>

<script setup lang="ts">
import BaseChart from '@/components/BaseChart';
import { PropType } from 'vue';
import { useChartData } from '../hooks/useChartData';

const props = defineProps({
  indicActiveTab: {
    type: String,
    default: '',
  },
  reqParam: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
});

const { lineOptions, getLineChartData, loading } = useChartData();

watch(
  [() => props.reqParam, () => props.indicActiveTab],
  data => {
    if (props.indicActiveTab === 'lineChart' && data[0].startTime) {
      getLineChartData(data[0]);
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style scoped>
.chart {
  height: 360px;
}
</style>
