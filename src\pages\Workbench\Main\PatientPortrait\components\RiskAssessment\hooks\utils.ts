import dayjs from 'dayjs';
import { difference, isArray, round, takeRight } from 'lodash-es';
import { getLineEchartsOptions } from '@/components/BaseChart/options/line';
import { LineECOption } from '@/components/BaseChart';
import { IAssessmentSubType, IAssessmentType } from './index';
import arrowUp from '@/assets/icons/arrow-up.png';
import arrowDown from '@/assets/icons/arrow-down.png';
import { formatFloat } from '@/utils';

// 开启 dataZoom 数据量
const dataZoomLimit = 50;

export const formatTime = (time?: string) => dayjs(time).format('MM-DD');

interface IRiskDescription {
  /** 分数 */
  score?: number;
  /** 其他信息 */
  data?: {
    /** 值 */
    value?: string;
    /** 分数 */
    score?: number;
  }[];
}

/** 标准化评论描述 */
export function formatDescription(data: any[], type: IAssessmentType) {
  if (!data?.length) return [];
  data = takeRight(data, 3);
  return data.map(item => {
    const { dt, riskLevel } = item;
    let detail = '';
    if ([1, 2].includes(type)) {
      const { score, data } = item as IRiskDescription;
      detail =
        data?.reduce((result, { value, score: subScore = 0 }, currentIndex) => {
          const degree = score
            ? formatFloat(round(subScore / score, 2) * 100)
            : 0;
          if (currentIndex === 0) {
            return `${value}贡献度${degree}%`;
          }
          return `${result}，${value}贡献度${degree}%`;
        }, '') || '';
    }

    if (type === 3) {
      const { ascvd, highRiskFactor, riskFactor } = item;
      detail = `ASCVD事件：${ascvd?.join('，') || '--'}；高危因素事件：${
        highRiskFactor?.join('，') || '--'
      }；危险因素事件：${riskFactor?.join('，') || '--'}`;
    }

    return {
      time: dayjs(dt).format('YYYY-MM-DD'),
      desc: `${riskLevel}患者${detail ? '，' + detail : ''}`,
    };
  });
}

export interface IEchartsLineParams {
  type: IAssessmentType;
  subType: IAssessmentSubType;
  data: any[];
}

/** 获取chart option */
export const getEchartsLineConfig = ({
  type,
  subType,
  data = [],
}: IEchartsLineParams): LineECOption => {
  if (!data?.length) return {};
  // 是否存在MarkArea
  const hasMarkArea = [1, 2, 3].includes(type);
  // 是否存在MarkLine
  const hasMarkLine = [3, 4].includes(type);

  const xAxisData = data?.map(item => formatTime(item.dt));
  return getLineEchartsOptions({
    tooltipConfig: {
      padding: 0,
      borderWidth: 0,
      enterable: true,
      appendToBody: true,
      formatter: (p: any[]) => getTooltipFormatter(p, subType),
    },
    legend: { enable: false },
    dataZoom: { enable: xAxisData.length >= dataZoomLimit },
    grid: { top: 20, right: 20, bottom: 40, left: hasMarkLine ? 42 : 28 },
    yAxis: {
      axisLabel: {
        formatter: (value: any) => (hasMarkLine || value === 0 ? '' : value),
      },
      max: type === 3 ? 5 : type === 4 ? 2 : null,
    },
    xAxisData,
    seriesConfig: [
      {
        data: formatSeriesData(data, type),
        markLineData: hasMarkLine ? getASCVDOrDeathMarkLine(type) : null,
        markArea: hasMarkArea ? getGraceOrBleedMarkArea(subType) : null,
      },
    ],
  });
};

/** 系列中的数据内容 */
function formatSeriesData(data: any[], type: IAssessmentType) {
  if (!data?.length) return [];
  return data.map((item, index) => {
    let value = 1;
    if ([1, 2].includes(type)) value = item.score;
    if (type === 3) value = item.level - 0.5;
    if (type === 4) value = 1; // 猝死风险仅存在高危风险值设置为1

    return {
      ...item,
      value,
      // 保存前一条数据方便做图表对比
      preItem: index === 0 ? null : data[index - 1],
    };
  });
}

/** 获取MarkLine数据 */
function getASCVDOrDeathMarkLine(type: IAssessmentType) {
  const riskGrade = {
    3: [
      { yAxis: 0.5, name: '低危', color: '#999', labelPosition: 'start' },
      { yAxis: 1.5, name: '中危', color: '#999', labelPosition: 'start' },
      { yAxis: 2.5, name: '高危', color: '#999', labelPosition: 'start' },
      { yAxis: 3.5, name: '极高危', color: '#999', labelPosition: 'start' },
      { yAxis: 4.5, name: '超高危', color: '#999', labelPosition: 'start' },
    ],
    4: [{ yAxis: 1, name: '高危', color: '#999', labelPosition: 'start' }],
  };
  return riskGrade[type];
}

/** 获取MarkArea */
function getGraceOrBleedMarkArea(subType: IAssessmentSubType) {
  const lowColor = '#2DA641';
  const midColor = '#FF9546';
  const higColor = '#DC0101';
  const riskLimit = {
    gra: [99, 200], // 急性ACS风险 低：<99 中：100~200 高：>200
    cha: [1, 2], // 非瓣膜性房颤脑卒中风险 低：=0 中：=1 高：=2
    syn: [20], // 复杂ACS风险 低：<20  高：≥20
    aci: [10, 14], // ACS出血风险 低危:（＜10分）中危 : (10-14分）高危: (≥15分)
    bled: [2], // 房颤抗凝出血风险 中危:（≤2） 高危: (≥3)
    ascvd: [1, 2, 3, 4], //ASCVD总体风险
  };
  let markAreaData: any[] = [];
  const areaCoord = riskLimit[subType];
  if (['gra', 'cha', 'aci', 'ascvd'].includes(subType)) {
    markAreaData = [
      [
        {
          name: '低危',
          yAxis: 0,
          itemStyle: { color: lowColor },
          label: { color: lowColor },
        },
        { yAxis: areaCoord[0] },
      ],
      [
        {
          name: '中危',
          yAxis: areaCoord[0],
          itemStyle: { color: midColor },
          label: { color: midColor },
        },
        { yAxis: areaCoord[1] },
      ],
      [
        {
          name: '高危',
          yAxis: areaCoord[1],
          itemStyle: { color: higColor },
          label: { color: higColor },
        },
        {},
      ],
    ];
    if (subType === 'ascvd') {
      markAreaData.splice(
        2,
        1,
        [
          {
            name: '高危',
            yAxis: areaCoord[1],
            itemStyle: { color: higColor },
            label: { color: higColor },
          },
          { yAxis: areaCoord[2] },
        ],
        [
          {
            name: '极高危',
            yAxis: areaCoord[2],
            itemStyle: { color: higColor, opacity: '0.16' },
            label: { color: higColor },
          },
          { yAxis: areaCoord[3] },
        ],
        [
          {
            name: '超高危',
            yAxis: areaCoord[3],
            itemStyle: { color: higColor, opacity: '0.26' },
            label: { color: higColor },
          },
          {},
        ]
      );
    }
  }

  if (['syn', 'bled'].includes(subType)) {
    markAreaData = [
      [
        {
          name: '高危',
          yAxis: areaCoord[0],
          itemStyle: { color: higColor },
          label: { color: higColor },
        },
        {},
      ],
    ];
    if (subType === 'syn') {
      markAreaData.unshift([
        {
          name: '低危',
          yAxis: 0,
          itemStyle: { color: lowColor },
          label: { color: lowColor },
        },
        { yAxis: areaCoord[0] },
      ]);
    } else {
      markAreaData.unshift([
        {
          name: '中危',
          yAxis: 0,
          itemStyle: { color: midColor },
          label: { color: midColor },
        },
        { yAxis: areaCoord[0] },
      ]);
    }
  }

  return {
    label: {
      fontFamily: 'PingFang SC',
      fontWeight: 'bolder',
      fontStyle: 'italic',
      position: 'inside',
      opacity: '0.16',
      fontSize: 20,
    },
    itemStyle: { opacity: '0.06' },
    emphasis: { disabled: true },
    data: markAreaData,
  };
}

/** 自定义 tooltip Dom */
function getTooltipFormatter(params: any[], type: IAssessmentSubType) {
  const { data } = params[0];
  // 组装对比内容数据
  const dataArr = [data];
  // 前一条数据
  const preItem = data?.preItem;
  if (preItem) dataArr.push(preItem);
  // 是否有前一条对比数据
  const noPreItem = dataArr.length === 1;
  // 字符串拼接组装标识
  const jointFlag = '__DOM__';

  const domObj = {
    gra: () => {
      const dangerousFactor = data?.dangerousFactor;
      const {
        highPressure: PhighPressure,
        heartRate: PheartRate,
        creatinine: Pcreatinine,
        dangerousFactor: PdangerousFactor,
      } = preItem || {};
      // eslint-disable-next-line prefer-const
      let { currentItemDom, preItemDom } = getDifferenceByPreItem(
        dangerousFactor,
        PdangerousFactor
      );

      return dataArr.map((item, index) => {
        const { killip, highPressure, heartRate, creatinine } = item;
        const isPreItem = index === 1;
        const hiddenArrow = noPreItem || isPreItem;
        if (isPreItem) currentItemDom = preItemDom;

        return `
          <div class="content-item-sub common-size">
            <div class="title-text">KILLP分级</div>
            <div>${killip}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              收缩压<span class="sub-text">（mmHg）</span>
            </div>
            <div class="flex items-center">
              ${highPressure || '--'}
              <img
                 src="${
                   Number(highPressure) > Number(PhighPressure)
                     ? arrowUp
                     : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   highPressure === PhighPressure ||
                   hiddenArrow ||
                   !highPressure
                     ? 'hidden'
                     : ''
                 }"
                />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              心率<span class="sub-text">（次/min）</span>
            </div>
            <div class="flex items-center">
              ${heartRate || '--'}
              <img
                 src="${
                   Number(heartRate) > Number(PheartRate) ? arrowUp : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   heartRate === PheartRate || hiddenArrow || !heartRate
                     ? 'hidden'
                     : ''
                 }"
                />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              肌酐<span class="sub-text">（umol/L）</span>
            </div>
            <div class="flex items-center">
              ${creatinine || '--'}
              <img
                 src="${
                   Number(creatinine) > Number(Pcreatinine)
                     ? arrowUp
                     : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   creatinine === Pcreatinine || hiddenArrow || !creatinine
                     ? 'hidden'
                     : ''
                 }"
                />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">危险因素</div>
            <div class="content-item-danger">${currentItemDom}</div>
          </div>
          `;
      });
    },
    cha: () => {
      const { clinicalDiagnosis, previousHistory } = data || {};
      const {
        clinicalDiagnosis: PclinicalDiagnosis,
        previousHistory: PpreviousHistory,
      } = preItem || {};
      let {
        currentItemDom: currentClinicalDiagnosis,
        // eslint-disable-next-line prefer-const
        preItemDom: preClinicalDiagnosis,
      } = getDifferenceByPreItem(clinicalDiagnosis, PclinicalDiagnosis, '');
      let {
        currentItemDom: currentPreviousHistory,
        // eslint-disable-next-line prefer-const
        preItemDom: prePreviousHistory,
      } = getDifferenceByPreItem(previousHistory, PpreviousHistory);

      return dataArr.map((item, index) => {
        const isPreItem = index === 1;
        if (isPreItem) {
          currentClinicalDiagnosis = preClinicalDiagnosis;
          currentPreviousHistory = prePreviousHistory;
        }
        return `
          <div class="content-item-sub common-size">
            <div class="title-text">临床诊断</div>
            <div class="content-item-diagnose">${currentClinicalDiagnosis}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">既往史</div>
            <div class="content-item-danger">${currentPreviousHistory}</div>
          </div>
        `;
      });
    },
    syn: () => {
      const { clinicalDiagnosis, segments } = data || {};
      const {
        clinicalDiagnosis: pClinicalDiagnosis,
        segments: pSegments,
        crcl: Pcrcl,
        lvef: Plvef,
      } = preItem || {};
      let {
        currentItemDom: currentClinicalDiagnosis,
        // eslint-disable-next-line prefer-const
        preItemDom: preClinicalDiagnosis,
      } = getDifferenceByPreItem(clinicalDiagnosis, pClinicalDiagnosis, '');
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentSegments, preItemDom: preSegments } =
        getDifferenceByPreItem(segments, pSegments);

      return dataArr.map((item, index) => {
        const { crcl, lvef } = item;
        const isPreItem = index === 1;
        const hiddenArrow = noPreItem || isPreItem;
        if (isPreItem) {
          currentClinicalDiagnosis = preClinicalDiagnosis;
          currentSegments = preSegments;
        }
        return `
          <div class="content-item-sub common-size">
            <div class="title-text">节段/狭窄</div>
            <div class="content-item-danger">
              ${currentSegments}
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">Cr<span class="sub-text">（umol/L）</span></div>
            <div class="flex items-center">
              ${crcl || '--'}
              <img
                 src="${Number(crcl) > Number(Pcrcl) ? arrowUp : arrowDown}"
                 alt="flag"
                 class="content-item-img ${
                   crcl === Pcrcl || hiddenArrow || !crcl ? 'hidden' : ''
                 }"
                />
              </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">LVEF<span class="sub-text">（%）</span></div>
            <div class="flex items-center">
              ${lvef || '--'}
              <img
               src="${Number(lvef) > Number(Plvef) ? arrowUp : arrowDown}"
               alt="flag"
               class="content-item-img ${
                 lvef === Plvef || hiddenArrow || !lvef ? 'hidden' : ''
               }"
              />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">临床诊断</div>
            <div class="content-item-diagnose">${currentClinicalDiagnosis}</div>
          </div>
        `;
      });
    },
    aci: () => {
      const { medicate, clinicalDiagnosis } = data || {};
      const {
        leukocyte: Pleukocyte,
        creatinine: Pcreatinine,
        clinicalDiagnosis: PclinicalDiagnosis,
        medicate: Pmedicate,
        hemoglobin: Phemoglobin,
      } = preItem || {};
      let {
        currentItemDom: currentClinicalDiagnosis,
        // eslint-disable-next-line prefer-const
        preItemDom: preClinicalDiagnosis,
      } = getDifferenceByPreItem(clinicalDiagnosis, PclinicalDiagnosis, '');
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentMedicate, preItemDom: preMedicate } =
        getDifferenceByPreItem(medicate, Pmedicate);

      return dataArr.map((item, index) => {
        const { leukocyte, creatinine, hemoglobin } = item;
        const isPreItem = index === 1;
        const hiddenArrow = noPreItem || isPreItem;
        if (isPreItem) {
          currentClinicalDiagnosis = preClinicalDiagnosis;
          currentMedicate = preMedicate;
        }

        return `
          <div class="content-item-sub common-size">
            <div class="title-text">
              白细胞计数<span class="sub-text">（*10⁹/L）</span>
            </div>
            <div class="flex items-center">
              ${leukocyte || '--'}
              <img
                 src="${
                   Number(leukocyte) > Number(Pleukocyte) ? arrowUp : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   leukocyte === Pleukocyte || hiddenArrow ? 'hidden' : ''
                 }"
              />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              肌酐<span class="sub-text">（umol/L）</span>
            </div>
            <div class="flex items-center">
              ${creatinine || '--'}
              <img
                 src="${
                   Number(creatinine) > Number(Pcreatinine)
                     ? arrowUp
                     : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   creatinine === Pcreatinine || hiddenArrow || !creatinine
                     ? 'hidden'
                     : ''
                 }"
               />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">血红蛋白<span class="sub-text">（g/L）</span></div>
            <div class="flex items-center">
              ${hemoglobin || '--'}
              <img
                 src="${
                   Number(hemoglobin) > Number(Phemoglobin)
                     ? arrowUp
                     : arrowDown
                 }"
                 alt="flag"
                 class="content-item-img ${
                   hemoglobin === Phemoglobin || hiddenArrow || !hemoglobin
                     ? 'hidden'
                     : ''
                 }"
                />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">临床诊断</div>
            <div class="content-item-diagnose">${currentClinicalDiagnosis}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">抗血栓形成药物</div>
            <div class="content-item-danger">${currentMedicate}</div>
          </div>
          `;
      });
    },
    bled: () => {
      const { medicate, previousHistory, clinicalDiagnosis } = data || {};
      const {
        inr: Pinr,
        tipple: Ptipple,
        previousHistory: PpreviousHistory,
        clinicalDiagnosis: PclinicalDiagnosis,
        medicate: Pmedicate,
      } = preItem || {};
      let {
        currentItemDom: currentPreviousHistory,
        // eslint-disable-next-line prefer-const
        preItemDom: prePreviousHistory,
      } = getDifferenceByPreItem(previousHistory, PpreviousHistory);
      let {
        currentItemDom: currentClinicalDiagnosis,
        // eslint-disable-next-line prefer-const
        preItemDom: preClinicalDiagnosis,
      } = getDifferenceByPreItem(clinicalDiagnosis, PclinicalDiagnosis, '');
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentMedicate, preItemDom: preMedicate } =
        getDifferenceByPreItem(medicate, Pmedicate, '');

      return dataArr.map((item, index) => {
        const { inr, tipple } = item;
        const isPreItem = index === 1;
        const hiddenArrow = noPreItem || isPreItem;
        if (isPreItem) {
          currentPreviousHistory = prePreviousHistory;
          currentClinicalDiagnosis = preClinicalDiagnosis;
          currentMedicate = preMedicate;
        }

        return `
          <div class="content-item-sub common-size">
            <div class="title-text">INR</div>
            <div class="flex items-center">
              ${inr || '--'}
              <img
               src="${Number(inr) > Number(Pinr) ? arrowUp : arrowDown}"
               alt="flag"
               class="content-item-img ${
                 inr === Pinr || hiddenArrow || !inr ? 'hidden' : ''
               }" />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">饮酒过量<span class="sub-text">（ml）</span></div>
            <div class="flex items-center">
              ${tipple || '--'}
              <img
               src="${Number(tipple) > Number(Ptipple) ? arrowUp : arrowDown}"
               alt="flag"
               class="content-item-img ${
                 tipple === Ptipple || hiddenArrow || !tipple ? 'hidden' : ''
               }" />
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">既往史</div>
            <div class="content-item-danger">${currentPreviousHistory}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">临床诊断</div>
            <div class="content-item-diagnose">${currentClinicalDiagnosis}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">出血药物服用</div>
            <div class="content-item-danger">${currentMedicate}</div>
          </div>
          `;
      });
    },
    ascvd: () => {
      const { ascvd, highRiskFactor, riskFactor } = data || {};
      const {
        ascvd: pAscvd,
        highRiskFactor: pHighRiskFactor,
        riskFactor: pRiskFactor,
      } = preItem || {};
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentAscvd, preItemDom: preAscvd } =
        getDifferenceByPreItem(ascvd, pAscvd, '');
      let {
        currentItemDom: currentHighRiskFactor,
        // eslint-disable-next-line prefer-const
        preItemDom: preHighRiskFactor,
      } = getDifferenceByPreItem(highRiskFactor, pHighRiskFactor, '');
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentRiskFactor, preItemDom: preRiskFactor } =
        getDifferenceByPreItem(riskFactor, pRiskFactor, '');

      return dataArr.map((item, index) => {
        const { ascvd, highRiskFactor, riskFactor } = item;
        const aLen = ascvd?.length || 0;
        const hLen = highRiskFactor?.length || 0;
        const rLen = riskFactor?.length || 0;
        const isPreItem = index === 1;
        if (isPreItem) {
          currentAscvd = preAscvd;
          currentHighRiskFactor = preHighRiskFactor;
          currentRiskFactor = preRiskFactor;
        }
        return `
          <div class="content-item-sub common-size">
            <div class="title-text">
              <span class="title-text-point"></span>
              ASCVD事件
              <span class="title-text-count">（${aLen} 个）</span>
            </div>
            <div class="content-item-vertical">
              <div>${currentAscvd}</div>
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              <span class="title-text-point"></span>
              高危因素
              <span class="title-text-count">（${hLen}个）</span>
            </div>
            <div class="content-item-vertical">
              <div>${currentHighRiskFactor}</div>
            </div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">
              <span class="title-text-point"></span>
              危险因素
              <span class="title-text-count">（${rLen}个）</span>
            </div>
            <div class="content-item-vertical">
              <div>${currentRiskFactor}</div>
            </div>
          </div>
        `;
      });
    },
    death: () => {
      const { clinicalDiagnosis, ecg } = data || {};
      const { clinicalDiagnosis: PclinicalDiagnosis, ecg: Pecg } =
        preItem || {};
      let {
        currentItemDom: currentClinicalDiagnosis,
        // eslint-disable-next-line prefer-const
        preItemDom: preClinicalDiagnosis,
      } = getDifferenceByPreItem(clinicalDiagnosis, PclinicalDiagnosis, '');
      // eslint-disable-next-line prefer-const
      let { currentItemDom: currentEcg, preItemDom: preEcg } =
        getDifferenceByPreItem(ecg, Pecg);

      return dataArr.map((item, index) => {
        const isPreItem = index === 1;
        if (isPreItem) {
          currentClinicalDiagnosis = preClinicalDiagnosis;
          currentEcg = preEcg;
        }
        return `
          <div class="content-item-sub common-size">
            <div class="title-text">临床诊断</div>
            <div class="content-item-diagnose">${currentClinicalDiagnosis}</div>
          </div>
          ${jointFlag}
          <div class="content-item-sub common-size">
            <div class="title-text">心电图</div>
            <div class="content-item-danger">${currentEcg}</div>
          </div>
        `;
      });
    },
  };

  return domObj[type] && getAssembleItem(domObj[type](), jointFlag);
}

/** 自定义 tooltip 返回本次新增上次结果没有的数据及上次存在的数据 */
function getDifferenceByPreItem(curItem, preItem, joinFlag = '；') {
  const curArr = isArray(curItem)
    ? curItem
    : (curItem && curItem.split(',')) || [];
  const preArr = isArray(preItem)
    ? preItem
    : (preItem && preItem.split(',')) || [];

  const newItems = difference(curArr, preArr);
  const originItems = difference(curArr, newItems);

  // 拼接本次结果危险因素dom
  const currentItemDom =
    originItems?.map(item => `<span>${item}${joinFlag}</span>`).join('') +
      (newItems?.length && originItems?.length ? joinFlag : '') +
      newItems
        ?.map(item => `<span class="danger">${item}${joinFlag}</span>`)
        .join('') || '--';

  // 格式化上次结果
  const preItemDom =
    preArr
      .map(item => `<span class="normal">${item}${joinFlag}</span>`)
      .join('') || '--';

  return {
    currentItemDom,
    preItemDom,
    rowCurrentItems: newItems,
    rowPreItems: originItems,
  };
}

/** 自定义 tooltip 组装content-item dom */
function getAssembleItem(subItems, jointFlag) {
  if (!subItems) return '';
  let [currentSubItems, preSubItems] = subItems;
  currentSubItems = currentSubItems?.split(jointFlag);
  preSubItems = preSubItems?.split(jointFlag);

  const dom = currentSubItems
    ?.map((subItem, index) => {
      const preSubItem = (preSubItems && preSubItems[index]) || '';
      return `
      <div class="content-item ${preSubItem ? '' : 'content-item-only'}">
        ${subItem}${preSubItem}
      </div>
    `;
    })
    .join('');

  return `
  <div class="assessment-prognosis-tooltip">
    <div class="header">
      <div class="common-size title-text">本次结果</div>
      <div
        class="common-size title-text ${!preSubItems ? 'hidden' : ''}">
        上次结果
      </div>
    </div>
    <div class="content">${dom}</div>
  <div>
  `;
}
