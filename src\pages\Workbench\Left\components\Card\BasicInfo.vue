<template>
  <div class="flex text-[14px]">
    <div class="basic flex flex-1 text-[#7A8599] text-[14px] items-center">
      <Text
        :custom-text="renderName"
        custom-style="max-width: 76px;font-weight:bold; color: #3A4762;padding-right:12px"
      />
      <span v-if="showSexAge">{{
        data?.patientGender === 1 ? '男' : '女'
      }}</span>
      <span v-if="showSexAge">{{ data?.patientAge ?? '-' }}岁</span>
      <Text
        v-else
        :custom-text="`患者: ${data?.patientName ?? '-'}`"
        :custom-style="subTitleStyle"
      />
      <div
        v-if="
          (globalStore.currentRole === 1 || globalStore.currentRole === 2) &&
          ((data.heartFailureLevel && data.heartFailureLevel != 'NO_RISK') ||
            data.isVulnerablePhase)
        "
        class="tagName"
        :class="{
          'tagName-two': data.heartFailureLevel === 'LOW_RISK',
          'tagName-three': data.heartFailureLevel === 'MEDIUM_RISK',
          'tagName-four': data.heartFailureLevel === 'HIGH_RISK',
          'tagName-one easy':
            data.heartFailureLevel === 'NO_RISK' || !data.isVulnerablePhase,
        }"
      >
        {{
          data.heartFailureLevel === 'LOW_RISK'
            ? '低'
            : data.heartFailureLevel === 'MEDIUM_RISK'
              ? '中'
              : data.heartFailureLevel === 'HIGH_RISK'
                ? '高'
                : '易'
        }}<span
          v-if="data.heartFailureLevel !== 'NO_RISK' && data.isVulnerablePhase"
          >(易)</span
        >
      </div>
      <slot name="extra"></slot>
    </div>
    <slot>
      <div v-if="data.totalTodoNum" class="flex h-20 items-center">
        <img class="w-12 h-13 mr-4" :src="Todo" />
        <div>
          <span :style="{ color: '#2E6BE6' }">{{
            data.currentTodoNum ?? 0
          }}</span>
          <span
            :style="{
              color: data.currentTodoNum! < data.totalTodoNum ? '' : '#2E6BE6',
            }"
            >/{{ data.totalTodoNum }}</span
          >
        </div>
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { getHilightStr } from '../../utils';
import Text from '@/components/Text/index.vue';
import Todo from '@/assets/imgs/userList/todo.png';
import { ICard } from '@/store/module/useUserList';
import useGlobal from '@/store/module/useGlobal';
const globalStore = useGlobal();

interface IProps {
  data: ICard;
  highlightKey?: string;
  type?: 'info' | 'chat' | 'tag' | 'phone';
  size?: 'large';
}
const props = defineProps<IProps>();

defineOptions({
  name: 'BasicInfo',
});
const subTitleStyle = computed(() => {
  let width = 82;
  if (props.size === 'large') {
    width = 120;
  } else if ((props.data.totalPatientChats ?? 0) > 1) {
    width = 120;
  }
  return `max-width:${width}px`;
});
const renderName = computed(() => {
  const { highlightKey, data } = props;
  const { teamType, doctorName } = data;
  if (teamType === 1) return doctorName;
  if (teamType === 4) return '哈瑞特团队';
  return getHilightStr(data?.patientName, highlightKey);
});

const showSexAge = computed(() => {
  const { type, data } = props;
  if (type !== 'chat') return true;
  const { teamType } = data;
  if ([1, 4].includes(teamType)) return false;
  return true;
});
</script>

<style scoped lang="less">
.basic {
  height: 20px;
  > span {
    padding: 0 12px;
    position: relative;
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      padding-right: 2px;
    }
    &:not(:first-child):after {
      position: absolute;
      content: '/';
      font-size: 0;
      left: 0;
      top: 4px;
      width: 1px;
      height: 12px;
      background: #bac8d4;
    }
  }
  .tagName {
    width: 48px;
    height: 22px;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
  }
  .easy {
    width: 22px;
    height: 22px;
  }
  .tagName-one {
    background: #7a8599;
  }
  .tagName-two {
    background: #2fb324;
  }
  .tagName-three {
    background: #e88b48;
  }
  .tagName-four {
    background: #e63746;
  }
  .renderName {
    display: inline-block;
    max-width: 82px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
