<template>
  <div class="statistics">
    <CardWrapper class="mt-2xs" title="​服务统计">
      <!-- 统计所有类型 -->
      <div class="all-type">
        <div
          v-for="(item, index) in allStatisticsList"
          :key="index"
          class="item-type flex mb-8"
        >
          <div class="left-type flex items-center">
            <img :src="item.img" alt="" class="w-16 h-16" />
            <span class="item-title ml-12">{{ item.title }}</span>
          </div>
          <div class="right-type">
            {{ item.num }}
            <span v-if="item.time">/{{ item.time }}</span>
          </div>
        </div>
      </div>
      <!-- 血压测量统计 -->
      <div class="blood-box mt-16">
        <div class="title">
          ​血压测量统计/次数：
          <span class="num">{{ getStatisticsNum(bloodInfo.yData[0]) }}次</span>
        </div>
        <Echarts :index-info="bloodInfo" />
      </div>
      <!-- 问诊时间分布统计 -->
      <div class="blood-box mt-16">
        <div class="title">
          ​问诊时间段分布统计：
          <span class="num">{{ interrogationTotal }}次</span>
        </div>
        <PieChart :data="interrogationData" />
      </div>
      <!-- ​按月问诊次数统计 -->
      <div class="blood-box mt-16">
        <div class="title">
          ​按月问诊次数统计：
          <span class="num">
            {{ getStatisticsNum(interrogationMonthInfo.yData[0]) }}次
          </span>
        </div>
        <Echarts :index-info="interrogationMonthInfo" />
      </div>
    </CardWrapper>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import Echarts from '../StructuredReport/AddReport/components/Echarts.vue';
import PieChart from './components/PieChart.vue';
import useGlobal from '@/store/module/useGlobal';
let useGlobalInfo = useGlobal();
import {
  queryPatientInfoApi,
  queryPressureNumApi,
  queryPressureTimeNumApi,
  queryConsultNumApi,
} from '@/api/managementSituation';
import { getStatisticsNum } from '../hooks';
import store from '@/store';
const tabs = store.useTabs();
import drugImg from '../img/drug-img.png';
import followImg from '../img/follow-img.png';
import reviewImg from '../img/review-img.png';

onMounted(() => {
  getAllStatisticsList();
  getBloodList();
  getInterrogationList();
  getInterrogationMonthList();
});

watch(
  () => useGlobalInfo.userId,
  () => {
    getBloodList();
    getAllStatisticsList();
    getInterrogationList();
    getInterrogationMonthList();
  },
  {
    deep: true,
  }
);

watch(
  () => tabs.mainActiveTab,
  () => {
    getBloodList();
    getAllStatisticsList();
    getInterrogationList();
    getInterrogationMonthList();
  },
  {
    deep: true,
  }
);

// 统计的所有类型
let allStatisticsList = ref([
  // {
  //   title: '电话宣教次数及平均用时：',
  //   num: '20次',
  //   time: '56分16秒',
  //   img: '/phone-img.png',
  // },
  {
    title: '用药调整次数：',
    num: '--',
    time: '',
    img: drugImg,
  },
  {
    title: '随访次数及平均用时：',
    num: '--',
    time: '--',
    img: followImg,
  },
  {
    title: '复查次数及平均用时：',
    num: '--',
    time: '--',
    img: reviewImg,
  },
]);
let getAllStatisticsList = () => {
  queryPatientInfoApi({ patientId: useGlobalInfo.userId }).then((res: any) => {
    if (res.data) {
      let { drugAdjust, followUpNum, followUpAvg, reviewNum, reviewAvg } =
        res.data;
      allStatisticsList.value = [
        {
          title: '用药调整次数：',
          num: drugAdjust,
          time: '',
          img: drugImg,
        },
        {
          title: '随访次数及平均用时：',
          num: followUpNum,
          time: followUpAvg,
          img: followImg,
        },
        {
          title: '复查次数及平均用时：',
          num: reviewNum,
          time: reviewAvg,
          img: reviewImg,
        },
      ];
    }
  });
};

// 血压测量统计
let bloodInfo = ref({
  unit: '次',
  xData: [],
  yData: [[]],
});
let getBloodList = () => {
  queryPressureNumApi({ patientId: useGlobalInfo.userId }).then((res: any) => {
    if (res.data) {
      bloodInfo.value.xData = res.data.map(
        (item: { month: string }) => item.month + '月'
      );
      bloodInfo.value.yData = [
        res.data.map((item: { num: number }) => item.num || 0),
      ];
    }
  });
};

// 问诊时间统计分布
let interrogationData = ref([]);
let interrogationTotal = ref(0);
let getInterrogationList = () => {
  queryPressureTimeNumApi({ patientId: useGlobalInfo.userId }).then(
    (res: any) => {
      if (res.data && res.data.length) {
        interrogationTotal.value = res.data[0].totalNum;
        interrogationData.value = res.data.map(
          (item: { rate: string; time: any }) => {
            return {
              value: parseFloat(item.rate.replace('%', '')),
              name: item.time,
            };
          }
        );
      }
    }
  );
};

// ​按月问诊次数统计
let interrogationMonthInfo = ref({
  unit: '次',
  xData: [],
  yData: [[]],
});
let getInterrogationMonthList = () => {
  queryConsultNumApi({ patientId: useGlobalInfo.userId }).then((res: any) => {
    if (res.data) {
      interrogationMonthInfo.value.xData = res.data.map(
        (item: { month: string }) => {
          return item.month + '月';
        }
      );
      interrogationMonthInfo.value.yData = [
        res.data.map((item: { num: number }) => item.num || 0),
      ];
    }
  });
};
</script>
<style scoped lang="less">
.all-type {
  width: 100%;
  .item-type {
    width: 100%;
    background: #f7f8fa;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #101b25;
    .left-type {
      width: 24%;
    }
    .right-type {
      font-weight: bold;
    }
  }
}
.blood-box {
  background: #f7f8fa;
  border-radius: 4px;
  padding: 12px;
  .title {
    font-weight: bold;
    font-size: 14px;
    color: #101b25;
    .num {
      color: #2e6be6;
    }
  }
}
</style>
