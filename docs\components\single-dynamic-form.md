# SingleDynamicForm 单项动态表单

获取动态表单中的子表单，

## 基础用法

[//]: # '<demo vue="form-item/single-dynamic-form/base.vue" scope="vue"></demo>'

## API

### Props

| 参数        | 说明                     | 类型                     | 默认值   |
| ----------- | ------------------------ | ------------------------ | -------- |
| v-model     | 表单的值                 | `Record<string, any>`    | -        |
| excludeKeys | 提交时需要排除的键       | `string[]`               | `[]`     |
| category    | 子表单的key              | `string`                 | -        |
| mode        | 表单模式                 | `view \| edit \| create` | `create` |
| extraData   | 提交表单时额外携带的参数 | `Record<string, any>`    | `{}`     |

### Expose

| 方法名   | 说明                               |
| -------- | ---------------------------------- |
| `submit` | 调用组件内部封装的方法提交表单数据 |
