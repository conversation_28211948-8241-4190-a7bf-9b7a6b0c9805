<template>
  <UploadImages
    :img-list="value ?? []"
    :is-view="mode === 'view'"
    @on-delete="deleteHandler"
    @update:img-list="updateHandler"
  />
</template>

<script setup lang="ts">
import UploadImages from '@/components/UploadImages/index.vue';
import { Base } from './type';
defineProps<Base>();
const emit = defineEmits(['delete', 'change']);

const deleteHandler = val => {
  emit('delete', val);
};

const updateHandler = val => {
  emit('change', val);
};
defineOptions({
  name: 'File',
});
</script>

<style scoped lang="less">
// todo
</style>
