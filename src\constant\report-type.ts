/** 报告类型 */
export const ReportType = {
  /** 生化 */
  BIOCHEMICAL: 1,
  /** 临检 */
  CLINICAL: 2,
  /** 免疫 */
  IMMUNE: 3,
  /** 心电 */
  ECG: 4,
  /** 超声 */
  ULTRASOUND: 5,
  /** 造影 */
  ANGIOGRAPHY: 6,
  /** 放射 */
  RADIOLOGY: 7,
  /** MRI */
  MRI: 8,
  /** 内镜 */
  ENDOSCOPY: 9,
  /** 其他 */
  OTHER: 10,
  /** 自定义检查 */
  CUSTOM: 11,
} as const;

/** 报告类型描述映射 */
export const ReportTypeLabel: Record<
  (typeof ReportType)[keyof typeof ReportType],
  string
> = {
  [ReportType.BIOCHEMICAL]: '生化',
  [ReportType.CLINICAL]: '临检',
  [ReportType.IMMUNE]: '免疫',
  [ReportType.ECG]: '心电',
  [ReportType.ULTRASOUND]: '超声',
  [ReportType.ANGIOGRAPHY]: '造影',
  [ReportType.RADIOLOGY]: '放射',
  [ReportType.MRI]: 'MRI',
  [ReportType.ENDOSCOPY]: '内镜',
  [ReportType.OTHER]: '其他',
  [ReportType.CUSTOM]: '自定义检查',
};

/** 报告类型值类型 */
export type ReportTypeValue = (typeof ReportType)[keyof typeof ReportType];

/** 获取报告类型描述 */
export const getReportTypeLabel = (type: ReportTypeValue): string => {
  return ReportTypeLabel[type] || '未知类型';
};

/** 报告类型选项 */
export const REPORT_TYPE_OPTIONS = Object.entries(ReportTypeLabel).map(
  ([value, label]) => ({
    value: Number(value),
    label,
  })
);
