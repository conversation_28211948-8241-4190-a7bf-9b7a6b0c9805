import { keyBy, throttle } from 'lodash-es';
import { defineStore } from 'pinia';
import {
  initNimSdk,
  getHistoryMsgHandler,
  getMoreHistoryMsgHandler,
  sendTextMsg,
  sendBlobMsg,
  sendFileMsg,
  sendCustomMsg,
  MSG_LIMIT,
  RECORD_LIMIT,
  layzyFn,
  findTargetMsgs,
  getTteammsgReadAccounts,
} from '@/lib/nim/nim';
import { MSGT_TYPES } from '@/constant/chat';
import { ElMessage } from 'element-plus';
import { processMsg, buildSessionMsg } from '@/lib/nim/nim_util';
import { addMsg, removeMsg } from '@/lib/nim/cache';
import { IApiPatientConversationTeamListTeamList } from '@/interface/type';
import useGlobal from './useGlobal';
import useAIConversation from './useAIConversation';

interface IState {
  sessionMap: any;
  historyMsgs: any[];
  curTeamId: string;
  tempMsg?: any;
  draftMap: Record<number, Record<string, string>>;
  lastMsg: {
    lastMsgId: string;
    endTime: number;
  };
  ready: boolean;
  loading: boolean;
  hasMore: boolean;
  withdrawStatus: boolean | null;
  isSearch: boolean;
  isScrollBottom: boolean;
  isPlaying: boolean;
  findMsgFlag: number;
  findMsgId: string;
  findTeamId: string;
  findMsgFocus: boolean;
  findMsgData: null | Record<string, any>;
  teamUnreadMap: Record<string, number>;
  patientTeamList: IApiPatientConversationTeamListTeamList[];
  historyRecords: any[];
  recordLastMsg: {
    lastMsgId: string;
    endTime: number;
  };
  hasRecordMore: boolean;
  recordLoading: boolean;
  needGetHistory: boolean;
}

const defaultState: IState = {
  sessionMap: {},
  historyMsgs: [],
  curTeamId: '',
  lastMsg: {
    lastMsgId: '',
    endTime: 0,
  },
  draftMap: {},
  ready: false,
  loading: false,
  hasMore: true,
  isSearch: false,
  isScrollBottom: true,
  isPlaying: false,
  withdrawStatus: null,
  findMsgFlag: 0,
  findMsgId: '',
  findTeamId: '',
  findMsgFocus: false,
  findMsgData: null,
  needGetHistory: false,
  teamUnreadMap: {},
  patientTeamList: [],
  historyRecords: [],
  recordLastMsg: {
    lastMsgId: '',
    endTime: 0,
  },
  hasRecordMore: true,
  recordLoading: false,
};

let sendMsgCache: any[] = [];
const useIM = defineStore('IM', {
  state: () => ({ ...defaultState }) as IState,
  actions: {
    initNimSdk() {
      initNimSdk({
        onMsg: this.onMsg, //消息
        setReady: (e: boolean) => (this.ready = e), // 是否准备就绪
        onSessions: this.onSessions, //同步最近会话列表
        onUpdateSession: this.onUpdateSession, //更新会话
        onTeamMsgReceipt: this.onTeamMsgReceipt, // 消息已读通知
      });
    },
    reset() {
      this.patientTeamList = [];
      this.curTeamId = '';
    },
    onMsg(msg: any) {
      const throttleUnread = throttle(() => {
        this.resetSessionUnread();
      }, 50);
      if (msg.flow === 'in') {
        if (msg.to === this.curTeamId) {
          const newMsg = processMsg(msg);
          this.historyMsgs.push(newMsg);
          throttleUnread();
          this.isScrollBottom = true;
        } else {
          //todo
        }
        // 需求调整：网易通知消息不做声音提示
        if (msg.type !== MSGT_TYPES.NOTIFICATION) {
          this.isPlaying = true;
        }
      }
    },
    onSessions(sessions: any) {
      const obj = {};
      sessions.forEach(item => {
        obj[item.to] = item.unread;
      });
      this.teamUnreadMap = obj;
    },
    onUpdateSession(session: any) {
      if (session.lastMsg?.status == 'success') {
        if (session.to === this.curTeamId) {
          this.teamUnreadMap[session.to] = 0;
        } else {
          this.teamUnreadMap[session.to] = session.unread;
        }
      }
    },
    onTeamMsgReceipt(data: any) {
      for (let i = 0; i < data.teamMsgReceipts.length; i++) {
        const curItem = data.teamMsgReceipts[i];
        getTteammsgReadAccounts(
          {
            ...curItem,
            to: curItem.teamId,
          },
          (msg: any, val: boolean) => {
            this.setMsgReceipt(msg, val);
          }
        );
      }
    },
    setMsgReceipt(msg: any, val: boolean) {
      this.historyMsgs.forEach(v => {
        if (v.idClient === msg.idClient) {
          v.userIsRead = val;
        }
      });
      // const result = this.historyMsgs.map(v => {
      //   if (v.idClient === msg.idClient) {
      //     v.userIsRead = val;
      //   }
      //   return v;
      // });
      // layzyFn(() => {
      //   if (result?.length && this.curTeamId === result[0]?.target) {
      //     this.historyMsgs = result;
      //   }
      // });
    },
    getHistoryRecordByTypes(
      msgTypes?: ('text' | 'image' | 'audio' | 'video')[]
    ) {
      this.recordLoading = true;
      getHistoryMsgHandler({
        teamId: this.curTeamId,
        msgTypes,
        limit: RECORD_LIMIT,
        callback: (msgs: any) => {
          if (msgs.length < RECORD_LIMIT) {
            this.hasRecordMore = false;
          }
          const lastMsg = msgs[0];
          this.historyRecords = msgs.reverse();
          this.recordLoading = false;
          if (lastMsg) {
            this.recordLastMsg = {
              lastMsgId: lastMsg.idServer,
              endTime: lastMsg.timestap,
            };
          }
        },
      });
    },
    getMoreHistoryRecordByTypes(
      msgTypes?: ('text' | 'image' | 'audio' | 'video')[]
    ) {
      if (!this.hasRecordMore) {
        console.log('【nim】 没有更多记录了', this.hasRecordMore);
        return false;
      }
      getMoreHistoryMsgHandler({
        teamId: this.curTeamId,
        msgTypes,
        limit: RECORD_LIMIT,
        lastMsg: this.recordLastMsg,
        callback: (msgs: any) => {
          console.log('【nim】,收到历史消息记录', msgs);
          if (msgs.length < RECORD_LIMIT) {
            this.hasRecordMore = false;
          }
          const lastMsg = msgs[0];
          const newMsg = msgs.reverse();
          this.historyRecords.push(...newMsg);
          if (lastMsg) {
            this.recordLastMsg = {
              lastMsgId: lastMsg.idServer,
              endTime: lastMsg.timestap,
            };
          }
        },
      });
    },
    getHisMsg(teamId: string) {
      this.loading = true;
      getHistoryMsgHandler({
        teamId,
        callback: (msgs: any) => {
          if (this.curTeamId !== teamId) return;
          if (msgs.length < MSG_LIMIT) {
            this.hasMore = false;
          }
          if (sendMsgCache.length) {
            const _msgs = msgs;
            sendMsgCache.forEach(msg => {
              if (!_msgs.find(v => v.idClient === msg.idClient)) {
                _msgs.push(msg);
              }
            });
            sendMsgCache = [];
            this.historyMsgs = _msgs.sort((a, b) => a?.time - b?.time);
          } else {
            this.historyMsgs = msgs;
          }
          this.loading = false;
          const lastMsg = msgs[0];
          if (lastMsg) {
            this.lastMsg = {
              lastMsgId: lastMsg.idServer,
              endTime: lastMsg.timestap,
            };
          }
          this.isScrollBottom = true;
        },
        readCallback: (msg: any, val: boolean) => {
          this.setMsgReceipt(msg, val);
        },
      });
    },
    getMoreHisMsg(teamId: string) {
      if (!this.hasMore) return;
      this.loading = true;
      getMoreHistoryMsgHandler({
        teamId,
        lastMsg: this.lastMsg,
        callback: (msgs: any) => {
          if (msgs.length < MSG_LIMIT) {
            this.hasMore = false;
          }
          this.historyMsgs.unshift(...msgs);
          this.loading = false;
          const lastMsg = msgs[0];
          if (lastMsg) {
            this.lastMsg = {
              lastMsgId: lastMsg.idServer,
              endTime: lastMsg.timestap,
            };
          }
          this.isScrollBottom = false;
        },
        readCallback: (msg: any, val: boolean) => {
          this.setMsgReceipt(msg, val);
        },
      });
    },
    sendMsgDone(error: any, msg: any) {
      if (error && error.message !== '超时') {
        ElMessage.error(error.message);
        return;
      }
      this.loading = false;
      const _msg = processMsg(msg);
      if (this.findMsgFlag) {
        sendMsgCache.push(_msg);
        this.findMsgFlag = 0;
      }
      this.historyMsgs.push(_msg);
      if (error && _msg.type === 'text') {
        console.log('************ TEXT 失败消息写入缓存*****************');
        addMsg(msg);
        return;
      }
      if (!error && _msg.type === 'text') {
        removeMsg(msg);
      }
      if (msg?.target === this.curTeamId) {
        this.isScrollBottom = true;
      }
    },
    resetSessionUnread() {
      const sessionId = `team-${this.curTeamId}`;
      (window as any).nim?.resetSessionUnread(sessionId);
    },
    sendHandler(callback: () => void) {
      if (this.findMsgFlag) {
        setTimeout(() => {
          callback();
          this.findMsgId = '';
          this.needGetHistory = true;
        }, 100);
      } else {
        callback();
      }
      /** 清除当前患者所有AI推荐信息 */
      useAIConversation().removeConversationMsg();
    },
    sendTextMsg(options: any) {
      this.sendHandler(() => sendTextMsg(options, this.sendMsgDone));
    },
    sendBlobMsg(options: any) {
      this.sendHandler(() => sendBlobMsg(options, this.sendMsgDone));
    },
    resendTextMsg(options: any) {
      this.sendHandler(() => sendTextMsg(options, this.sendMsgDone));
    },
    resendFileMsg(options: any) {
      this.sendHandler(() => sendFileMsg(options, this.sendMsgDone));
    },
    withdrawMsg({ msg }: any) {
      const env = import.meta.env.VITE_APP_nimEnv;
      if (env) msg.env = env;
      (window as any).nim.recallMsg({
        msg,
        done: (error: any) => {
          if (error) {
            this.withdrawStatus = false;
          } else {
            this.getHisMsg(this.curTeamId);
            this.withdrawStatus = true;
          }
        },
      });
    },
    findMsg({ idServer, time }: { idServer: string; time: number }) {
      if (!this.curTeamId) return;
      this.loading = true;
      findTargetMsgs({
        teamId: this.curTeamId,
        idServer,
        time,
        callback: (msgs: any) => {
          this.historyMsgs = msgs;
          this.loading = false;
          const lastMsg = msgs[0];
          if (lastMsg) {
            this.lastMsg = {
              lastMsgId: lastMsg.idServer,
              endTime: lastMsg.timestap,
            };
          }
          this.findMsgFlag += 5;
          this.findMsgId = idServer;
        },
        readCallback: (msg: any, val: boolean) => {
          this.setMsgReceipt(msg, val);
        },
      });
    },
    insertDraft(val: string) {
      const userId = useGlobal().userId;
      if (userId) {
        if (this.draftMap[userId]) {
          this.draftMap[userId][this.curTeamId] = val;
        } else {
          this.draftMap[userId] = {
            [this.curTeamId]: val,
          };
        }
      }
    },
    /** 给患者发送自定义消息 */
    sendPatientCustomMsg(options: { content: Record<string, any> }) {
      const curPatientTeamId = this.patientTeamList?.filter(
        v => v.teamType === 2
      )?.[0]?.teamNumber;
      if (!curPatientTeamId) {
        ElMessage.warning('发送消息失败，无效 TeamNumber!');
        return;
      }
      sendCustomMsg(
        {
          ...options,
          to: curPatientTeamId,
        },
        this.sendMsgDone
      );
    },
    sendCustomMsg(options: { to: string; content: Record<string, any> }) {
      sendCustomMsg(options, this.sendMsgDone);
    },
  },
});
export default useIM;
