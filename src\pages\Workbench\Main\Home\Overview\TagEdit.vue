<template>
  <div ref="TagContent" class="tags-content">
    <div class="tag-box">
      <h4 class="til">历史标签</h4>
      <div v-if="historyTags?.length === 0">无</div>
      <el-checkbox-group v-model="tagListOr" :max="20">
        <el-checkbox
          v-for="(item, i) in historyTags"
          :key="i"
          :label="item"
          :value="item"
          @change="changeTag($event, item, null)"
        >
          {{ item.tagName }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="tag-box">
      <h4 class="til">查找标签</h4>
      <div class="search mb-8">
        <el-input
          v-model="searchKey"
          maxlength="10"
          class="w-10 mr-16"
          size="large"
          placeholder="请输入标签名（最长10个汉字）"
          :prefix-icon="Search"
          @input="e => getTags(e, 1)"
        />
        <el-button link @click="changeTag(true, { tagName: searchKey }, 1)">
          存为自定义
        </el-button>
      </div>
      <div v-if="allTags?.length === 0">无</div>
      <el-checkbox-group v-model="tagListOr">
        <el-checkbox
          v-for="(item, i) in allTags"
          :key="i"
          :label="item"
          :value="item"
          @change="changeTag($event, item, null)"
        >
          {{ item.tagName }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div v-show="false">展开更多{{ bottom }}</div>
  </div>
</template>
<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import {
  getPatientTagSystem,
  getPatientTagHistory,
  addPatientTag,
  deletePatientTag,
} from '@/api/overview';
import bus from './hooks/bus';
import globalBus from '@/lib/bus';
import { useScroll } from '@vueuse/core';
import { throttle } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';

const TagContent = ref<HTMLElement | null>(null);

const { arrivedState } = useScroll(TagContent);

const bottom = computed(() => {
  if (arrivedState.bottom) {
    if (totals.value > allTag.value.length) {
      getTags(searchKey.value, ++pageNumber.value);
    }

    return true;
  }
  return false;
});

interface IProps {
  tagList?: object;
}
const props = defineProps<IProps>();
const searchKey = ref('');
const allTag = ref([]);
const historyTag = ref([]);
const allTags = ref([]);
const historyTags = ref([]);
const global = useGlobal();
const pageNumber = ref(1);
const totals = ref(10);
const tagListOr = ref(props.tagList);

const getTags = throttle(async (keyword = '', page = 1) => {
  let dataAllTag: any = await getPatientTagSystem({
    pageNumber: page,
    pageSize: 10,
    keyword,
  });
  pageNumber.value = page;
  allTag.value =
    page == 1 ? dataAllTag.data : [...allTag.value, ...dataAllTag.data];
  totals.value = dataAllTag.totals;
  initTagList();
}, 800);
const getHistoryTag = async () => {
  let data: any = await getPatientTagHistory();
  historyTag.value = data.tagList || [];
  initTagList();
};

const changeTag = (e, item, type) => {
  if (type && !item.tagName) {
    ElMessage.warning('查找标签输入框不能为空!');
    return false;
  }
  if (e && tagListOr.value.length == 20) {
    ElMessage.warning('患者标签超过限制,可清除无用标签后重新添加!');
    return false;
  }
  if (e) {
    //   新增
    addPatientTag({ patientId: global.userId, tagName: item.tagName })
      .then(() => {
        ElMessage.success('保存成功!');
        // 更新历史标签
        getHistoryTag();
        // //修改标签成功后， 刷新患者标签信息、网页左侧列表
        bus.emit('refresh-patient-Info-tag');
        globalBus.emit('refresh-patient-list');
      })
      .catch(() => {
        ElMessage.error('保存失败!');
      });
  } else {
    //   删除
    deletePatientTag({ patientTagId: item.patientTagId })
      .then(() => {
        ElMessage.success('删除成功!');
        // //修改标签成功后， 刷新患者标签信息、网页左侧列表
        bus.emit('refresh-patient-Info-tag');
        globalBus.emit('refresh-patient-list');
      })
      .catch(() => {
        ElMessage.error('删除失败!');
      });
  }
};

const initTagList = () => {
  allTags.value = allTag.value.map(v => {
    let info = tagListOr.value.find(el => el.tagName === v) || { tagName: v };
    return info;
  });
  historyTags.value = historyTag.value.map(v => {
    let info = tagListOr.value.find(el => el.tagName === v) || { tagName: v };
    return info;
  });
};

onMounted(async () => {
  getHistoryTag();
  getTags();
});
watch(
  () => props.tagList,
  () => {
    tagListOr.value = props.tagList;
    initTagList();
  }
);
</script>
<style lang="less">
.tags-content {
  height: 308px;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    width: 8px;
    background: #bebebe;
    border-radius: 5px;
  }
}
.tag-box {
  width: 464px;
  margin: 0 auto;
  .til {
    font-size: 14px;
    font-weight: bold;
    color: #101b25;
    padding: 16px 0 8px 0;
  }
  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .el-checkbox {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-right: 0;
    width: 218px;
    height: 36px;
    border-radius: 2px;
    padding: 0 12px;
    margin-bottom: 8px;
    .el-checkbox__label {
      padding: 0;
      //width: 84px;
      //height: 20px;
      font-size: 14px;
      color: #3a4762;
      line-height: 20px;
    }
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      background: #fff;
      border-radius: 2px;
      border: 1px solid #dcdee0;
      &::after {
        //width: 11px;
        //height: 8px;
        //background: #ffffff;
        //border: 2px solid transparent;
        border-width: 2px;
      }
    }
  }
  .is-checked {
    background: #e2f5e1;
    .el-checkbox__label {
      color: #2fb324;
    }
    .el-checkbox__inner {
      background: #2fb324;
      border: 1px solid #2fb324;
      &::after {
        //width: 11px;
        //height: 8px;
        //background: #ffffff;
        //border: 2px solid transparent;
        border-width: 2px;
      }
    }
  }
  .search {
    .el-input--large {
      width: 360px;
      height: 32px;

      .el-input__wrapper {
        border-radius: 2px;
        padding: 0 12px;
        .el-input__inner {
          height: 100%;
          color: #3a4762;
          &::placeholder {
            color: #bac8d4;
          }
        }
      }
    }
    .el-button {
      font-size: 14px;
      color: #2e6be6;
    }
  }
}
</style>
