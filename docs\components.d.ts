/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    IEpArrowDown: typeof import('~icons/ep/arrow-down')['default']
    IEpArrowLeft: typeof import('~icons/ep/arrow-left')['default']
    IEpArrowLeftBold: typeof import('~icons/ep/arrow-left-bold')['default']
    IEpArrowRight: typeof import('~icons/ep/arrow-right')['default']
    IEpArrowRightBold: typeof import('~icons/ep/arrow-right-bold')['default']
    IEpCaretBottom: typeof import('~icons/ep/caret-bottom')['default']
    IEpCaretRight: typeof import('~icons/ep/caret-right')['default']
    IEpCaretTop: typeof import('~icons/ep/caret-top')['default']
    IEpCircleCloseFilled: typeof import('~icons/ep/circle-close-filled')['default']
    IEpCirclePlus: typeof import('~icons/ep/circle-plus')['default']
    IEpClose: typeof import('~icons/ep/close')['default']
    IEpDArrowLeft: typeof import('~icons/ep/d-arrow-left')['default']
    IEpDArrowRight: typeof import('~icons/ep/d-arrow-right')['default']
    IEpLink: typeof import('~icons/ep/link')['default']
    IEpLoading: typeof import('~icons/ep/loading')['default']
    IEpPlus: typeof import('~icons/ep/plus')['default']
    IEpUploadFilled: typeof import('~icons/ep/upload-filled')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
