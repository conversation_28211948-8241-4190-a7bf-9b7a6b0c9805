<template>
  <span class="dialog-footer">
    <el-button
      :loading="confirmLoading"
      type="primary"
      class="sure-btn w-76 h-32"
      @click="sure"
    >
      确定
    </el-button>
    <el-button class="cancel-btn w-76 h-32" @click="cancel">取消</el-button>
  </span>
</template>
<script setup lang="ts">
interface IProps {
  confirmLoading: boolean;
}
defineProps<IProps>();
const emit = defineEmits(['submit', 'cancel']);
let sure = () => {
  emit('submit');
};
let cancel = () => {
  emit('cancel');
};
</script>
