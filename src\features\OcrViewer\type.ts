import {
  IApiOcrStartScanLdParseDataSurgicalRecord,
  IApiOcrStartScanLdParseDataColorUltrasoundDTO,
  IApiOcrStartScanLdParseDataTwelveLeadEleDTO,
  IApiOcrStartScanLdParseDataDynamicDTO,
  IApiOcrStartScanLdParseDataOutPatientPrescriptionDTO,
  IApiOcrScanImage,
} from '@/interface/type';

export interface ComponentType {
  [key: number]: Component;
}

export interface OcrProps {
  reviewId?: string;
  useType?: number;
  title?: string;
  disableScanning?: boolean;
  patientHistoryId?: number;
  componentLocation: string;
  componentLocationStr?: string;
}

export interface FileTagItem {
  tagName: string;
  annexType: number;
}

export interface InHospitalRecordType {
  /** 原文 */
  originalText?: string;
  /** 主诉、现病史 */
  mainSuit?: string;
  /** 现病史 */
  medicalHistory?: string;
  /** 个人史 */
  personalHistory?: string;
  /** 家族史 */
  familyHistory?: string;
  /** 既往史 */
  pastHistory?: string;
  /** 入院时间 */
  inHospitalTime?: string;
  /** 基本信息 */
  basicInfo?: {
    /** 身高 */
    height?: string;
    /** 体重 */
    weight?: string;
    /** 收缩压 */
    highPressure?: string;
    /** 舒张压 */
    lowPressure?: string;
    /** 心率 */
    heartRate?: string;
  };
}

export interface OutHospitalRecordType {
  /** 原文 */
  originalText?: string;
  /** 临床诊断 */
  clinicalDiagnosis?: string;
}

export interface InspectionReportType {
  /** 检验报告时间 */
  reportTime?: string;
  /** 检验报告指标信息 */
  indexInfo?: {
    /** 指标大类 */
    checkType?: number;
    /** 指标小类 */
    indexType?: number;
    /** 指标信息 */
    content?: number;
    /** 指标名称 */
    name?: string;
  }[];
}

export interface OutpatientReportType {
  /** 原文 */
  originalText?: string;
  /** 主诉 */
  mainSuit?: string;
  presentIllness?: string;

  /** 诊断 */
  diagnosis?: string;
}

export type OcrResultDataType = InHospitalRecordType &
  OutHospitalRecordType &
  InspectionReportType &
  OutpatientReportType &
  IApiOcrStartScanLdParseDataSurgicalRecord &
  IApiOcrStartScanLdParseDataColorUltrasoundDTO &
  IApiOcrStartScanLdParseDataTwelveLeadEleDTO &
  IApiOcrStartScanLdParseDataDynamicDTO &
  IApiOcrStartScanLdParseDataOutPatientPrescriptionDTO;

//ai结构化数据
export interface AiDataType {
  id?: number;
  key?: string;
  value?: number | string;
}

//ai转本地到组件数据

export interface ComponentDataType {
  suitList?: AiDataType[];
  personalHistoryMsg?: AiDataType[];
  familyHistoryMsg?: AiDataType[];
  basicInfo?: AiDataType[];
  diseaseData?: AiDataType[];
  inHospitalTime?: AiDataType[];
}

export type IAiResultData =
  | ComponentDataType
  | Array<
      Record<
        string,
        AiDataType &
          IApiOcrScanImage & {
            key: string;
            items: Record<string, string | number>;
          }
      >
    >;
