<template>
  <div class="w-full">
    <FormHeader @search="searchHandler" />
    <TableContent :params="params" />
  </div>
</template>

<script setup lang="ts">
import FormHeader from './Header/index.vue';
import TableContent from './Content/index.vue';
const params = ref();
defineOptions({
  name: 'InternContent',
});
const searchHandler = (val: any) => {
  params.value = val;
};
</script>

<style scoped lang="less">
// todo
</style>
