import { cloneDeep, isNil, isNumber } from 'lodash-es';
import { getThreshold, updateThreshold } from '@/api/indicatorsReport';

export type ThresholdType =
  | 'bloodPressure'
  | 'heartRate'
  | 'bloodLipid'
  | 'bloodSugar';

export const thresholdTypeMapping: Record<string, ThresholdType> = {
  BLOOD_PRESSURE: 'bloodPressure',
  HEART_RATE: 'heartRate',
  INDEX: 'bloodLipid',
  BLOOD_SUGAR: 'bloodSugar',
};

export interface IThresholdSubmitParams {
  type: ThresholdType;
  id?: number;
  patientId: number;
  uid: number;
  templateName: string;
}

interface IComThresholdConfig {
  // 风险等级
  level: number;
  // 警告描述
  warningInfo: {
    title: string;
    tips: string;
  };
  label: string;
}
interface IThreshold {
  defaultVal: number;
  value: number | undefined;
  disabled?: boolean;
}

// 血压阈值
export interface IBloodPressure extends IComThresholdConfig {
  // 收缩压
  systolic: IThreshold;
  // 舒张压
  diastolic: IThreshold;
}

// 心率阈值 or 血脂阈值
export interface IHeartRateOrBloodLipid extends IComThresholdConfig {
  // 阈值
  threshold: IThreshold;
}
// 血糖
export interface IBloodSugarData {
  // 系统监测并提醒患者测量7天血糖
  status: 0 | 1;
  lev1Min: IThreshold;
  lev1Max: IThreshold;
  lev2List: Array<{ type: string; name: string } & IThreshold>;
}
export default function useThresholdSet() {
  // 血压
  const bloodPressureList = ref<IBloodPressure[]>([
    {
      level: 0,
      warningInfo: {
        title: '偏低',
        tips: '一旦出现收缩压或舒张压低于偏低值',
      },
      label: '偏低值',
      // 收缩压
      systolic: {
        defaultVal: 90,
        value: undefined,
      },
      // 舒张压
      diastolic: {
        defaultVal: 40,
        value: undefined,
      },
    },
    {
      level: 1,
      warningInfo: {
        title: '1级预警值',
        tips: '7日内收缩压或舒张压截尾平均值处于一级预警和二级预警值之间',
      },
      label: '一级预警值',
      // 收缩压
      systolic: {
        defaultVal: 140,
        value: undefined,
      },
      // 舒张压
      diastolic: {
        defaultVal: 90,
        value: undefined,
      },
    },
    {
      level: 2,
      warningInfo: {
        title: '2级预警值',
        tips: '3日内累计2次收缩压或舒张压处于二级预警值和三级预警值之间',
      },
      label: '二级预警值',
      // 收缩压
      systolic: {
        defaultVal: 160,
        value: undefined,
      },
      // 舒张压
      diastolic: {
        defaultVal: 100,
        value: undefined,
      },
    },
    {
      level: 3,
      warningInfo: {
        title: '3级预警值',
        tips: '收缩压或舒张压高于三级预警值',
      },
      label: '三级预警值',
      // 收缩压
      systolic: {
        defaultVal: 180,
        value: undefined,
      },
      // 舒张压
      diastolic: {
        defaultVal: 110,
        value: undefined,
      },
    },
  ]);

  // 心率
  const heartRateList = ref<IHeartRateOrBloodLipid[]>([
    {
      level: 0,
      warningInfo: {
        title: '超低',
        tips: '患者心率值一旦低于超低值，将立即心率超低提示',
      },
      label: '超低值',
      threshold: {
        defaultVal: 50,
        value: undefined,
      },
    },
    {
      level: 1,
      warningInfo: {
        title: '偏高',
        tips: '5日内，累计5次心率测量在偏高与超高之间，提示心率偏高',
      },
      label: '偏高值',
      threshold: {
        defaultVal: 80,
        value: undefined,
      },
    },
    {
      level: 2,
      warningInfo: {
        title: '超高',
        tips: '患者心率值一旦达到此区间，将立即心率超高提示',
      },
      label: '超高值',
      threshold: {
        defaultVal: 100,
        value: undefined,
      },
    },
  ]);

  // 血脂（LDL-c）
  const bloodLipidList = ref<IHeartRateOrBloodLipid[]>([
    {
      level: 0,
      warningInfo: {
        title: '超低',
        tips: 'LDL-c值下限',
      },
      label: '超低值',
      threshold: {
        defaultVal: 0,
        value: undefined,
      },
    },
    {
      level: 1,
      warningInfo: {
        title: '超高',
        tips: 'LDL-c值上限',
      },
      label: '超高值',
      threshold: {
        defaultVal: 3.12,
        value: undefined,
      },
    },
  ]);

  // 血糖
  const bloodSugarData = ref<IBloodSugarData>({
    status: 0,
    lev1Min: {
      value: undefined,
      defaultVal: 3.9,
    },
    lev1Max: {
      value: undefined,
      defaultVal: 33.3,
    },
    lev2List: [
      {
        type: 'dinner',
        name: '餐后2小时血糖（mmol/L）',
        value: undefined,
        defaultVal: 11.1,
      },
      {
        type: 'stomach',
        name: '空腹血糖（mmol/L）',
        value: undefined,
        defaultVal: 7,
      },
      {
        type: 'random',
        name: '随机血糖（mmol/L）',
        value: undefined,
        defaultVal: 11.1,
      },
    ],
  });

  const configDataMap = reactive({
    bloodPressure: bloodPressureList,
    heartRate: heartRateList,
    bloodLipid: bloodLipidList,
    bloodSugar: bloodSugarData,
  });

  const loading = ref(false);

  async function setThresholdSubmit(
    params: IThresholdSubmitParams,
    callback?: () => void
  ) {
    const { type, ...rest } = params;
    if (verifyEmptyData(type)) {
      return ElMessage.error('值不能为空！');
    }
    try {
      loading.value = true;
      await updateThreshold({
        ...transformData(type),
        ...rest,
        id: thresholdDetailId.value,
      });
      ElMessage.success('操作成功！');
      callback && callback();
    } finally {
      loading.value = false;
    }
  }

  // 数据转化
  function transformData(type: ThresholdType) {
    let data = {};
    switch (type) {
      case 'bloodPressure': {
        const originData: IBloodPressure[] = cloneDeep(configDataMap[type]);

        data = {
          systolicNormalValue: originData[0].systolic.value,
          diastolicNormalValue: originData[0].diastolic.value,
          systolicLevel1Value: originData[1].systolic.value,
          diastolicLevel1Value: originData[1].diastolic.value,
          systolicLevel2Value: originData[2].systolic.value,
          diastolicLevel2Value: originData[2].diastolic.value,
          systolicLevel3Value: originData[3].systolic.value,
          diastolicLevel3Value: originData[3].diastolic.value,
        };
        break;
      }

      case 'heartRate': {
        const originData: IHeartRateOrBloodLipid[] = cloneDeep(
          configDataMap[type]
        );

        data = {
          minHeartRateValue: originData[0].threshold.value,
          tallHeartRateValue: originData[1].threshold.value,
          maxHeartRateValue: originData[2].threshold.value,
        };
        break;
      }
      case 'bloodLipid': {
        const originData: IHeartRateOrBloodLipid[] = cloneDeep(
          configDataMap[type]
        );

        data = {
          ldlcMin: originData[0].threshold.value,
          ldlcMax: originData[1].threshold.value,
        };
        break;
      }
      case 'bloodSugar': {
        const originData: IBloodSugarData = cloneDeep(configDataMap[type]);

        const { lev2List, status, lev1Min, lev1Max } = originData;

        data = {
          status,
          lev1Min: lev1Min.value,
          lev1Max: lev1Max.value,
          lev2DinnerMin: lev2List[0].value,
          lev2DinnerMax: lev1Max.value,
          lev2StomachMin: lev2List[1].value,
          lev2StomachMax: lev1Max.value,
          lev2RandomMin: lev2List[2].value,
          lev2RandomMax: lev1Max.value,
        };
        break;
      }
    }

    return data;
  }

  function verifyEmptyData(type: ThresholdType) {
    let emptyData = false;
    const dataList = configDataMap[type];
    switch (type) {
      case 'bloodPressure':
        emptyData = !!(dataList as IBloodPressure[]).find(item => {
          const {
            systolic: { value: systolicVal },
            diastolic: { value: diastolicVal },
          } = item;

          return !isNumber(systolicVal) || !isNumber(diastolicVal);
        });
        break;
      case 'heartRate':
      case 'bloodLipid':
        emptyData = !!(dataList as IHeartRateOrBloodLipid[]).find(
          item => !isNumber(item.threshold.value)
        );
        break;
      case 'bloodSugar':
        {
          const {
            lev1Min: { value: lev1MinVal },
            lev1Max: { value: lev1MaxVal },
            lev2List,
          } = dataList as IBloodSugarData;
          if (!isNumber(lev1MinVal) || !isNumber(lev1MaxVal)) {
            emptyData = true;
          } else {
            emptyData = !!lev2List.find(item => !isNumber(item.value));
          }
        }
        break;
    }

    return emptyData;
  }

  function resetThreshold(type: ThresholdType) {
    switch (type) {
      case 'bloodPressure':
        bloodPressureList.value = bloodPressureList.value.map(item => {
          const { systolic, diastolic } = item;
          return {
            ...item,
            systolic: {
              ...systolic,
              value: systolic.defaultVal,
            },
            diastolic: {
              ...diastolic,
              value: diastolic.defaultVal,
            },
          };
        });
        break;
      case 'heartRate':
        heartRateList.value = heartRateList.value.map(item => {
          return {
            ...item,
            threshold: {
              ...item.threshold,
              value: item.threshold.defaultVal,
            },
          };
        });
        break;
      case 'bloodLipid':
        bloodLipidList.value = bloodLipidList.value.map(item => {
          return {
            ...item,
            threshold: {
              ...item.threshold,
              value: item.threshold.defaultVal,
            },
          };
        });
        break;
      case 'bloodSugar':
        {
          const { lev1Min, lev1Max, lev2List } = bloodSugarData.value;
          bloodSugarData.value = {
            status: 0,
            lev1Min: {
              ...lev1Min,
              value: lev1Min.defaultVal,
            },
            lev1Max: {
              ...lev1Max,
              value: lev1Max.defaultVal,
            },
            lev2List: lev2List.map(item => ({
              ...item,
              value: item.defaultVal,
            })),
          };
        }
        break;
    }
  }

  const thresholdDetailId = ref<number | undefined>(undefined);

  // 获取阈值详情
  async function getThresholdDetail(
    patientId: number,
    templateName: string,
    type: ThresholdType
  ) {
    const res = await getThreshold({ patientId, templateName });
    thresholdDetailId.value = res.id;
    switch (type) {
      case 'bloodPressure': {
        const originData: IBloodPressure[] = cloneDeep(configDataMap[type]);
        originData[0].systolic.value = res.systolicNormalValue;
        originData[0].diastolic.value = res.diastolicNormalValue;
        originData[1].systolic.value = res.systolicLevel1Value;
        originData[1].diastolic.value = res.diastolicLevel1Value;
        originData[2].systolic.value = res.systolicLevel2Value;
        originData[2].diastolic.value = res.diastolicLevel2Value;
        originData[3].systolic.value = res.systolicLevel3Value;
        originData[3].diastolic.value = res.diastolicLevel3Value;
        bloodPressureList.value = originData;
        break;
      }

      case 'heartRate': {
        const originData: IHeartRateOrBloodLipid[] = cloneDeep(
          configDataMap[type]
        );
        originData[0].threshold.value = res.minHeartRateValue;
        originData[1].threshold.value = res.tallHeartRateValue;
        originData[2].threshold.value = res.maxHeartRateValue;
        heartRateList.value = originData;
        break;
      }
      case 'bloodLipid': {
        const originData: IHeartRateOrBloodLipid[] = cloneDeep(
          configDataMap[type]
        );
        originData[0].threshold.value = res.ldlcMin;
        originData[1].threshold.value = res.ldlcMax;
        bloodLipidList.value = originData;
        break;
      }
      case 'bloodSugar': {
        const originData: IBloodSugarData = cloneDeep(configDataMap[type]);
        const status = res.status as 0 | 1 | null;
        originData.status = !isNil(status) ? status : 0;
        originData.lev1Min.value = res.lev1Min;
        originData.lev1Max.value = res.lev1Max;
        originData.lev2List[0].value = res.lev2DinnerMin;
        originData.lev1Max.value = res.lev2DinnerMax;
        originData.lev2List[1].value = res.lev2StomachMin;
        originData.lev1Max.value = res.lev2StomachMax;
        originData.lev2List[2].value = res.lev2RandomMin;
        originData.lev1Max.value = res.lev2RandomMax;

        bloodSugarData.value = originData;
        break;
      }
    }
  }

  return {
    loading,
    configDataMap,
    bloodPressureList,
    setThresholdSubmit,
    resetThreshold,
    getThresholdDetail,
  };
}
