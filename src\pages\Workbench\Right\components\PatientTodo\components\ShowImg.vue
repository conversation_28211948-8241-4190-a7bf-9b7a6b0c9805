<template>
  <img
    v-if="[1, 18].includes(type)"
    src="@/assets/imgs/todo/patient-enrollment-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="[2, 19].includes(type)"
    src="@/assets/imgs/todo/group-mission-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="[3, 13, 16].includes(type)"
    src="@/assets/imgs/todo/review-reminder-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 4"
    src="@/assets/imgs/todo/risk-treatment-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 5"
    src="@/assets/imgs/todo/symptom-handling-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 6"
    src="@/assets/imgs/todo/dosing-tracking-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 7"
    src="@/assets/imgs/todo/outpatient-follow-up-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 8"
    src="@/assets/imgs/todo/hospitalization-follow-up-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="[9, 11].includes(type)"
    src="@/assets/imgs/todo/follow-up-reminder-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 10"
    src="@/assets/imgs/todo/customize-to-do-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 12"
    src="@/assets/imgs/todo/inquiry-reply-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="[14, 15].includes(type)"
    src="@/assets/imgs/todo/clincal-event-img.png"
    alt=""
    class="w-16 h-16"
  />
  <img
    v-if="type === 17"
    src="@/assets/imgs/todo/doubt-img.png"
    alt=""
    class="w-16 h-16"
  />
</template>
<script setup lang="ts">
const props = defineProps({
  type: {
    default: 1,
    type: Number,
  },
});

let { type } = props;
</script>
