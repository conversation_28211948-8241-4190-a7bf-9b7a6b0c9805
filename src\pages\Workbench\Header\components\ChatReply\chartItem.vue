<template>
  <div class="content p-sm">
    <div class="flex justify-between items-center py-sm">
      <div class="flex items-center">
        <div class="avatar flex-c">{{ patientInfo.name.slice(0, 1) }}</div>
        <div class="base-box ml-2xs">
          <span class="base-box-name">{{ patientInfo.name }}</span>
          <el-divider direction="vertical" />
          <span>{{ patientInfo.gender }}</span>
          <el-divider direction="vertical" />
          <span>{{ patientInfo.age }}</span>
        </div>
      </div>
      <div class="flex-c">
        <span class="text-base font-bold text-primary">
          {{ item.recommends?.length || 0 }}条信息
        </span>
        <div
          class="ignore-all-btn cursor-pointer flex-c ml-2xs hover:opacity-80"
          @click="emits('handleIgnoreAll', patientInfo.id || 0)"
        >
          <i-ep-delete class="mr-3xs" />忽略
        </div>
      </div>
    </div>
    <div
      v-for="recommend in item.recommends"
      :key="recommend.recommendId"
      class="chat-box mb-sm"
    >
      <div class="reply-time text-center pt-xs -mb-3xs">
        {{ formatResTime(recommend.sentenceTime) }}
      </div>
      <div class="px-sm">
        <div class="chat-box-name text-xs">患者</div>
        <div class="left-msg">
          <div class="left-triangle"></div>
          <div v-for="(sentence, i) in recommend.sentences" :key="i">
            {{ sentence }}
          </div>
        </div>
      </div>
      <div class="flex justify-end p-sm pt-0">
        <div class="right-msg">
          <div class="right-triangle"></div>
          {{ recommend.outputDialogue || '--' }}
        </div>
        <img
          src="@/assets/imgs/chat/chat-img.png"
          class="chat-img"
          alt="chat"
        />
      </div>
      <div class="reply-btn-box p-sm">
        <el-link
          :class="['!text-danger', 'hover:opacity-80', '!text-sm']"
          :underline="false"
          @click="emits('handleRespond', { recommend, confirmType: 'IGNORED' })"
        >
          忽略此条建议
        </el-link>
        <div class="flex">
          <div
            class="flex-c filling-btn hover:opacity-80"
            @click="
              emits('handleRespond', { recommend, confirmType: 'CONFIRMED' })
            "
          >
            填入对话
          </div>
          <div
            class="flex-c reply-btn ml-2xs hover:opacity-80"
            @click="
              emits('handleRespond', { recommend, confirmType: 'REPLIED' })
            "
          >
            直接回复
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PropType } from 'vue';
import { formatTime } from '@/utils';
import dayjs from 'dayjs';
import {
  IApiRecommendConversationQueryPageContents,
  IApiRecommendConversationQueryPageContentsRecommends,
} from '@/interface/type';

const props = defineProps({
  item: {
    type: Object as PropType<IApiRecommendConversationQueryPageContents>,
    default: () => {},
  },
  isCurrentPatientConversation: Boolean,
});

const emits = defineEmits<{
  (
    e: 'handleRespond',
    obj: {
      recommend: IApiRecommendConversationQueryPageContentsRecommends;
      confirmType: 'IGNORED' | 'CONFIRMED' | 'REPLIED';
    }
  ): void;
  (e: 'handleIgnoreAll', patientId: number): void;
}>();

const patientInfo = computed(() => {
  const { gender, age, name, id } = props.item?.patient || {};
  return {
    gender: gender === 1 ? '男' : gender === 2 ? '女' : '未知',
    name: name || '--',
    age: (age || '--') + '岁',
    id,
  };
});

const formatResTime = computed(() => (time?: string | number) => {
  if (!time) return '';
  const tArr = formatTime(time, true);
  return `${tArr[0]} ${dayjs(time).format('HH:mm')}`;
});
</script>
<style lang="less" scoped>
.content {
  position: relative;
  padding: 8px 16px 16px;
  font-size: 14px;
  background: #f7f8fa;

  &:before,
  &:after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
  }
  &:before {
    top: 0;
    height: 8px;
    background: #2e6be6;
  }
  &:after {
    bottom: 0;
    height: 16px;
    background: #fff;
  }

  .avatar {
    width: 30px;
    height: 30px;
    font-size: 16px;
    font-weight: 600;
    color: #2e6be6;
    border-radius: 50%;
    background: #fff;
    border: 1px solid #efefef;
  }
  .base-box {
    color: #7a8599;
    :deep(.el-divider--vertical) {
      height: 12px;
    }
    &-name {
      font-weight: 600;
      color: #3a4762;
    }
  }
  .ignore-all-btn {
    width: 72px;
    height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    color: #e63746;
  }
  .chat-box {
    background: #fff;
    border: 1px solid #e1e5ed;
    border-radius: 4px;
    &-name,
    .reply-time {
      color: #7a8599;
    }
    .left-msg,
    .right-msg {
      max-width: calc(100% - 100px);
      position: relative;
      border-radius: 4px;
      padding: 12px;
      font-size: 16px;
      white-space: pre-wrap;
    }
    .left-msg {
      margin: 4px 0 4px 8px;
      border-top-left-radius: 0;
      color: #3a4762;
      background: #f7f8fa;
      .left-triangle {
        position: absolute;
        border: 6px solid #f7f8fa;
        border-bottom-color: transparent;
        border-left-color: transparent;
        left: -8px;
        top: 0;
      }
    }
    .right-msg {
      margin: 10px 16px 0 0;
      border-top-right-radius: 0;
      background: #0a73e4;
      color: #fff;
      .right-triangle {
        position: absolute;
        border: 6px solid #0a73e4;
        border-bottom-color: transparent;
        border-right-color: transparent;
        right: -8px;
        top: 0;
      }
    }
    .chat-img {
      width: 32px;
      height: 32px;
    }

    .reply-btn-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #e1e5ed;

      .filling-btn,
      .reply-btn {
        width: 72px;
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #2e6be6;
        font-size: 14px;
        color: #2e6be6;
        cursor: pointer;
      }
      .reply-btn {
        background: #2e6be6;
        color: #ffffff;
      }
    }
  }
}
</style>
