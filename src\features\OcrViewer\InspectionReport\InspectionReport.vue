<template>
  <div>
    <ReportHead @get-report-head-params="getReportHeadParams" />
    <div v-if="formList?.length" class="p-lg bg-white">
      <template v-for="item in formList" :key="item._id">
        <div class="flex items-center bg-[#f7f8fa] h-44 pr-lg pl-md mb-sm">
          <div class="bg-[#2E6BE6] w-10 h-10 rounded-xl mr-2xs"></div>
          <h3 class="text-base font-bold text-[#101B25]">
            {{ item.name }}
          </h3>
          <div class="flex-1 flex justify-end">
            <span
              class="cursor-pointer text-[#DC0101]"
              @click="handleDelete(item._id)"
            >
              删除
            </span>
          </div>
        </div>
        <SingleDynamicForm
          :ref="el => (dynamicFormRefMap[item._id] = el)"
          v-model="item.data"
          mode="create"
          :category="item['key']"
          hidden-file-upload
        />
      </template>
    </div>
    <el-button class="mt-xl" type="primary" link @click="reportVisible = true">
      <CirclePlus class="mr-3xs w-20" />
      新增报告单
    </el-button>
    <Dialog
      v-if="reportVisible"
      v-model:visible="reportVisible"
      title="新增报告单"
      :width="840"
    >
      <SelectReport
        :business-call-enabled="false"
        :filter-keys="['ecg_12', 'ecg_dynamic', 'echocardiogram']"
        @handle-checked="handleSelectReport"
        @handle-close="reportVisible = false"
      />
    </Dialog>
  </div>
</template>
<script setup lang="ts">
import { cloneDeep, isArray, uniqueId } from 'lodash-es';
import { CirclePlus } from '@element-plus/icons-vue';
import ReportHead from './ReportHead.vue';
import Dialog from '@/components/Dialog/index.vue';
import SelectReport from '@/features/SelectReport/index.vue';
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';
import globalBus from '@/lib/bus';
import { useOcrScan } from '@/store/module/useOcrScan';
import { ocrSaveCheckIndex } from '@/api/ocr';
import { TypeCheckHeadParams } from './type';
import { handleFormatDiagnoseReportData } from '@/features/OcrViewer/utils';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';
import { VALIDATION_PASSED } from '@/constant';

const ocrStore = useOcrScan();
const saveOcrStore = useSaveOcrInfo();

const headData = ref<TypeCheckHeadParams>({
  type: 0,
  patientHistoryId: null,
});
const dynamicFormRefMap = reactive({});
const formList = ref<any[]>();

/** 新增报告单的弹窗显示控制 */
const reportVisible = ref(false);
const handleSelectReport = (data: { name: string; key: string }[]) => {
  const newFormList = data.map(({ key, name }) => {
    return {
      key,
      name,
      data: {},
      _id: uniqueId(),
    };
  });
  formList.value = [...(formList.value || []), ...newFormList];
  reportVisible.value = false;
};
const handleDelete = (_id: string) => {
  formList.value = formList.value?.filter(item => item._id !== _id) || [];
};

const getReportHeadParams = (val: TypeCheckHeadParams) =>
  (headData.value = val);
const locatedPatientHistoryId = inject('locatedPatientHistoryId');
const componentLocation = inject('componentLocation');

const CASE_TYPE_MAP = Object.freeze({
  REVIEW_RECORDS: 2,
  OUTPATIENT_RECORDS: 1,
  HOSPITALIZATION_RECORDS: 0,
});
const locatedCaseType = computed(
  () => CASE_TYPE_MAP[componentLocation as string] ?? null
);

const saveData = async () => {
  const { patientHistoryId, type } = headData.value;
  if (!patientHistoryId && !locatedPatientHistoryId) {
    return ElMessage.warning('请选择记录日期！');
  }
  for (const item of formList.value || []) {
    const ref = dynamicFormRefMap[item._id];
    if (ref) {
      const res = await ref?.submit(false);
      if (res !== VALIDATION_PASSED) return;
    }
  }
  const reqData = formList.value?.map(item => {
    return {
      ...cloneDeep(item.data),
      ...saveOcrStore.getExtraParams(),
      accessory: [ocrStore.globalImgInfo.currentImgUrl.url],
      source_id: locatedPatientHistoryId || patientHistoryId,
      source_type: locatedPatientHistoryId ? locatedCaseType.value : type,
    };
  });

  await ocrSaveCheckIndex(reqData);
  ElMessage.success('保存成功!');
  globalBus.emit('close-ocr-dialog');
  globalBus.emit('saved-ocr-info');
  const id: any = locatedPatientHistoryId || patientHistoryId;
  if (id) {
    globalBus.emit('refresh-attachment', id);
    globalBus.emit('refresh-record-data', id);
  }
};

const setFormData = () => {
  if (isArray(ocrStore.aiResultData)) {
    formList.value = handleFormatDiagnoseReportData(ocrStore.aiResultData)?.map(
      item => {
        return {
          key: item.key,
          name: item.name,
          data: cloneDeep(item),
          _id: uniqueId(),
        };
      }
    );
  }
};
watch(
  () => ocrStore.aiResultData,
  () => {
    setFormData();
  },
  { deep: true }
);

onMounted(() => {
  setFormData();
});
defineExpose({
  saveData,
});
defineOptions({
  name: 'InspectionReport',
});
</script>
