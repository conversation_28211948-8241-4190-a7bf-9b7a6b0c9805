<template>
  <div class="mission">
    <div class="postpone flex items-center mb-12">
      <div class="title">跟踪成功：</div>
      <div class="time-box ml-12">
        <div class="flex items-center">
          <div
            v-for="item in patientTypeList"
            :key="item.id"
            class="all-box flex items-center cursor-pointer mr-27"
            @click="changeResult(item.id)"
          >
            <div
              v-if="missionResult === item.id"
              class="change-box-checked mr-8 flex items-center justify-center"
            >
              <div class="interior-check"></div>
            </div>
            <div v-else class="change-box mr-8"></div>
            <span class="change-title">{{ item.title }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="postpone flex">
      <div class="title mt-4">跟踪结果：</div>
      <div class="time-box mb-16">
        <div v-if="missionResult === 1" class="result-box">
          <div class="checkbox-box ml-12">
            <el-checkbox-group
              v-model="checkResultSuccessList"
              @change="changeMissionResultSuccess"
            >
              <el-checkbox label="用药后好转" />
              <el-checkbox label="未执行用药建议" />
              <el-checkbox label="建议进一步门诊" />
              <el-checkbox label="建议进一步住院" />
              <el-checkbox label="其他" />
            </el-checkbox-group>
          </div>
          <div
            v-if="checkResultSuccessList.some(item => item === '其他')"
            class="mission-type-reason"
          >
            <el-input
              v-model="miassionResultSuccessInput"
              placeholder="请输入其他结果（100字内）"
              clearable
              show-word-limit
              maxlength="100"
              :rows="3"
              type="textarea"
              resize="none"
            />
          </div>
        </div>
        <div v-if="missionResult === 2" class="result-box">
          <div class="checkbox-box ml-12">
            <el-checkbox-group
              v-model="checkResultLoseList"
              @change="changeMissionResult"
            >
              <el-checkbox label="无法联系" />
              <el-checkbox label="拒绝沟通" />
              <el-checkbox label="其他" />
            </el-checkbox-group>
          </div>
          <div
            v-if="checkResultLoseList.some(item => item === '其他')"
            class="mission-type-reason mt-8"
          >
            <el-input
              v-model="miassionResultLoseInput"
              placeholder="请输入其他结果（100字内）"
              clearable
              show-word-limit
              maxlength="100"
              :rows="3"
              type="textarea"
              resize="none"
            />
          </div>
        </div>
      </div>
    </div>
    <Btns @submit="submit" @cancel="cancel" />
  </div>
</template>
<script setup lang="ts">
import Btns from './Btns.vue';
import { dialogTip, getUserRoles, todoType } from '../index';
import { handleTodoApi } from '@/api/todo';
import useTodo from '@/store/module/useTodo';
import useUserStore from '@/store/module/useUserStore';
let useTodoInfo = useTodo();

const userStore = useUserStore();
// 跟踪成功
const checkResultSuccessList = ref([]);
const miassionResultSuccessInput = ref<string>('');
let changeMissionResultSuccess = () => {
  if (!checkResultSuccessList.value.some(item => item === '其他')) {
    miassionResultSuccessInput.value = '';
  }
};

// 跟踪失败
const checkResultLoseList = ref([]);
const miassionResultLoseInput = ref<string>('');
let changeMissionResult = () => {
  if (!checkResultLoseList.value.some(item => item === '其他')) {
    miassionResultLoseInput.value = '';
  }
};
let missionResult = ref<number>(1);
let patientTypeList = ref([
  { title: '是', id: 1 },
  { title: '否', id: 2 },
]);
let changeResult = (id: number) => {
  missionResult.value = id;
  if (id === 1) {
    checkResultLoseList.value = [];
    miassionResultLoseInput.value = '';
  }
  if (id === 2) {
    checkResultSuccessList.value = [];
    miassionResultSuccessInput.value = '';
  }
};

let submit = () => {
  // 跟踪成功
  if (missionResult.value === 1 && !checkResultSuccessList.value.length) {
    return dialogTip('请选择跟踪结果！');
  } else if (
    checkResultSuccessList.value.some(item => item === '其他') &&
    !miassionResultSuccessInput.value
  ) {
    return dialogTip('请填写其他结果！');
  }

  //   跟踪失败
  if (missionResult.value === 2 && !checkResultLoseList.value.length) {
    return dialogTip('请选择跟踪结果！！');
  } else if (
    checkResultLoseList.value.some(item => item === '其他') &&
    !miassionResultLoseInput.value
  ) {
    return dialogTip('请填写其他结果！');
  } else {
    let params = {
      missionResult: missionResult.value,
      checkResultSuccessList: checkResultSuccessList.value,
      miassionResultSuccessInput: miassionResultSuccessInput.value,
      checkResultLoseList: checkResultLoseList.value,
      miassionResultLoseInput: miassionResultLoseInput.value,
    };
    handleTodoApi({
      backlogId: useTodoInfo.todoInfo.backlogId,
      type: todoType[useTodoInfo.todoInfo.type - 1],
      content: JSON.stringify(params),
      headId: userStore.accountId,
      headRole: getUserRoles(),
    }).then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('处理成功！', 'success');
        cancel();
      } else {
        dialogTip(message);
      }
    });
  }
};
const emit = defineEmits(['close']);
let cancel = () => {
  emit('close');
};
</script>
<style scoped lang="less">
.mission {
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #3a4762;
  }
  .postpone {
    padding-left: 24px;
    margin-top: -4px;
    :deep(.time-box) {
      flex: 1;
      .result-box {
        .checkbox-box {
          .el-checkbox-group {
            display: flex;
            flex-direction: column;
          }
        }
      }
      .all-box {
        .change-box {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #dcdee0;
        }
        .change-box-checked {
          width: 14px;
          height: 14px;
          background: #ffffff;
          border-radius: 8px;
          border: 1px solid #2e6be6;
          .interior-check {
            width: 10px;
            height: 10px;
            background: #0a73e4;
            border-radius: 8px;
          }
        }
        .change-title {
          font-size: 14px;
          color: #3a4762;
        }
      }
      .el-checkbox-group {
        .is-checked {
          .el-checkbox__label {
            color: #3a4762;
          }
          .el-checkbox__inner {
            border-color: #0a73e4;
            background: #0a73e4;
          }
        }
      }
      .mission-type-reason {
        width: 100%;
        padding: 12px;
        box-sizing: border-box;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
        height: 32px;
      }
    }
  }
}
</style>
