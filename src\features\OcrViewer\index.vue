<template>
  <HrtDialog
    v-model="visible"
    class="ocr-dialog"
    :show-close="false"
    draggable
    size="large"
    destroy-on-close
    append-to-body
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="isFullScreen"
    :width="dialogWidth"
    top="5vh"
  >
    <template #header>
      <div class="flex box-border justify-between px-24 py-16">
        <div class="font-bold text-base">{{ title || 'OCR识别' }}</div>
        <el-icon :size="16" class="cursor-pointer" @click="closeDialog">
          <i-ep-close />
        </el-icon>
      </div>
    </template>
    <div
      v-loading="ocrStore.cropperImgInfo.cropperLoading"
      element-loading-text="正在打开..."
      class="content-box flex flex-col"
    >
      <div class="top-content flex-1 flex">
        <div class="left-content w-496 flex flex-col">
          <div class="ocrbtn-box">
            <div class="left">
              <div class="scan-btn-box">
                <div
                  v-if="!disableScanning"
                  class="img-box"
                  @click="startScanImg()"
                >
                  <img
                    v-if="!hasContent"
                    src="@/assets/imgs/ocr/doscan.png"
                    alt=""
                  />
                  <img
                    v-if="hasContent"
                    src="@/assets/imgs/ocr/rescan.png"
                    alt=""
                  />
                </div>
                <div v-if="hasContent" @click.stop="startScanImg(true)">
                  重新扫描
                </div>
              </div>
              <div v-if="hasContent" class="type-btn">
                <div
                  :class="{ activeColor: currentImgType === 1 }"
                  @click="changeImgType(1)"
                >
                  增强图
                </div>
                <div
                  :class="{ activeColor: currentImgType === 0 }"
                  @click="changeImgType(0)"
                >
                  原图
                </div>
              </div>
            </div>
            <div class="right">
              <div class="img-box">
                <img
                  v-if="!isFullScreen"
                  src="@/assets/imgs/ocr/fullscren.png"
                  alt=""
                  @click="openFullScreen"
                />
                <img
                  v-if="isFullScreen"
                  src="@/assets/imgs/ocr/nofullscreen.png"
                  alt=""
                  @click="openFullScreen"
                />
                <img
                  src="@/assets/imgs/ocr/zoomOut.png"
                  alt=""
                  @click="imgScaleHandle(-0.25)"
                />
                <img
                  src="@/assets/imgs/ocr/zoomIn.png"
                  alt=""
                  @click="imgScaleHandle(0.25)"
                />
              </div>
              <div class="img-box">
                <img
                  src="@/assets/imgs/ocr/turnleft.png"
                  alt=""
                  @click="handleRotate(-90)"
                />
                <img
                  src="@/assets/imgs/ocr/turnright.png"
                  alt=""
                  @click="handleRotate(90)"
                />
              </div>
            </div>
          </div>
          <div ref="maskBox" class="imgscan-box flex-1">
            <div
              v-if="ocrStore.loadingStatus"
              :class="['scan-contain', { active: ocrStore.loadingStatus }]"
            ></div>
            <img
              v-if="url && !loading && currentImgType === 0"
              id="ocrCurrentImg"
              :src="url"
              alt=""
              :style="{
                width: imgW,
                height: imgH,
                top: top + 'px',
                left: left + 'px',
                right: 0,
                bottom: 0,
                transform: `scale(${scale}) rotateZ(${deg}deg)`,
                userSelect: 'none',
                margin: 'auto',
              }"
              @mousedown="onmousedownHandle"
            />
            <CopyTextImgBox
              v-if="imageEnhancedUrl && !loading && currentImgType === 1"
              :url="imageEnhancedUrl"
              :scale="scale"
              :deg="deg"
              :fullscreen="isFullScreen"
            />
            <div
              v-if="!hasContent"
              class="pre-img img-icon cursor-pointer"
              @click="changeImg(-1)"
            >
              <el-icon :size="16">
                <i-ep-arrowLeftBold />
              </el-icon>
            </div>
            <div
              v-if="!hasContent"
              class="next-img img-icon cursor-pointer"
              @click="changeImg(1)"
            >
              <el-icon :size="16">
                <i-ep-arrowRightBold />
              </el-icon>
            </div>
            <el-icon v-if="loading" :size="24" class="loading-icon">
              <i-ep-loading />
            </el-icon>
          </div>
        </div>
        <div
          v-if="showAllContent && hasContent"
          class="right-content w-496 box-border relative"
        >
          <OriginalContent />
        </div>
        <div v-if="hasContent" class="middle-content flex-1">
          <div class="middle-top">
            <div class="top">
              <div class="category-left">
                <div class="category-label">附件类型</div>
                <el-select
                  v-model="fileType"
                  placeholder="请选择图片类型"
                  style="width: 240px"
                  :disabled="scanLoading"
                  @change="changeFileType"
                >
                  <el-option-group
                    v-for="group in tagList"
                    :key="group.label"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-option-group>
                </el-select>
              </div>
              <div v-if="props.patientHistoryId" class="category-left">
                <div class="category-label">保存位置</div>
                <div class="text-content">{{ props.componentLocationStr }}</div>
              </div>
            </div>
          </div>
          <div class="middle-bottom box-border px-24 py-16">
            <component
              :is="componentMap[ocrStore.annexType]"
              ref="annexTypeComRef"
            />
          </div>
        </div>
      </div>
      <div v-if="hasContent" class="bottom-btn">
        <div class="btn-group">
          <el-button type="primary" :loading="saveLoading" @click="saveInfo">
            保存
          </el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </div>
    </div>
    <OcrSaveDialog
      :save-loading="confirmSaveLoading"
      @save-data="saveMedicalRecords"
    />
    <ImageCropper
      v-if="url"
      v-model:dialog-cropper-visible="dialogCropperVisible"
      :url="url"
      :deg="deg"
    />
  </HrtDialog>
</template>

<script setup lang="tsx">
import { HrtDialog } from '@hrt/components';
import { useImgPosition } from './hooks/useImgPosition';
import OriginalContent from './OutPatientClinc/OriginalContent.vue';
import DischargeRecord from './OutPatientClinc/DischargeRecord.vue';
import HospitalizationRecord from './OutPatientClinc/HospitalizationRecord.vue';
import SurgeryRecord from './SurgeryRecord/index.vue';
import Electrocardiogram from './Electrocardiogram/index.vue';
import ImageCropper from './ImageCropper/index.vue';
import CopyTextImgBox from './CopyTextImgBox/index.vue';
import InspectionReport from './InspectionReport/InspectionReport.vue';
import OutpatientRecord from './OutPatientClinc/OutpatientRecord.vue';
import OutpatientPrescription from './OutPatientClinc/OutpatientPrescription.vue';
import OcrSaveDialog from './OcrSaveDialog/OcrSaveDialog.vue';
import { onClickOutside } from '@vueuse/core';
import { ComponentType, OcrProps } from './type';
import { useOcrScan } from '@/store/module/useOcrScan';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';
import globalBus from '@/lib/bus';
import { selectOptions } from '@/features/OcrViewer/constant';
defineOptions({ name: 'OcrViewer' });

const ocrStore = useOcrScan();

const saveOcrStore = useSaveOcrInfo();

const {
  loading,
  imgW,
  imgH,
  top,
  left,
  scale,
  deg,
  maskBox,
  handleRotate,
  onmousedownHandle,
  imgScaleHandle,
  unloadMouseWheeLevtEvent,
  addMouseWheeLevtEvent,
  dialogWidth,
  showAllContent,
  changDialogWidthByScanRes,
  isFullScreen,
  openFullScreen,
  initImgPosition,
} = useImgPosition();

const createECGComponent = (category: string) =>
  shallowRef(
    defineComponent({
      setup(_, { expose }) {
        const ecgRef = ref<InstanceType<typeof Electrocardiogram>>();
        expose({
          saveData: () => ecgRef?.value?.saveData(),
          setFormData: (data: any) => ecgRef?.value?.setFormData(data),
        });
        return () => <Electrocardiogram ref={ecgRef} category={category} />;
      },
    })
  );

const componentMap = reactive<ComponentType>({
  1: shallowRef(HospitalizationRecord), // 入院记录
  2: shallowRef(DischargeRecord), //  出院记录
  3: shallowRef(InspectionReport), // 检验
  4: shallowRef(OutpatientRecord), // 门诊记录
  5: shallowRef(SurgeryRecord), // 手术
  6: createECGComponent('ecg_12'),
  7: createECGComponent('ecg_dynamic'),
  8: createECGComponent('echocardiogram'),
  9: shallowRef(OutpatientPrescription), // 门诊处方
});

const annexTypeComRef = ref();

const hasContent = computed(() => ocrStore.hasContent);

const scanLoading = computed(() => ocrStore.loadingStatus);

const isCloseDialog = computed(() => saveOcrStore.isCloseDialog);

const url = computed(() => ocrStore.globalImgInfo.currentImgUrl.url);

// const cropperLoading = computed(() => ocrStore.cropperImgInfo.cropperLoading);
//图片旋转角度参数
const rotation = computed(() => {
  if (deg.value === 90 || deg.value === -270) {
    return 3;
  } else if (Math.abs(deg.value) === 180) {
    return 2;
  } else if (deg.value === 270 || deg.value === -90) {
    return 1;
  } else {
    return 0;
  }
});

//0 原图 1 增强图
const currentImgType = ref<number>(0);

const visible = ref<boolean>(true);

watch(isCloseDialog, newValue => {
  if (newValue) {
    closeDialog();
  }
});

watch(url, () => {
  if (url.value) {
    initImgPosition();
  }
});

const props = defineProps<OcrProps>();

provide('locatedPatientHistoryId', props.patientHistoryId);

watch(
  () => props.patientHistoryId,
  () => {
    saveOcrStore.outPatientClincInfo.currentData.patientHistoryId =
      props.patientHistoryId;
  },
  { immediate: true }
);

const emit = defineEmits(['closeDialog']);

const fileType = computed(() => ocrStore.annexType);

const componentLocation = inject('componentLocation');

const tagList = selectOptions[componentLocation as keyof typeof selectOptions];

const showScanTagBox = ref<boolean>(false);

const ocrFileTagBox = ref<HTMLElement | null>(null);

const saveLoading = ref(false);
const confirmSaveLoading = ref(false);

const changeFileType = value => {
  ocrStore.changeFileType(value);
};

onClickOutside(ocrFileTagBox, () => {
  showScanTagBox.value = false;
});
//关闭弹窗
const closeDialog = (): void => {
  clearTimeout(ocrStore.timer);
  emit('closeDialog', null);
  saveOcrStore.isCloseDialog = false;
};
//发起扫描请求
const startScanImg = (value = false): void => {
  if (ocrStore.loadingStatus) return;
  ocrStore.rotation = rotation.value;
  if (!value) {
    ocrStore.scanType = 0;
    ocrStore.startOcrScan();
  } else {
    ocrStore.scanType = 1;
    ocrStore.isReIdentify = value;
    ocrStore.getImgPoints(rotation.value);
  }
};

const changeImg = (index: number) => {
  ocrStore.changeAnotherImg(index);
};

//从对比框保存病历
const saveMedicalRecords = async () => {
  confirmSaveLoading.value = true;
  await annexTypeComRef.value!.saveData();
  confirmSaveLoading.value = false;
};

const saveInfo = async () => {
  saveLoading.value = true;
  try {
    if ([1, 2, 4, 9].includes(ocrStore.annexType)) {
      //如果是保存到已有记录
      if (saveOcrStore.outPatientClincInfo.currentData.patientHistoryId) {
        const res = await annexTypeComRef.value!.saveData(false);
        if (!res) return;
        //更新当前对比数据参数
        const data = annexTypeComRef.value!.getSaveData();
        saveOcrStore.outPatientClincInfo.newData = data;
        saveOcrStore.saveCurrentData(
          ocrStore.annexType,
          data.key,
          data.source_type
        );
      } else {
        await annexTypeComRef.value!.saveData();
      }
    } else {
      await annexTypeComRef.value!.saveData();
    }
  } catch (error) {
    console.log('$debug: error', error);
  } finally {
    saveLoading.value = false;
  }
};

const changeImgType = (type: number) => {
  currentImgType.value = type;
  initImgPosition();
};

//图片增强识别
const dialogCropperVisible = computed(
  () => ocrStore.cropperImgInfo.showDialogCropper
);
// 这儿里面之前计算的是没有增强的url，没有增强的url之前有个计算属性，因此这儿改了一下
const imageEnhancedUrl = computed(
  () => ocrStore.cropperImgInfo.imageEnhancedUrl
);

watch(hasContent, newValue => {
  if (newValue) {
    //识别成功角度回正
    deg.value = 0;
    handleRotate(0);
    changDialogWidthByScanRes();
    //识别成功,默认展示增强图
    currentImgType.value = 1;
  }
});

onMounted(() => {
  addMouseWheeLevtEvent();
  initImgPosition();
  changDialogWidthByScanRes();
  globalBus.on('close-ocr-dialog', () => {
    closeDialog();
  });
});
onUnmounted(() => {
  unloadMouseWheeLevtEvent();
});
</script>
<style scoped lang="less">
.bottom-btn {
  background-color: white;
  padding: 24px 0;
  box-sizing: border-box;
  border-top: 1px solid #e9e8eb;
  height: 80px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    margin-right: 24px;
  }
}

.btn {
  box-sizing: border-box;
  padding: 6px 24px;
  border-radius: 2px;
  font-size: 14px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  cursor: pointer;
  user-select: none;
}
.confirm {
  color: #ffffff;
  background-color: #0a73e4;
}
.cancel {
  color: #606266;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  margin-left: 8px;
}
</style>
<style lang="less">
.ocr-dialog {
  font-family:
    PingFangSC,
    PingFang SC;
  border-radius: 8px;
  padding: 0 !important;
  // min-width: 1500px;
  .el-overlay-dialog {
    pointer-events: none;
  }
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    pointer-events: auto !important;
  }
  .el-dialog__header {
    padding: 0 !important;
    margin: 0;
    border-bottom: 1px solid #ebeef5;
  }
  .el-dialog__body {
    padding: 0;
    height: 736px;
    .content-box {
      width: 100%;
      height: 100%;
      .top-content {
        overflow: hidden;
        .left-content {
          position: relative;
          .ocrbtn-box {
            display: flex;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 16px 0;
            .left {
              display: flex;
              align-items: center;
              margin-left: 16px;
              color: #0a73e4;
              position: relative;
              .scan-btn-box {
                display: flex;
                align-items: center;
                cursor: pointer !important;
                img {
                  width: 16px;
                  height: 16px;
                  margin-right: 4px;
                }
              }
            }
            .right {
              display: flex;
              align-items: center;
              .img-box {
                display: flex;
                justify-content: space-between;
                border-right: 1px solid #e9e8eb;
                box-sizing: border-box;
                padding: 0 24px;
                img {
                  width: 14px;
                  height: 14px;
                  cursor: pointer !important;
                }
                img:not(:first-child) {
                  margin-left: 16px;
                }
              }
              .img-box:last-child {
                border: none;
              }
              .img-box:first-child {
                padding-left: 0;
              }
            }
          }
          .imgscan-box {
            position: relative;
            overflow: hidden;
            background-color: white;
            img {
              position: absolute;
              cursor: pointer;
            }
            .ocrEnhanceImgWrapper {
              position: absolute;
              cursor: pointer;
            }
            .scan-contain {
              position: absolute;
              left: 0;
              right: 0;
              height: 100%;
              width: 100%;
              z-index: 2000;
              background: linear-gradient(
                180deg,
                rgba(0, 255, 51, 0) 60%,
                #1989fa 250%
              );
              border-bottom: 2px solid #03a9f4;
              &.active {
                animation: moveScan 3s ease-in-out infinite;
              }
            }
          }
          .pre-img {
            left: 24px;
            top: 50%;
          }
          .next-img {
            right: 24px;
            top: 50%;
          }
          .loading-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            color: #0a73e4;
            animation: spin 1s infinite linear;
          }
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            } /* 初始状态，角度为0度 */
            100% {
              transform: rotate(360deg);
            } /* 结束状态，角度为360度（完全旋转一周）*/
          }
          .img-icon {
            width: 28px;
            height: 28px;
            background: #ffffff;
            box-shadow: 0px 2px 8px 0px rgba(200, 201, 204, 0.5);
            border-radius: 50%;
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-weight: bold;
          }
          .img-icon:hover {
            color: #0a73e4;
          }
        }
        .middle-content {
          border-left: 1px solid #e9e8eb;
          min-width: 704px;
          height: 100%;
          .middle-top {
            padding: 24px 16px 16px 24px;
            box-sizing: border-box;
            height: 14%;
            .top {
              display: flex;
              justify-content: space-between;
              font-family:
                PingFangSC-Regular,
                PingFang SC;
              .category-left {
                .text-content {
                  width: 240px;
                  box-sizing: border-box;
                  padding: 4px 12px;
                  border-radius: 2px;
                  border: 1px solid rgb(229, 231, 235);
                  font-size: 14px;
                  color: #3a4762;
                  height: 32px;
                  background: #f7f8fa;
                }
                .category-label {
                  font-size: 14px;
                  font-weight: bold;
                  color: #111111;
                  margin-bottom: 8px;
                }
              }
              .category-right {
              }
            }
            .bottom {
              display: flex;
              align-items: center;
              margin-top: 8px;
            }
          }
          .middle-bottom {
            background: #f5f6f8;
            overflow: auto;
            height: 86%;
          }
        }
        .right-content {
          border-left: 1px solid #e9e8eb;
        }
      }
    }
  }
}
@keyframes moveScan {
  from {
    height: 0;
  }
  to {
    height: 100%;
  }
}

:has(> .el-overlay-dialog .ocr-dialog) {
  pointer-events: none !important;
}
.type-btn {
  display: flex;
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  div {
    cursor: pointer;
    margin-left: 12px;
  }
  .activeColor {
    color: #0a73e4;
  }
}
</style>
