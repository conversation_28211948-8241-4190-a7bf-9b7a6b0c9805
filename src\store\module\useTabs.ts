import { defineStore } from 'pinia';
import { getUuid } from '@/utils';
import STORE_NAMES from '@/constant/storeNames';
import useGlobal from './useGlobal';
import { Component } from 'vue';
import Home from '@/pages/Workbench/Main/Home/index.vue';
import EmptyTab from '@/components/EmptyTab/index.vue';
import { debounce } from 'lodash-es';

export interface IMainTabItem {
  title: string;
  code: number;
  key: string;
  roles: number[];
}
const defaultGroup = 'patient_info';
const limit = 21;
const limitText = (n: number) =>
  `新开标签数量不能超过 ${n - 1} 个，点击确定将会强制删除第二个`;
const deleteWarningText = '尚有内容未保存，退出后将丢失，是否确认退出？';
interface IPatientTabItem {
  /**自动生成唯一 Id */
  id: string;
  /**用于 tab 大类分组 */
  group?: string;
  /**vue 组件绑定 key, 用于组件刷新 */
  renderKey: string;
  /**标签类型，  edit标签需要做出提示 */
  type?: 'edit' | 'view';
  /**是否能缓存*/
  disableCache?: boolean;
  /**tab 标题 */
  name: string;
  /**tab 需要渲染的组件 */
  component: Component;
  /**tab刷新方式 new: 刷新  reuse:不刷新 */
  mode?: 'new' | 'reuse';
  /**tab 标识符，不传入则根据 name 判断是否同一个 tab */
  key?: string;
  /**是否能关闭 */
  closeable?: boolean;
  /**组件内传参数据 */
  data?: Record<string, any>;
  /**1: 运动方案  2： 患者信息 3：患者画像 4：管理情况 5：图片档案 6: 数字画像 */
  mainTabCode?: 1 | 2 | 3 | 4 | 5 | 6;
  changeMainTab?: boolean;
  /** 通用标识字段， 使用 || 作为分隔符，example:   aaaa||bbbb  */
  commonSign?: string;
}

export type IPatientTabAddItem = Omit<IPatientTabItem, 'id' | 'renderKey'>;

interface IState {
  mainTabs: IMainTabItem[];
  /**中间内容区右侧当前选中 tab */
  mainActiveTab: number;
  /**患者信息当前选中 tab Object */
  patientActiveTabMap: Record<string, string>;
  /**患者信息中 tab   */
  tabsMap: {
    [key: string]: IPatientTabItem[];
  };
  /** 是否展示提醒 dialog */
  remindVisible: boolean;
  /** 提醒文案 */
  remindText: string;
  /** 提醒类型 */
  remindType: 'add' | 'delete';
  /** 暂存数据 */
  temp: IPatientTabItem | null;
  /** 暂存当前标签 id */
  tempId: string;
  remindGroup: string;
}
export interface ITabProps
  extends Pick<IPatientTabItem, 'data' | 'commonSign'> {
  deleteTabItem: () => void;
  updateTabItem: (data: any) => void;
}
const defaultTabName = '_defaultTab';
const defaultTab = [
  {
    id: getUuid(),
    name: defaultTabName,
    group: 'all',
    renderKey: getUuid(),
    component: markRaw(EmptyTab),
    closeable: false,
  },
];
const patientHome = {
  id: getUuid(),
  name: '首页',
  key: 'patient_info',
  group: defaultGroup,
  renderKey: getUuid(),
  component: markRaw(Home),
  closeable: false,
  data: {},
};
const defaultMainTabs: IMainTabItem[] = [
  { title: '运动方案', code: 1, key: '', roles: [3] },
  { title: '患者主页', code: 2, key: '', roles: [1, 2, 3] },
  { title: '患者分析', code: 3, key: '', roles: [1, 2, 3] },
  // { title: '数字画像', code: 6, key: '', roles: [1, 2, 3] },
  { title: '管理情况', code: 4, key: '', roles: [1, 2, 3] },
  { title: '图片档案', code: 5, key: '', roles: [1, 2, 3] },
];

const setActiveIdDebounce = debounce(
  (context: any, group: string, id: string) => {
    context.patientActiveTabMap[group] = id;
  },
  20
);
const globalData = useGlobal();
const useTabs = defineStore(STORE_NAMES.PATIENT_TABS, {
  state: (): IState => ({
    mainTabs: defaultMainTabs,
    mainActiveTab: 0,
    patientActiveTabMap: {},
    tabsMap: {},
    temp: null,
    tempId: '',
    remindType: 'add',
    remindVisible: false,
    remindText: '',
    remindGroup: '',
  }),
  actions: {
    clearCache() {
      const curTab = this.tabsMap[globalData.userId + ''] ?? [];
      this.tabsMap[globalData.userId + ''] = curTab.filter(
        v => !v.disableCache
      );
    },
    getPatientTabs() {
      return this.tabsMap[globalData.userId + ''];
    },
    getCurItemById(id: string) {
      const curTabs = this.getPatientTabs();
      const index = curTabs.findIndex(v => v.id === id);
      const item = curTabs[index];
      return { index, item };
    },
    deleteTab(id: string, group: string) {
      const { index, item } = this.getCurItemById(id);
      if (item.type === 'edit') {
        this.tempId = id;
        this.remindVisible = true;
        this.remindType = 'delete';
        this.remindText = deleteWarningText;
        this.remindGroup = group;
      } else {
        this.deleteByIndex(index, group);
      }
    },
    deleteByIndex(index: number, group: string) {
      const curTabs = this.getPatientTabs();
      const curItem = curTabs[index];
      const curGroupTab = this.getCurrentPatientTabs(group);
      const curGroupIndex = curGroupTab?.findIndex(v => v.id === curItem.id);
      curTabs.splice(index, 1);
      const curActGroupTab = curGroupTab[curGroupIndex - 1];
      this.patientActiveTabMap[group] =
        curActGroupTab?.name === defaultTabName && curGroupTab[index]
          ? curGroupTab[index].id
          : curActGroupTab?.id || curGroupTab[1]?.id || '';
    },
    updateTab(
      id: string,
      data: Partial<
        Pick<IPatientTabItem, 'name' | 'type' | 'commonSign' | 'data'>
      >
    ) {
      const { item } = this.getCurItemById(id);
      if (data.name) item.name = data.name;
      if (data.type) item.type = data.type;
      if (data.data) item.data = { ...item.data, ...data.data };
      item.commonSign = data.commonSign;
    },
    refreshTab(id: string) {
      const { item } = this.getCurItemById(id);
      if (item) {
        item.renderKey = getUuid();
      }
    },
    remindConfirm() {
      if (this.remindType === 'add') {
        this.forceAdd();
      } else {
        const { index } = this.getCurItemById(this.tempId);
        this.deleteByIndex(index, this.remindGroup);
        this.remindVisible = false;
        this.remindGroup = '';
      }
    },
    forceAdd() {
      const userId = globalData.userId;
      const allTabs = this.getPatientTabs();
      const newTab = this.temp;
      const curTabs = this.getCurGroupPatientTabs(allTabs, this.remindGroup);
      const firstClosedItem = curTabs.find(v => v.closeable);
      const index = allTabs.findIndex(v => v.id === firstClosedItem!.id);
      if (!newTab) return;
      if (index !== -1) {
        allTabs.splice(index, 1);
      }
      this.tabsMap[userId + ''].push(newTab);
      this.patientActiveTabMap[this.remindGroup] = newTab?.id;
      this.remindVisible = false;
      this.temp = null;
      this.remindGroup = '';
    },
    addTab(item: IPatientTabAddItem, curLimit = limit) {
      console.log(
        '【tab管理】：新开 name key data',
        item.name,
        item.key,
        item.data,
        item
      );
      const userId = globalData.userId;
      if (!userId) return;
      item.group = item.group ?? defaultGroup;
      item.mainTabCode = item.mainTabCode ?? 2;
      item.closeable = item.closeable ?? true;
      item.changeMainTab = item.changeMainTab ?? true;
      if (item.changeMainTab) {
        this.mainActiveTab = item.mainTabCode!;
      }
      const newTab: IPatientTabItem = {
        id: getUuid(),
        renderKey: getUuid(),
        mode: 'reuse',
        ...item,
        component: markRaw(item.component),
      };
      const curTabs = this.getPatientTabs();
      let activeId = newTab.id;
      if (curTabs) {
        const curItem = curTabs.find((v: IPatientTabItem) => {
          const isSameGruup = v.group === item.group;
          if (v.key) {
            return isSameGruup && v.key === item.key;
          }
          return isSameGruup && v.name === item.name;
        });
        if (curItem) {
          curItem.data = newTab.data;
          activeId = curItem.id;
          if (newTab.mode === 'new') {
            this.refreshTab(activeId);
          }
        } else {
          if (
            this.getCurGroupPatientTabs(curTabs, newTab.group).length >=
            curLimit
          ) {
            this.remindVisible = true;
            this.temp = newTab;
            this.remindType = 'add';
            this.remindText = limitText(curLimit);
            this.remindGroup = newTab.group!;
            return;
          } else {
            this.tabsMap[userId].push(newTab);
          }
        }
      } else {
        this.tabsMap[userId] = [...defaultTab];
        if (!newTab.group || newTab.group === defaultGroup) {
          this.tabsMap[userId].push(patientHome);
        }
        this.tabsMap[userId].push(newTab);
        activeId = newTab.id;
      }
      this.setActiveId(newTab.group!, activeId);
    },
    setActiveId(group: string, id: string) {
      setActiveIdDebounce(this, group, id);
    },
    getCurGroupPatientTabs(list: IPatientTabItem[] = [], group: string = '') {
      return list.filter(v => {
        if (v.group === 'all') return true;
        if (group === defaultGroup) {
          return !v.group || v.group === defaultGroup;
        }
        return v.group === group;
      });
    },
    getCurrentPatientTabs(group: string) {
      const allTabs = this.getPatientTabs();
      const curTabs = this.getCurGroupPatientTabs(allTabs, group);
      if (curTabs?.length) {
        // if (!this.patientActiveTabMap[group]) {
        //   this.setActiveId(group, curTabs?.[1]?.id);
        // }
        return curTabs;
      } else {
        const userId = globalData.userId;
        if (!userId) return [];
        this.tabsMap[userId] = [...defaultTab];
        if (group === defaultGroup) {
          this.tabsMap[userId].push(patientHome);
          this.patientActiveTabMap[group] = patientHome.id;
        }
        return this.tabsMap[userId];
      }
    },
    getMainTabs() {
      const role = globalData.currentRole;
      // 通过角色权限返回对应tab
      return this.mainTabs.filter(item => item.roles.includes(role));
    },
    changeTabPositions(source: number, target: number, group: string) {
      const allTabs = this.getPatientTabs();
      const curTabs = this.getCurrentPatientTabs(group);
      const flag = source < target ? -1 : 0;
      const originSourceIndex = allTabs.findIndex(
        v => v.id === curTabs[source].id
      );
      const isBorder = target >= curTabs.length;
      const originTargetIndex =
        allTabs.findIndex(
          v => v.id === curTabs[Math.min(target, curTabs.length - 1)].id
        ) + (isBorder ? 1 : 0);
      const item = curTabs.slice(source, source + 1)[0];
      allTabs.splice(originSourceIndex, 1);
      allTabs.splice(originTargetIndex + flag, 0, { ...item });
    },
  },
});

export default useTabs;
