<template>
  <div class="add-todo">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="item flex"
      :class="item.type === 4 ? 'mb-8' : 'mb-12'"
    >
      <div
        class="title w-92 mr-16"
        :style="{ marginTop: item.type === 1 ? '' : '5px' }"
      >
        <span v-if="item.isRequired" class="required">*</span>{{ item.title }}
      </div>
      <div class="content">
        <div v-if="item.type === 1" class="user-name">{{ item.value }}</div>
        <div v-if="item.mode === 2 && item.type === 2">
          <div v-for="(v, i) in item.value" :key="i" class="multiple">
            <el-date-picker
              v-model="item.value[i]"
              type="datetime"
              placeholder="请选择日期"
              size="default"
              :disabled-date="disabledDateFn"
              :clearable="false"
              value-format="YYYY-MM-DD HH:mm"
              format="YYYY-MM-DD HH:mm"
              :default-time="defaultTime"
            />
            <div v-if="i !== 0" class="title innerTitle">
              <span v-if="item.isRequired" class="required">*</span
              >{{ item.title }}{{ i + 1 }}
            </div>
            <img :src="changeTime" alt="" class="w-13 h-13 change-time-icon" />
            <span
              v-if="item.value.length < 10"
              @click="() => addRemindTime(item)"
            >
              <img :src="addIcon" class="w-14 h-14" />
            </span>
            <span
              v-if="item.value.length > 1"
              @click="() => deleteRemindTime(item, i)"
            >
              <el-icon>
                <i-ep-delete />
              </el-icon>
            </span>
          </div>
        </div>
        <el-date-picker
          v-if="item.mode !== 2 && item.type === 2"
          v-model="item.value"
          type="datetime"
          placeholder="请选择日期"
          size="default"
          :disabled-date="disabledDateFn"
          :clearable="false"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm:ss"
        />
        <el-input
          v-if="item.type === 3"
          v-model="item.value"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          placeholder="请输入内容"
          resize="none"
          maxlength="300"
          show-word-limit
        />
        <el-radio-group
          v-if="item.type === 4"
          v-model="item.value"
          class="ml-4"
        >
          <el-radio label="1">同步</el-radio>
          <el-radio label="0">不同步</el-radio>
        </el-radio-group>
        <div v-if="item.type === 5" class="tips">{{ item.value }}</div>
        <img
          v-if="item.mode !== 2 && item.type === 2"
          :src="changeTime"
          alt=""
          class="w-13 h-13 change-time-icon"
        />
      </div>
    </div>
    <Btns :confirm-loading="confirmLoading" @submit="submit" @cancel="cancel" />
  </div>
</template>
<script setup lang="ts">
import Btns from './Btns.vue';
import changeTime from '@/assets/imgs/callCenter/change-time.png';
import { addTodoApi } from '@/api/todo';
import { getUserRoles, dialogTip } from '../index';
import useGlobal from '@/store/module/useGlobal';
import dayjs from 'dayjs';
import addIcon from '@/assets/imgs/callCenter/add.png';
import useUserStore from '@/store/module/useUserStore';

let useGlobalInfo = useGlobal();
const userStore = useUserStore();
interface listInfo {
  title: string;
  value: string | string[];
  //   type  1-文本模式  2-时间选择 3-输入框 4-单选  5-提示文字
  type: number;
  // 1 单个， 2支持添加多个
  mode?: number;
  isRequired: boolean;
  flag: string;
}
const gap = 5 * 60 * 1000;
const defaultTime = new Date(2000, 1, 1, 9, 0, 0);
const confirmLoading = ref(false);
let list = ref<listInfo[]>([
  {
    title: '患者',
    value: useGlobalInfo.userInfo.patientName || '--',
    type: 1,
    isRequired: false,
    flag: 'name',
  },
  {
    title: '提醒时间',
    value: [''],
    type: 2,
    mode: 2,
    isRequired: true,
    flag: 'remindTimes',
  },
  {
    title: '事件描述',
    value: '',
    type: 3,
    isRequired: true,
    flag: 'content',
  },
  {
    title: '待办期限',
    value: '',
    type: 2,
    isRequired: true,
    flag: 'term',
  },
  {
    title: '同步推送患者',
    value: '0',
    type: 4,
    isRequired: true,
    flag: 'mqPatient',
  },
  {
    title: '',
    value: '勾选后，将在待办提醒时间给患者同步推送一条公众号提醒',
    type: 5,
    isRequired: false,
    flag: 'push',
  },
]);

const formatTimeDuration = times => {
  const sortTimes = times
    .filter(v => !!v)
    .map((v, i) => {
      return {
        time: dayjs(v).valueOf(),
        no: i + 1,
      };
    })
    .sort((a, b) => a.time - b.time);
  return sortTimes;
};
const getText = i => {
  return `提醒时间${i === 1 ? '' : i}`;
};
const checkTimes = data => {
  let errorMsg = '';
  for (let i = 1; i < data.length; i++) {
    if (data[i].time - data[i - 1].time < gap) {
      errorMsg = `${getText(data[i].no)}和${getText(data[i - 1].no)}间隔不能小于5分钟`;
      break;
    }
  }
  return errorMsg;
};
const addRemindTime = item => {
  item.value.push('');
};
const deleteRemindTime = (item, index) => {
  item.value.splice(index, 1);
};
// 限制日期选择时间
const disabledDateFn = (time: { getTime: () => number }) => {
  if (time.getTime() < Date.now() - 8.64e7) {
    return true;
  } else {
    return false;
  }
};

// 新增待办

let add = () => {
  let params = {
    patientId: useGlobalInfo.userId,
    headId: userStore.accountId,
    headRole: getUserRoles(),
    remindTimes: [],
    term: 0,
    content: '',
    mqPatient: 0,
    type: 'CUSTOM_BACKLOG',
  };
  list.value.forEach(item => {
    for (let key in params) {
      if (item.flag === key) {
        if (key === 'term') {
          params[key] = Date.parse(item.value as string);
        } else if (key === 'remindTimes') {
          params[key] = (item.value as string[]).map(v => Date.parse(v)) as any;
        } else {
          params[key] = item.value;
        }
      }
    }
  });
  confirmLoading.value = true;
  addTodoApi(params)
    .then(res => {
      let { code, message } = res;
      if (code === 'E000000') {
        dialogTip('新增成功！', 'success');
        cancel();
      } else {
        dialogTip(message, 'success');
      }
    })
    .finally(() => {
      confirmLoading.value = false;
    });
};

const emit = defineEmits(['close']);
let submit = () => {
  let todoTime = 0;
  let tipTime = [];

  for (let i = 0; i < list.value.length; i++) {
    let item = list.value[i];
    if (item.isRequired && item.mode !== 2 && !item.value) {
      let title = item.type === 3 ? '填写' : '选择';
      return ElMessage({
        message: '请' + title + item.title,
        type: 'warning',
      });
    }
    if (item.isRequired && item.mode === 2) {
      for (let i = 0; i < item.value.length; i++) {
        if (!item.value[i]) {
          ElMessage.warning(`请选择${item.title}${i ? i + 1 : ''}`);
          return;
        }
      }
    }
    if (item.type === 2 && item.title === '提醒时间') {
      tipTime = item.value as any;
      const res = formatTimeDuration(item.value);
      const error = checkTimes(res);
      if (error) {
        ElMessage.warning(error);
        return;
      }
    }
    if (item.type === 2 && item.title === '待办期限') {
      todoTime = Date.parse(item.value as string);
      if (!todoTime) return;
      for (let i = 0; i < tipTime.length; i++) {
        if (Date.parse(tipTime[i]) > todoTime) {
          return ElMessage.warning(`待办期限必须大于${getText(i + 1)}！`);
        }
      }
    }
  }
  add();
};
let cancel = () => {
  emit('close');
};
</script>
<style scoped lang="less">
.add-todo {
  width: 100%;
  padding-left: 30px;
  padding-right: 42px;
  .multiple {
    display: flex;
    position: relative;
    .innerTitle {
      text-align: left;
      position: absolute;
      left: -80px;
      top: 4px;
    }
    &:not(:last-child) {
      margin-bottom: 16px;
    }
    margin-bottom: 8px;
    :deep(.el-date-editor) {
      width: calc(100% - 80px) !important;
      height: 32px;
    }
    .change-time-icon {
      position: absolute;
      top: 9px;
      right: 90px !important;
    }
    > span {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      width: 32px;
      justify-content: center;
      margin: 0 4px;
    }
  }
  .item {
    .title {
      font-size: 14px;
      font-weight: bold;
      color: #101b25;
      text-align: right;
      .required {
        font-size: 14px;
        font-weight: bold;
        color: #e63746;
      }
    }
    :deep(.content) {
      flex: 1;
      position: relative;
      .change-time-icon {
        position: absolute;
        top: 9px;
        right: 10px;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
        height: 32px;
      }
      .el-input__prefix {
        display: none;
      }
      .el-radio-group {
        .el-radio__inner {
          width: 16px;
          height: 16px;
        }
        .is-checked {
          .el-radio__label {
            color: #3a4762;
          }
        }
        .el-radio__input.is-checked .el-radio__inner {
          background: #2e6be6;
          border-color: #2e6be6;
        }
      }
      .user-name {
        font-size: 14px;
        color: #3a4762;
      }
      .tips {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
}
</style>
