<template>
  <el-tabs
    v-if="activeItem"
    :model-value="activeItem.id"
    @tab-change="tabChange"
  >
    <el-tab-pane
      v-for="item in list"
      :key="item.id"
      :name="item.id"
      :label="item.name"
      :lazy="true"
    />
    <RiskDescription v-if="riskDesc" :info="riskDesc" />
  </el-tabs>
</template>

<script setup lang="ts">
import { IAnaSubIndicItem } from '../utils';
import useGlobal from '@/store/module/useGlobal';
import RiskDescription from './RiskDescription.vue';
import { TabPaneName } from 'element-plus';
import useAbnormalContent, {
  subIndexGetAbnormalContent,
} from './hooks/useAbnormalContent';

const {
  list = [],
  activeItem,
  templateName = '',
  pId,
} = defineProps<{
  list: IAnaSubIndicItem[];
  activeItem: IAnaSubIndicItem;
  templateName: string;
  pId: number;
}>();
const emit = defineEmits<{ (e: 'tab-change', item: IAnaSubIndicItem): void }>();
const globalData = useGlobal();
const { riskDesc, getRiskDesc } = useAbnormalContent();
function tabChange(id: TabPaneName) {
  emit('tab-change', list.find(item => item.id === id)!);
}

watch(
  () => activeItem,
  ({ id }) => {
    if (id) {
      const riskItem = subIndexGetAbnormalContent.find(
        ({ checkType, indexType }) =>
          checkType === Number(pId) && indexType === Number(id)
      );
      if (riskItem) {
        getRiskDesc(
          {
            patientId: globalData.userId!,
            checkType: pId,
            indexType: Number(id),
            templateName: templateName,
          },
          {
            frequency: riskItem.frequency || '',
          }
        );
      } else if (riskDesc.value) {
        riskDesc.value = undefined;
      }
    } else if (riskDesc.value) {
      riskDesc.value = undefined;
    }
  },
  { immediate: true }
);
</script>
