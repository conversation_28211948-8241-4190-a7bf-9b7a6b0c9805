<template>
  <div class="bg-white w-full h-full py-2xl px-sm flex flex-col">
    <div class="header">
      患者数字画像
      <span class="sub-title ml-xs">根据患者近360天监测数据绘制</span>
    </div>
    <div v-loading="loading" class="flex-1">
      <G6Force v-if="graphData" :data="graphData" />
      <el-empty v-else description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Graph',
});
import G6Force from '@/components/G6Force/index.vue';
import { getLatestRecommend } from '@/api/recommendConversation';
import useGlobal from '@/store/module/useGlobal';

const graphData = ref();
const globalData = useGlobal();
const loading = ref(false);

watchEffect(async () => {
  if (!globalData.userId) return;
  try {
    loading.value = true;
    const data = await getLatestRecommend({ patientId: globalData.userId });
    if (data) {
      const { leafs, roots, relations, nodes } = data;

      const curRootIds =
        roots?.filter(id => nodes?.find(n => n.id === id)?.type === 0) || [];

      const curRelations = relations?.filter(relation =>
        curRootIds?.includes(relation.source!)
      );

      // 根节点关联一级节点id
      const curNodeIds = Array.from([
        ...(curRelations?.map(re => re.target) || []),
        ...curRootIds,
      ]);

      const curNodes = nodes?.filter(n => curNodeIds.includes(n.id!));

      const curLeafs = leafs?.filter(id => curNodeIds?.includes(id));

      graphData.value = {
        roots: curRootIds,
        leafs: curLeafs,
        nodes: curNodes,
        relations: curRelations,
      };
    }
  } catch (error) {
    console.log('$debugZ：error', error);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped lang="less">
.header {
  font-weight: bold;
  font-size: 14px;
  color: #3a4762;
  .sub-title {
    font-size: 12px;
    color: #7a8599;
  }
}
</style>
