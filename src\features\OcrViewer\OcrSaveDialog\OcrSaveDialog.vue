<template>
  <DataComparison
    v-model:visible="visible"
    class="ocr-diff-wrapper"
    is-need-callback
    :title-info="{
      dialogTitle: '是否覆盖更新当前内容？',
      oldTitle: '老数据',
      newTitle: '新数据',
    }"
    :tips="''"
    :save-loading="saveLoading"
    @handle-callback="confirmSave"
  >
    <template #old>
      <SingleDynamicForm
        v-model="originData"
        class="mt-12"
        mode="view"
        :category="category"
        hidden-file-upload
        block-label
        :exclude-validate-keys="excludeKeys"
      />
    </template>
    <template #new>
      <SingleDynamicForm
        v-model="currentData"
        class="mt-12"
        mode="view"
        :category="category"
        hidden-file-upload
        block-label
        :exclude-validate-keys="excludeKeys"
      />
    </template>
    <template #footer>''</template>
  </DataComparison>
</template>

<script setup lang="ts">
import DataComparison from '@/features/DataComparison/index.vue';
import SingleDynamicForm from '@/features/SingleDynamicForm/index.vue';
import { useSaveOcrInfo } from '@/store/module/useSaveOcrInfo';

interface IProps {
  saveLoading: boolean;
}
defineProps<IProps>();
const emit = defineEmits(['saveData']);
const saveOcrSaveStore = useSaveOcrInfo();
const visible = ref(false);

watch(
  () => saveOcrSaveStore.showConfirmDialog,
  val => {
    if (val) {
      visible.value = val;
    }
  }
);
watch(visible, val => {
  if (!val) {
    saveOcrSaveStore.showConfirmDialog = false;
  }
});

const currentData = computed(
  () => saveOcrSaveStore.outPatientClincInfo.newData.data
);
const category = computed(
  () => saveOcrSaveStore.outPatientClincInfo.newData.key
);
const excludeKeys = computed(
  () => saveOcrSaveStore.outPatientClincInfo.newData.excludeKeys ?? []
);
const originData = computed(
  () => saveOcrSaveStore.outPatientClincInfo.originData
);

const confirmSave = () => {
  // saveOcrSaveStore.saveUpdatePatientReq(annexType.value);
  saveOcrSaveStore.showConfirmDialog = false;
  emit('saveData');
};
</script>

<style lang="less">
.ocr-diff-wrapper {
  color: red;
  .el-dialog__body {
    > div {
      padding: 12px 0 0 0;
      .content {
        padding-left: 12px;
      }
    }
  }
}
</style>
