<template>
  <AthleticStatusDetail
    v-if="readOnly"
    v-model:readOnly="readOnly"
    :history-list="historyList"
  />
  <CardWrapper
    v-else
    :title="titles"
    class="athletic-status-wrapper"
    :class="{ edit: !readOnly }"
  >
    <AthleticStatusEdit
      v-model:readOnly="readOnly"
      :edit-info="editInfo"
      @change-tab-index="changeTabIndex"
    />
  </CardWrapper>
</template>

<script setup lang="ts">
import CardWrapper from '@/components/CardWrapper/index.vue';
import store from '@/store';
import AthleticStatusEdit from './AthleticStatusEdit.vue';
import AthleticStatusDetail from './AthleticStatusDetail.vue';
import { getRehabManageMotionHistory } from '@/api/exerciseRehabilitation';

const readOnly = ref(false);
const title = ref('请从以下不确定条件中判断患者是否适合运动管理：');
const globalData = store.useGlobal();
const historyList = ref([]);
const editInfo = ref();
const titles = computed(() => {
  return readOnly.value ? '评估详情' : title.value;
});
const emit = defineEmits(['changeTabIndex']);
const getHistory = () => {
  readOnly.value = !!globalData.manageStatus && globalData.manageStatus != 1;
  if (!globalData.userId || !readOnly.value) {
    historyList.value = [];
    editInfo.value = null;
    return false;
  }
  getRehabManageMotionHistory({ patientId: globalData.userId })
    .then(res => {
      res.reverse();
      historyList.value = res;
      editInfo.value = res[0];
      readOnly.value = res?.length > 0;
    })
    .catch(() => {
      historyList.value = [];
      editInfo.value = null;
    });
};
const changeTabIndex = index => {
  emit('changeTabIndex', index);
};
onMounted(() => {
  getHistory();
});
watch(
  () => globalData.userInfo,
  () => {
    title.value = `请从以下不确定条件中判断患者【${
      globalData.userInfo.patientName || '--'
    }】是否适合运动管理`;
  },
  {
    immediate: true,
  }
);
watch(
  () => globalData.manageStatus,
  () => {
    getHistory();
  },
  {
    immediate: true,
  }
);
watch(
  () => globalData.userId,
  () => {
    getHistory();
  },
  {
    immediate: true,
  }
);

watch(
  () => readOnly.value,
  val => {
    if (val) {
      getHistory();
    }
  },
  {
    immediate: true,
  }
);
</script>
<script lang="ts">
export default {
  name: 'AthleticStatus',
};
</script>
<style scoped lang="less">
.athletic-status-wrapper {
  margin-bottom: 8px;
  .line {
    border-bottom: 1px solid #e9e8eb;
  }
  :deep(.edit-box) {
    padding: 0 16px;
    .label-til {
      padding: 16px 0 12px 0;
      color: #101b25;
      font-weight: bold;
      > span {
        color: #e63746;
        font-weight: normal;
        margin-left: 8px;
      }
    }
    .el-checkbox-group {
      .el-checkbox {
        height: 20px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
      }
      .el-checkbox__input {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        border-radius: 2px;
      }
      .el-checkbox__label {
        padding-left: 8px;
        color: #3a4762;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        left: 5px;
        border-width: 2px;
      }
    }
    .el-textarea__inner {
      width: 100%;
      min-height: 64px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #b8becc;
      box-shadow: none;
      color: #3a4762;
      &::placeholder {
        color: #c8c9cc;
      }
    }
  }
  .evaluation-recommendations {
    width: 100%;
    min-height: 78px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(186, 200, 212, 0.5);
    border-radius: 0 0 6px 6px;
    box-sizing: border-box;
    .til {
      color: #101b25;

      span.one {
        color: #e63746;
      }
      span.two {
        color: #e37221;
      }
    }
    .result {
      color: #7a8599;
    }
  }
}
</style>
<style lang="less">
.athletic-status-wrapper {
  > div {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
}
.el-tabs__content {
}
.tips-dialog {
  .tips {
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    display: flex;
    align-items: center;
    padding-top: 40px;
    padding-left: 24px;

    .untieImg {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
  .desc {
    padding: 12px 25px 0 50px;
    color: #7a8599;
  }
  .btn-box {
    border: none;
    padding-top: 0;
  }
}
</style>
