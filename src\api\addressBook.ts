import { http } from '@/network';

// 查询通讯录
export function patientList(params: any) {
  return http.post({
    url: '/api/address/patient/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 新增编辑通讯录
export function updateApi(params: any) {
  return http.post({
    url: '/api/address/update',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询通话记录列表
export function addressCallListApi(params: any) {
  return http.post({
    url: '/api/address/call/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询坐席电话列表
export function queryPatientNameApi(params: any) {
  return http.post({
    url: '/api/address/phone/list',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 记录呼叫事件
export function queryCallItemApi(params: any) {
  return http.post({
    url: '/api/address/call/item',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询通话记录详情
export function queryCallDetailsApi(params: any) {
  return http.post({
    url: '/api/address/call/details',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 通过手机号返回来电姓名
export function queryAddressPhoneNameApi(params: any) {
  return http.post({
    url: '/api/address/seats/phone',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 通过姓名和手机号搜索符合要求的患者及患者家属
export function queryPatientRelevanceMsgApi(params: any) {
  return http.post({
    url: '/api/address/patient/keyword',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 新增实质性事务数据
export function handleRecordApi(params: any) {
  return http.post({
    url: '/api/submit/handle/record/submit',
    method: 'post',
    data: params,
    customConfig: { reductDataFormat: false },
  });
}

// 当前角色未接来电数
export function addressMissCalls() {
  return http.post({
    url: '/api/address/miss/calls',
    method: 'post',
    data: {},
  });
}

// 清除未接来电红点标记
export function cleanAddressSign(params: {
  uniqueId?: string;
  phone?: string;
}) {
  return http.post({
    url: '/api/address/sign/clean',
    method: 'post',
    data: params,
  });
}
