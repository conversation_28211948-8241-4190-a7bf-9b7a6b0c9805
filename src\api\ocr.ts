import { FormCategoryValues } from '@/constant';
import {
  IApiOcrAiIdentify,
  IApiOcrPhotoStatus,
  IApiOcrInHistoryParams,
  IApiOcrInHistory,
  IApiOcrOutHistoryParams,
  IApiOcrOutHistory,
  IApiOcrOutpatientHistoryParams,
  IApiOcrOutpatientHistory,
  IApiOcrImgCornerParams,
  IApiOcrImgCorner,
  IApiOcrImgRectifiedParams,
  IApiOcrImgRectified,
  IApiCaseHistorySegment,
  IApiOcrScanImage,
  IApiOcrReScanImageParams,
  IApiOcrReScanImage,
  IApiOcrReformatImageParams,
  IApiOcrReformatImage,
} from '@/interface/type';
import { http } from '@/network';

export interface SymptomListResultModel {
  symptomId: number;
  symptomName: string;
}

export function getSuitsList() {
  return http.post<SymptomListResultModel[]>({
    url: '/api/query/symptom/list',
    method: 'post',
  });
}

export function getDiseaseList(data: any) {
  return http.post({
    url: '/api/query/disease/list',
    method: 'post',
    data,
  });
}

export function getInHospitalRecords(data: IApiOcrInHistoryParams) {
  return http.post<IApiOcrInHistory>({
    url: '/api/ocr/in/history',
    method: 'post',
    data,
  });
}

export function getOutHospitalRecords(data: IApiOcrOutHistoryParams) {
  return http.post<IApiOcrOutHistory>({
    url: '/api/ocr/out/history',
    method: 'post',
    data,
  });
}

export function getReviewRecords(data: any) {
  return http.post({
    url: '/api/ocr/review/list',
    method: 'post',
    data,
  });
}
export function getOutPatientHospitalRecords(
  data: IApiOcrOutpatientHistoryParams
) {
  return http.post<IApiOcrOutpatientHistory>({
    url: '/api/ocr/outpatient/history',
    method: 'post',
    data,
  });
}

export function getOnlyLeaveHospital(data: any) {
  return http.post({
    url: '/api/case/history/search/leave/hospital',
    method: 'post',
    data,
  });
}

export function getOnlyBeHospitalized(data: any) {
  return http.post({
    url: '/api/case/history/search/in/hospital',
    method: 'post',
    data,
  });
}
export function getRecordData(key: string, data: any) {
  return http.get({
    url: `/api/case/history/${key}`,
    params: data,
  });
}

export function getReviewList(data: any) {
  return http.post({
    url: '/api/ocr/review/list',
    method: 'post',
    data,
  });
}

export function getClinicBeHospitalized(data: any) {
  return http.post({
    url: '/api/case/history/search/in/hospital',
    method: 'post',
    data,
  });
}
//保存入院信息
export function addOrUpdateBeHospitalized(data: any) {
  return http.post({
    url: '/api/case/history/in/hospital/info',
    method: 'post',
    data,
  });
}
export function addOrUpdateLeaveHospital(data: any) {
  return http.post({
    url: '/api/case/history/diagnosis/leave/hospital',
    method: 'post',
    data,
  });
}
interface CheckIndexListModel {
  checkType: number;
  indexType: number;
  name: string;
}
export function getCheckIndexList() {
  return http.post<CheckIndexListModel[]>({
    url: '/api/riskMa/check/index/list',
    method: 'post',
  });
}

//发起ocr识别扫描
export interface OcrScanParams {
  rotation: number;
  url: string;
}
//直接扫描图片
export function startOcrScanReq(data: OcrScanParams) {
  return http.post<IApiOcrScanImage>({
    url: '/api/ocr/scan/image',
    method: 'post',
    data,
    customConfig: {
      repeatRequestCancel: false,
    },
  });
}

//重新识别扫描

export function reOcrScanReq(data: IApiOcrReScanImageParams) {
  return http.post<IApiOcrReScanImage>({
    url: '/api/ocr/re/scan/image',
    method: 'post',
    data,
    customConfig: {
      repeatRequestCancel: false,
    },
  });
}

export function typedOcrScanReq(data: IApiOcrReformatImageParams) {
  return http.post<IApiOcrReformatImage>({
    url: '/api/ocr/reformat/image',
    method: 'post',
    data,
    customConfig: {
      repeatRequestCancel: false,
    },
  });
}

// 获取ocr图片文本区域
export function getOcrTextCoords(data) {
  return http.post({
    url: '/api/ocr/bounding-box',
    method: 'post',
    data,
  });
}

//发起ocr文字识别
export interface OcrTextParams {
  sentence: string | null;
  category: string;
}
export function ocrTextToData(data: OcrTextParams) {
  return http.post<IApiOcrAiIdentify>({
    url: '/api/ocr/ai/identify',
    method: 'post',
    data,
  });
}

type OcrPhotoStatusParams = { urls: string[] };
//查询图片状态
export function getMsgImageStatus(data: OcrPhotoStatusParams) {
  return http.post<IApiOcrPhotoStatus>({
    url: '/api/ocr/photo/status',
    method: 'post',
    data,
  });
}

//ocr保存十二导联心电图
export function ocrSaveEcg(data: any) {
  return http.post({
    url: '/api/case/history/preserve/ecg',
    method: 'post',
    data,
  });
}

//ocr保存心脏彩超
export function ocrSaveCardiac(data: any) {
  return http.post({
    url: '/api/case/history/preserve/cardiac',
    method: 'post',
    data,
  });
}

//ocr保存动态心电图
export function ocrSaveDynamic(data: any) {
  return http.post({
    url: '/api/case/history/preserve/ecg/dynamic',
    method: 'post',
    data,
  });
}

//ocr保存手术记录
export function ocrSaveSurgery(data: any) {
  return http.post({
    url: '/api/case/history/diagnosis/surgery',
    method: 'post',
    data,
  });
}

//获取图片坐标
export function getOcrImgPoint(data: IApiOcrImgCornerParams) {
  return http.post<IApiOcrImgCorner>({
    url: '/api/ocr/img/corner',
    method: 'post',
    data,
  });
}

//保存ocr检验报告
export function ocrSaveCheckIndex(data: any) {
  return http.post({
    url: '/api/case/history/batch/saveData',
    method: 'post',
    data,
  });
}

//获取ocr矫正后的图片
export function getRectifiedImgUrl(data: IApiOcrImgRectifiedParams) {
  return http.post<IApiOcrImgRectified>({
    url: '/api/ocr/img/rectified',
    method: 'post',
    data,
  });
}

export function getSegmentList() {
  return http.post<IApiCaseHistorySegment>({
    url: '/api/case/history/segment',
    method: 'post',
  });
}

/**
 * 获取ocr所有最新关键字
 * @param data
 * @returns
 */
export function getOcrAllLatestKeyword(data: {
  enCategory?: FormCategoryValues;
  enCategoryList?: FormCategoryValues[];
}): Promise<any[]> {
  return http.post({
    url: '/api/ocr/all/latest/keyword',
    method: 'post',
    data,
  });
}

// 批量ocr扫描
interface IApiOcrBatchScanImageParams {
  urlList: string[];
  subTaskId: number;
}
export function ocrBatchScanImageApi(data: IApiOcrBatchScanImageParams) {
  return http.post({
    url: '/api/ocr/batch/scan/image',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}
