<template>
  <div class="top flex items-center justify-between">
    <div class="base-msg flex items-center justify-center">
      <div class="avatar flex items-center justify-center">
        {{ userInfo?.patientName.slice(0, 1) }}
      </div>
      <div class="base-box ml-8">
        <span class="name">{{ userInfo?.patientName }}</span>
        <el-divider direction="vertical" />
        <span class="sex">{{
          userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '--'
        }}</span>
        <el-divider direction="vertical" />
        <span class="sex">{{ userInfo?.age || '--' }}岁</span>
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
import useGlobal from '@/store/module/useGlobal';
const userInfo = computed(() => useGlobal().userInfo);
onMounted(() => {});
</script>
<style scoped lang="less">
.top {
  width: 100%;
  height: 50px;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(186, 200, 212, 0.5);
  padding: 0 16px;
  position: absolute;
  top: 0;
  left: 0;
  margin-bottom: 8px;
  z-index: 1;
  .base-msg {
    .avatar {
      width: 30px;
      height: 30px;
      background: #ffffff;
      border: 1px solid #efefef;
      font-size: 16px;
      font-weight: 600;
      color: #2e6be6;
      border-radius: 50%;
    }
    .base-box {
      :deep(.el-divider--vertical) {
        height: 12px;
      }
      .name {
        font-size: 14px;
        font-weight: 600;
        color: #3a4762;
      }
      .sex {
        font-size: 14px;
        color: #7a8599;
      }
    }
  }
  .transcribe-box {
    .date {
      font-size: 14px;
      color: #3a4762;
    }
    .link {
      font-size: 14px;
      color: #2e6be6 !important;
      display: flex;
      align-items: center;
      &:hover {
        color: #2e6be6;
        opacity: 0.75;
        cursor: pointer;
      }
      &:active {
        color: #2e6be6 !important;
        opacity: 1;
      }
    }
  }
}
</style>
