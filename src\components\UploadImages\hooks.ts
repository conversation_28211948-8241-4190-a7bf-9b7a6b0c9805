import * as qiniu from 'qiniu-js';
import { UploadRequestOptions } from 'element-plus';
import { QINIU_UPLOAD_CONFIG } from '@/constant';
import { useLocalStorage } from '@vueuse/core';
import { QINIU_VOUCHER } from '@/constant/cache';
import { getQiNiuVoucher } from '@/api/common';
import loadingify from '@/utils/loadingify';

interface IQiNiuVoucher {
  // 有效期
  expiresIn?: number;
  data?: string;
}
export function useUpload() {
  const { expiresInTime, url, urlQU } = QINIU_UPLOAD_CONFIG;
  const PERSISTENT_URL = import.meta.env.MODE !== 'production' ? urlQU : url;
  const getVoucherLoading = ref(false);
  const elUploadRef = ref();
  // 选择图片档案弹框
  const chooseImgVisible = ref(false);
  const popoverVisible = ref(false);

  // 选择上传方式
  function chooseMethod(type: 1 | 2) {
    popoverVisible.value = false;
    if (type === 1) {
      elUploadRef.value.$el.querySelector('.el-upload__input')?.click();
    } else {
      chooseImgVisible.value = true;
    }
  }

  /**
   * 图片上传
   * @param options upload 所有配置项
   * */
  async function handleHttpUpload(options: UploadRequestOptions) {
    try {
      const token = await getVoucher();
      const file = options.file;
      const key = Date.now() + file.name;
      const putExtra = {
        fname: '', //文件原文件名
        params: {}, //用来放置自定义变量
      };
      const config = {
        useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
        region: qiniu.region.z2, // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
      };
      const observable = qiniu.upload(file, key, token, putExtra, config);
      observable.subscribe({
        error: errResult => {
          getVoucher(true);
          ElMessage.error('网络错误，请稍后重试！');
          options.onError(errResult as any);
        },
        complete: ({ key }) => {
          options.onSuccess(PERSISTENT_URL + key);
        },
      });
    } catch (error) {
      options.onError(error as any);
    }
  }

  /** 获取上传凭证 */
  const getVoucher = loadingify(async (forceGet = false) => {
    let qiNiuVoucher = '';
    const voucher = useLocalStorage<IQiNiuVoucher>(QINIU_VOUCHER, {}).value;
    if (forceGet || !voucher?.expiresIn || Date.now() > voucher?.expiresIn) {
      getVoucherLoading.value = true;
      const res = await getQiNiuVoucher();
      getVoucherLoading.value = false;
      qiNiuVoucher = res;
      useLocalStorage(QINIU_VOUCHER, {}).value = {
        expiresIn: Date.now() + expiresInTime - 300,
        data: res,
      };
    } else {
      qiNiuVoucher = voucher.data!;
    }
    return qiNiuVoucher;
  });

  return {
    getVoucherLoading,
    elUploadRef,
    chooseMethod,
    handleHttpUpload,
    chooseImgVisible,
    popoverVisible,
  };
}
