<template>
  <div>
    <div v-for="item in curConfig.items" :key="item.key" class="flex mb-16">
      <div
        v-if="
          isShowBlock(item) && !(item?.uiMethod === 'file' && hiddenFileUpload)
        "
        class="form-item-row"
        :class="{ columnItem: blockLabel }"
        :style="item.uiOptions?.style"
      >
        <div class="label" :style="item.uiOptions?.labelStyle">
          {{ item.sessionName || item.value }}:
        </div>
        <div class="value" :style="item.uiOptions?.valueStyle">
          <template v-if="isShow(item, 'subform')">
            <OcrText
              v-if="item.items?.[0]?.key === 'org_text'"
              v-bind="commonProps()"
              :list="item.items?.[1]?.options"
              :title="item.value"
              :keys="[item.items[0]?.key, item.items[1]?.key || '']"
              :category="item.key as any"
              :value="formData[item.key]"
              @change="val => formItemChange(item, val)"
            />
            <NormalSubForm
              v-else
              v-bind="commonProps()"
              :items="item.items"
              :value="formData[item.key]"
              @change="val => formItemChange(item, val)"
            />
          </template>
          <TableForm
            v-else-if="isShow(item, 'table') && item.columns?.length > 0"
            v-bind="commonProps()"
            :type="item.uiOptions?.type"
            :columns="item.columns"
            :items="item.items"
            :value="formData[item.key]"
            :ui-options="item.uiOptions || {}"
            @change="val => formItemChange(item, val)"
          />
          <MixTableForm
            v-else-if="isShow(item, 'table') && item.columns?.length === 0"
            v-bind="commonProps()"
            :value="formData[item.key]"
            :items="item.items"
            @change="val => formItemChange(item, val)"
          />

          <File
            v-else-if="isShow(item, 'file')"
            v-bind="commonProps()"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
          <RadioGroup
            v-else-if="isShow(item, 'radio')"
            v-bind="commonProps()"
            :value="formData[item.key]"
            :options="item.items as any"
            :suffix-text="getSuffixText(item)"
            @change="val => formItemChange(item, val)"
            @click="val => radioClickHandler(item, val)"
          />
          <DatePicker
            v-else-if="isShow(item, 'date')"
            v-bind="commonProps()"
            :value="formData[item.key]"
            :type="item.uiOptions?.type"
            :format="item.uiRules?.dateFormat"
            :value-format="item.uiRules?.dateValueFormat"
            @change="val => formItemChange(item, val)"
          />
          <Textarea
            v-else-if="isShow(item, 'textarea')"
            v-bind="commonProps()"
            :placeholder="item.uiOptions?.placeholder"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
          <TextInput
            v-else-if="isShow(item, 'input')"
            v-bind="commonProps()"
            :placeholder="item.uiOptions?.placeholder"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
          <Select
            v-else-if="isShow(item, 'select')"
            v-bind="commonProps()"
            :placeholder="item.uiOptions?.placeholder"
            :value="formData[item.key]"
            :options="item.options"
            :class="item.uiOptions?.['class']"
            :style="item.uiOptions?.['style']"
            :multiple="item.uiRules?.['multiple']"
            @change="val => formItemChange(item, val)"
          />
          <InputNumber
            v-else-if="isShow(item, 'inputNumber')"
            v-bind="commonProps()"
            :placeholder="item.uiOptions?.placeholder"
            :value="formData[item.key]"
            :class="item.uiOptions?.['class']"
            :style="item.uiOptions?.['style']"
            :unit="item.uiOptions?.['unit']"
            :mode="mode"
            :max="item.uiRules?.max"
            :min="item.uiRules?.min"
            :step="item.uiRules?.step"
            :precision="item.uiRules?.precision"
            :control="item.uiOptions?.control"
            @change="val => formItemChange(item, val)"
          />
          <Conclusion
            v-if="isShow(item, 'conclusion')"
            v-bind="commonProps()"
            :list="item.items"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
          <ConditionRadio
            v-if="isShow(item, 'condition_radio')"
            v-bind="commonProps()"
            :list="item.items"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
          <Duration
            v-if="isShow(item, 'duration')"
            v-bind="commonProps()"
            :value="formData[item.key]"
            @change="val => formItemChange(item, val)"
          />
        </div>
      </div>
    </div>
    <ClinicalEventsDialog
      v-model:visible="evenDialogShow"
      :data="clinicalEvents"
      @change="clinicalEventChange"
    />
  </div>
</template>

<script setup lang="ts">
import ClinicalEventsDialog from '@/components/AddClinicalEvent/EventDialog.vue';
import DatePicker from '@/components/FormItem/DatePicker.vue';
import File from '@/components/FormItem/File.vue';
import InputNumber from '@/components/FormItem/InputNumber.vue';
import NormalSubForm from '@/components/FormItem/NormalSubForm.vue';
import OcrText from '@/components/FormItem/OcrText.vue';
import RadioGroup from '@/components/FormItem/RadioGroup.vue';
import Select from '@/components/FormItem/Select.vue';
import TableForm from '@/components/FormItem/TableForm.vue';
import MixTableForm from '@/components/FormItem/MixTableForm.vue';
import Textarea from '@/components/FormItem/Textarea.vue';
import TextInput from '@/components/FormItem/TextInput.vue';
import Conclusion from '@/components/FormItem/Conclusion.vue';
import ConditionRadio from '@/components/FormItem/ConditionRadio.vue';
import Duration from '@/components/FormItem/Duration.vue';
import { templateReplace } from '@/utils';
import { cloneDeep, isEmpty, isNil } from 'lodash-es';
import { Datum, DatumItem } from './types.d';
interface IProps {
  mode: 'create' | 'edit' | 'view';
  formValue?: any;
  formConfig: Datum;
  /** label是否独占一行 */
  blockLabel?: boolean;
  /** 是否隐藏文件上传组件 */
  hiddenFileUpload?: boolean;
  excludeValidateKeys?: string[];
}

const emit = defineEmits(['formdataChange']);
// 先特殊处理
const evenDialogShow = ref(false);
const clinicalEvents = ref(null);
const formData = ref({});
const props = withDefaults(defineProps<IProps>(), {
  mode: 'create',
  hiddenFileUpload: false,
});
const { excludeValidateKeys } = props;
const getSuffixText = item => {
  if (props.mode !== 'view') return '';
  if (item.key === 'admission_reason') {
    return clinicalEvents.value?.['clinicalCause'];
  }
};
const clinicalEventChange = val => {
  clinicalEvents.value = val;
  formData.value['clinical_events'] = val;
  emitChange();
};
const radioClickHandler = (item: { key: string }, val: string) => {
  if (item.key === 'admission_reason' && val === 'adverse_event') {
    evenDialogShow.value = true;
  }
};
const commonProps = () => {
  const { mode } = props;
  return {
    mode,
  };
};
const isShowBlock = item => {
  const { visibleKey, ifVisible } = item.uiOptions ?? {};
  if (visibleKey) {
    const params = { value: formData.value[visibleKey] };
    const ifCode = templateReplace(ifVisible, params);
    return eval(ifCode);
  }
  return true;
};
const isShow = (item: DatumItem, type: string) => {
  return item.uiMethod === type;
};
const parse = (str?: string | null) => {
  try {
    return str ? JSON.parse(str) : str;
  } catch (error) {
    console.log('$debug: error', error);
  }
};
const recursionParse = (list: any) => {
  for (const v of list) {
    v.uiOptions = parse(v.uiOptions);
    v.uiRules = parse(v.uiRules);
    if (v.items) {
      recursionParse(v.items);
    }
  }
};
const curConfig = computed(() => {
  const cloneConfig = cloneDeep(props.formConfig);
  cloneConfig.items = cloneConfig.items.filter(
    v => !excludeValidateKeys?.includes(v.key)
  );
  recursionParse(cloneConfig.items || []);
  return {
    ...cloneConfig,
    items: cloneConfig?.items?.sort((a, b) => a.ordered - b.ordered) ?? [],
  };
});
const formItemChange = (item, val) => {
  formData.value[item.key] = val;
  emitChange();
};

const emitChange = () => {
  emit('formdataChange', cloneDeep(formData.value));
};
const initDefaultValue = () => {
  if (!isEmpty(props.formValue)) return;
  curConfig.value.items.forEach(v => {
    if (!isNil(v.uiRules?.defaultValue)) {
      formData.value[v.key] = v.uiRules.defaultValue;
    }
  });
  emitChange();
};

watch(
  () => curConfig,
  () => {
    initDefaultValue();
  },
  { immediate: true }
);
watch(
  () => props.formValue,
  val => {
    formData.value = val || {};
    if (val?.['clinical_events']) {
      clinicalEvents.value = val['clinical_events'];
    }
  },
  { immediate: true }
);

defineOptions({
  name: 'FormList',
});
</script>

<style scoped lang="less">
.form-item-row {
  display: flex;
  width: 100%;
}
.columnItem {
  flex-direction: column;
  .label {
    text-align: left;
    margin-bottom: 4px;
  }
  .value {
    width: 98%;
  }
}
.label {
  width: 100px;
  text-align: right;
  font-weight: 700;
  font-size: 14px;
  color: #111;
  margin-right: 24px;
  padding-top: 2px;
  flex-shrink: 0;
}
.value {
  width: calc(100% - 125px);
  flex: 1;
}
</style>
