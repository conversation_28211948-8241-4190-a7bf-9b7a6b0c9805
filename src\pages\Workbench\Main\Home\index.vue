<template>
  <div class="home-content">
    <Anchor defalut-active="overview" :procedure-list="procedureList" />
    <div class="home flex-1">
      <Overview id="overview" />
      <CurrentMedications id="current-medications" />
      <Review id="review" />
      <FollowUp id="follow-up" />
      <div id="indicators-report">
        <IndicatorsReport />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Overview from './Overview/index.vue';
import CurrentMedications from './CurrentMedications/index.vue';
import Anchor from '@/pages/Workbench/Main/Home/Anchor/index.vue';
import { getHomeAnchor } from '@/api/home';
import store from '@/store';
defineOptions({ name: 'Home' });

const globalStore = store.useGlobal();

const IndicatorsReport = defineAsyncComponent(
  () => import('./IndicatorsReport/index.vue')
);

const Review = defineAsyncComponent(() => import('./Review/index.vue'));

const FollowUp = defineAsyncComponent(() => import('./FollowUp/index.vue'));

const procedureList = ref([
  {
    title: '概况',
    id: 'overview',
    des: '',
  },
  {
    title: '当前用药',
    id: 'current-medications',
    des: '',
  },
  {
    title: '复查/诊疗',
    id: 'review',
    des: '',
  },
  {
    title: '随访记录',
    id: 'follow-up',
    des: '',
  },
  {
    title: '报告指标',
    id: 'indicators-report',
    des: '',
  },
]);

const getProcedureList = async () => {
  const { isDrug, isFollow, isReview, isError } = await getHomeAnchor({
    patientId: globalStore.userId!,
  });
  procedureList.value[1].des = isDrug ? '(需跟踪)' : '';
  procedureList.value[2].des = isReview ? '(周期内)' : '';
  procedureList.value[3].des = isFollow ? '(周期内)' : '';
  procedureList.value[4].des = isError ? '(有异常)' : '';
};
onMounted(() => {
  getProcedureList();
});
</script>
<style scoped lang="less">
.home-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.home {
  padding: 0 2px;
  flex: 1;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    width: 6px;
    background: #bebebe;
    border-radius: 5px;
    display: none;
  }
  &:hover::-webkit-scrollbar-thumb {
    display: block;
  }
}
</style>
<style lang="less">
.basic-tabs .el-scrollbar__view {
  height: 100%;
}
</style>
