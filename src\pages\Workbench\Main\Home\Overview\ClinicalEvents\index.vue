<template>
  <div class="flex-1 events-list">
    <div class="time-row">
      <div class="item-list">
        <div v-for="(item, index) in props.eventList" :key="index" class="item">
          <div class="top">
            <div class="flex-c">
              <div class="round"></div>
              <span class="review-num">
                {{
                  item.clinicalTime
                    ? dayjs(item.clinicalTime).format('YYYY-MM-DD')
                    : '--'
                }}
              </span>
            </div>
            <div class="operation flex-c">
              <el-button link @click="deleteEventItem(item)">刪除</el-button>
              <el-button
                link
                @click="
                  ((currChooseEvent = item), (clinicalEventVisible = true))
                "
              >
                编辑
              </el-button>
            </div>
          </div>
          <div class="bottom">
            <div class="bottom-main">
              <div class="item-content">
                <div class="event-name flex-bc">
                  {{ clinicalTypes[item.clinicalType] }}

                  <div class="operator">
                    {{ item.userName }}&nbsp;
                    {{
                      item.modifyTime
                        ? dayjs(item.modifyTime).format('YYYY-MM-DD HH:mm')
                        : ''
                    }}
                  </div>
                </div>
                <div class="event-reason">
                  原因：{{ item.clinicalCause || '--' }}
                </div>
                <div v-if="item.accessory.length" class="event-reason">
                  附件：
                </div>
                <div class="img-box">
                  <div
                    v-for="(img, i) in item.accessory"
                    :key="i"
                    class="img-wrapper"
                  >
                    <ImgPreview
                      class="upload-image"
                      :width="80"
                      :url="img"
                      :type="item.modifyTime"
                      :show-status="false"
                      fixed
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <HrtDialog v-model="clinicalEventVisible" :width="600" title="临床事件">
    <AddClinicalEvent
      ref="refAddClinicalEvent"
      v-model:visible="clinicalEventVisible"
      :user-id="global.userId!"
      :curr-choose-event="currChooseEvent"
    />
    <template #footer>
      <div class="btn-box">
        <div class="cancel-btn" @click="clinicalEventVisible = false">取消</div>
        <div class="sure-btn" @click="sureBtnVisible">确定</div>
      </div>
    </template>
  </HrtDialog>
</template>

<script setup lang="ts">
import AddClinicalEvent from '@/components/AddClinicalEvent/index.vue';
import { HrtDialog } from '@hrt/components';
import dayjs from 'dayjs';
import { deleteEvent, saveEvent } from '@/api/event';
import bus from '../hooks/bus';
import { debounce } from 'lodash-es';
import useGlobal from '@/store/module/useGlobal';
import ImgPreview from '@/components/ImgPreview/index.vue';

interface IProps {
  eventList?: any;
  clinicalTypes?: any;
}
const props = defineProps<IProps>();
const currChooseEvent = ref({});

const clinicalEventVisible = ref(false);
const global = useGlobal();
const refAddClinicalEvent = ref();

const sureBtnVisible = debounce(() => {
  // refAddClinicalEvent.value.onClose();
  let params = refAddClinicalEvent.value.onClose().params;
  if (params) {
    ElMessageBox.confirm('确定提交内容并保存?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      draggable: true,
    })
      .then(() => {
        if (currChooseEvent.value.clinicalId) {
          params.clinicalId = currChooseEvent.value.clinicalId;
        }
        params.clinicalTime = dayjs(params.clinicalTime).format('YYYY-MM-DD');
        saveEvent(params).then(() => {
          clinicalEventVisible.value = false;
          //保存事件成功后， 刷新患者临床事件信息
          bus.emit('refresh-patient-Info-event');
          ElMessage({
            type: 'success',
            message: '保存成功！',
          });
        });
      })
      .catch(() => {});
  }
}, 600);
const deleteEventItem = item => {
  ElMessageBox.confirm('事件删除后无法找回!', '是否删除该临床事件', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
    draggable: true,
  })
    .then(() => {
      deleteEvent({ clinicalId: item.clinicalId })
        .then(() => {
          ElMessage.success('删除成功!');
          // //修改事件成功后， 刷新患者事件信息
          bus.emit('refresh-patient-Info-event');
        })
        .catch(() => {
          ElMessage.error('删除失败!');
        });
    })
    .catch(() => {});
};
</script>
<script lang="ts">
export default {
  name: 'ClinicalEvents',
};
</script>
<style scoped lang="less">
// todo
.events-list {
  padding: 0 16px;
}
.time-row {
  box-sizing: border-box;
  .item-list {
    margin-top: 16px;
    .item {
      margin-bottom: 8px;
      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .round {
          width: 11px;
          height: 11px;
          background: #daedff;
          border: 1px solid #0a73e4;
          border-radius: 50%;
          margin-right: 16px;
        }
        .review-num {
          font-size: 16px;
          color: #3a4762;
        }
      }
      .bottom {
        border-left: 1px dashed #0a73e4;
        margin-left: 5px;
        margin-top: 7px;
        padding-bottom: 9px;
        cursor: pointer;
        .bottom-main {
          background: #f7f8fa;
          margin-left: 21px;
          border: 1px solid #f7f8fa;
          box-sizing: border-box;
        }
        .item-content {
          width: 100%;
          min-height: 70px;
          border-radius: 2px;
          padding: 12px;
          box-sizing: border-box;
          .event-name {
            font-size: 14px;
            font-weight: bold;
            color: #15233f;
            margin-bottom: 6px;
            .operator {
              color: #7a8599;
              font-weight: normal;
            }
          }
          .event-reason {
            font-size: 14px;
            color: #3a4762;
          }
          .img-box {
            display: flex;
            flex-wrap: wrap;
            .img-wrapper {
              margin-right: 6px;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
//.operation {
//  .el-button {
//    height: 20px;
//    padding: 0;
//    margin: 0;
//    margin-left: 16px;
//    font-size: 14px;
//    &:first-of-type {
//      color: #e63746;
//    }
//    &:last-of-type {
//      color: #2e6be6;
//    }
//  }
//}
</style>
