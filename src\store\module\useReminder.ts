import { defineStore } from 'pinia';
import STORE_NAMES from '@/constant/storeNames';
import { getReminderList, getReminderCount } from '@/api/reminder';
import dayjs from 'dayjs';
export interface UseReminderState {
  calenderTagMap: {
    [key: string]: number;
  };
  reminderList: any[];
  totals: number;
  reminderTypeMap: any;
}

export const useReminder = defineStore(STORE_NAMES.REMINDER_EVENT, {
  state: (): UseReminderState => {
    return {
      calenderTagMap: {},
      reminderList: [],
      totals: 0,
      reminderTypeMap: {},
    };
  },
  actions: {
    getReminderListFnc(data) {
      if (data.pageNum === 1) {
        this.reminderTypeMap = {};
      }
      return new Promise((resolve, reject) => {
        getReminderList(data)
          .then(res => {
            if (res.code === 'E000000') {
              res.data.backlogResponseList.forEach(item => {
                if (!this.reminderTypeMap[String(item.type) + '_']) {
                  this.reminderTypeMap[String(item.type) + '_'] = {
                    type: item.type,
                    children: [],
                  };
                }
              });
              res.data.backlogResponseList.forEach(item => {
                if (this.reminderTypeMap[String(item.type) + '_']) {
                  const index = this.reminderTypeMap[
                    String(item.type) + '_'
                  ].children.findIndex(
                    originItem => originItem.backlogId === item.backlogId
                  );
                  if (index === -1) {
                    this.reminderTypeMap[String(item.type) + '_'].children.push(
                      item
                    );
                  }
                }
              });
              if (Object.values(this.reminderTypeMap).length) {
                this.reminderList = Object.values(this.reminderTypeMap);
              } else {
                this.reminderList = [];
              }
              this.totals = res.data.totals;
              resolve(res.data);
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    getReminderCountFnc(data) {
      getReminderCount(data).then(res => {
        this.calenderTagMap = {};
        res.data.forEach(item => {
          this.calenderTagMap[dayjs(item.date).format('YYYY-MM-DD')] =
            item.count;
        });
      });
    },
  },
});
