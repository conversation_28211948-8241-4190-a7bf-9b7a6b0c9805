import { TablePageable, TableStateProps } from '../type';
import { reactive, toRefs } from 'vue';

/**
 * @description table 页面操作方法封装
 * @param {Function} api 获取表格数据 api 方法 (必传)
 * @param {Object} initParam 获取数据初始化参数 (非必传，默认为{})
 * @param {Boolean} isPageable 是否有分页 (非必传，默认为true)
 * @param {Function} dataCallBack 对后台返回的数据进行处理的方法 (非必传)
 * */
export const useTable = (
  api?: (params: any) => Promise<any>,
  initParam: object = {},
  isPageable: boolean = true,
  dataCallBack?: (data: any) => any
) => {
  const state = reactive<TableStateProps>({
    // 表格数据
    tableData: [],
    // 分页数据
    pageable: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0,
    },
    // 总参数(包含分页和查询参数)
    totalParam: {},
    loading: false,
  });
  /**
   * @description 分页查询参数
   * */
  const pageParam = computed(() => ({
    page: state.pageable.pageNum,
    pageSize: state.pageable.pageSize,
  }));

  /**
   * @description 获取表格数据
   * @return void
   * */
  const getTableList = async () => {
    if (!api) return;
    try {
      Object.assign(
        state.totalParam,
        initParam,
        isPageable ? pageParam.value : {}
      );
      state.loading = true;
      let data = await api({
        ...state.totalParam,
      });
      state.loading = false;
      dataCallBack && (data = dataCallBack(data));
      state.tableData = isPageable ? data.data : data;
      // 解构后台返回的分页数据 (如果有分页更新分页信息)
      if (isPageable) {
        const { records } = data;
        updatePageable({ total: records });
      }
    } catch (error) {
      console.log('getTableList', error);
    }
  };
  /**
   * @description 更新分页信息
   * @param {Object} pageable 后台返回的分页数据
   * @return void
   * */
  const updatePageable = (pageable: Partial<TablePageable>) => {
    Object.assign(state.pageable, pageable);
  };
  /**
   * @description 表格数据重置
   * @return void
   * */
  const reset = () => {
    state.pageable.pageNum = 1;
    getTableList();
  };

  /**
   * @description 每页条数改变
   * @param {Number} val 当前条数
   * @return void
   * */
  const handleSizeChange = (val: number) => {
    state.pageable.pageNum = 1;
    state.pageable.pageSize = val;
    getTableList();
  };

  /**
   * @description 当前页改变
   * @param {Number} val 当前页
   * @return void
   * */
  const handleCurrentChange = (val: number) => {
    state.pageable.pageNum = val;
    getTableList();
  };

  return {
    ...toRefs(state),
    getTableList,
    reset,
    handleSizeChange,
    handleCurrentChange,
  };
};
