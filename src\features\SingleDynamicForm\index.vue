<script setup lang="ts">
import { toRefs } from 'vue';
import FormList from '@/components/FormList/index.vue';
import {
  FormCategoryValues,
  FormMode,
  FormModeValues,
  VALIDATION_PASSED,
} from '@/constant';
import { useDynamicSubForm } from '@/hooks';
import { isEmpty, omit } from 'lodash-es';
import { http } from '@/network';
import { dynamicFormDataFilter, dynamicFormValidator } from '@/utils';
import { camelToUnderline } from '@/lib/transform';

defineOptions({
  name: 'SingleDynamicForm',
});

const props = withDefaults(
  defineProps<{
    /** 表单模式 */
    mode?: FormModeValues;
    /** 动态表单的key */
    category: string | FormCategoryValues;
    /** 提交表单时排除的key */
    excludeKeys?: string[];
    /** 额外参数（接口提交时携带） */
    extraData?: Record<string, any>;
    /** 查询参数（查询详情时使用） */
    queryParams?: Record<string, any>;
    /** label是否独占一行 */
    excludeValidateKeys?: string[];
    blockLabel?: boolean;
    /** 是否隐藏文件上传组件 */
    hiddenFileUpload?: boolean;
  }>(),
  {
    mode: FormMode.CREATE,
    excludeKeys: () => [],
    extraData: () => ({}),
    queryParams: () => ({}),
    hiddenFileUpload: false,
    blockLabel: false,
    excludeValidateKeys: () => [],
  }
);
const {
  mode,
  category,
  excludeKeys,
  extraData,
  queryParams,
  hiddenFileUpload,
  excludeValidateKeys,
} = toRefs(props);
defineExpose({
  submit,
  clear,
});
const { allSchema } = useDynamicSubForm(category.value);

const modelValue = defineModel<Record<string, any>>();

function handleModelChange(value: Record<string, any>) {
  modelValue.value = value;
}

async function fetchData() {
  // 如果是编辑或者查看模式并且无数据，则获取数据
  if (mode.value !== FormMode.CREATE && !modelValue.value) {
    const res: Record<string, any> = await http.get({
      url: `/api/case/history/${category.value}`,
      params: camelToUnderline(queryParams.value),
    });
    modelValue.value = {
      ...res,
      ...(res?.items ? { [category.value]: res?.items } : {}),
    };
  }
}

/**
 * 表单提交
 * businessCallEnabled: 是否开启业务调用
 */
async function submit(businessCallEnabled = true) {
  const schema = allSchema.value?.[category.value];
  if (
    !dynamicFormValidator(schema, modelValue.value, excludeValidateKeys.value)
  )
    return;
  // 如果未开启业务调用，则直接返回校验通过的标识
  if (!businessCallEnabled) return VALIDATION_PASSED;

  const filterData = dynamicFormDataFilter(schema, modelValue.value);

  // 排除不需要提交的字段
  const submitData = {
    ...omit(filterData, excludeKeys.value),
    ...extraData.value,
  };

  try {
    return await http.post<number>({
      url: `/api/case/history/${category.value}`,
      data: camelToUnderline(submitData),
    });
  } catch (error) {
    console.error(error);
  }
}

/**
 * 清空表单
 */

async function clear() {
  modelValue.value = {};
}

const showForm = computed(() => {
  return !isEmpty(allSchema.value?.[category.value]);
});

watch(
  [() => props.category, () => props.mode],
  () => {
    fetchData();
  },
  { immediate: true }
);
</script>

<template>
  <FormList
    v-if="showForm"
    :mode="mode"
    :form-config="allSchema?.[category]"
    :form-value="modelValue"
    :block-label="blockLabel"
    :hidden-file-upload="hiddenFileUpload"
    :exclude-validate-keys="excludeValidateKeys"
    @formdata-change="handleModelChange"
  />
</template>
